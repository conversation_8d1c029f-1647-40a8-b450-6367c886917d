import { nodeRegistry } from '@/generated/node-registry';

// 从自动生成的注册表中动态创建精确的节点类型
export type NodeType = keyof typeof nodeRegistry;

// 节点引脚类型
export interface NodePin {
  id: string;
  name: string;
  type: 'input' | 'output';
  dataType: 'number' | 'string' | 'boolean' | 'color' | 'any';
  value?: any;
  defaultValue?: any;
  connected?: boolean;
  connectionId?: string;
}

// 节点位置
export interface NodePosition {
  x: number;
  y: number;
}

// 节点尺寸
export interface NodeSize {
  width: number;
  height: number;
}

// 节点状态
export type NodeStatus = 'idle' | 'running' | 'success' | 'error' | 'warning';

// 节点基础属性
export interface BaseNodeData {
  id: string;
  type: NodeType; // <-- 从 string 变为精确的 NodeType
  name: string;
  icon?: string;
  color?: string;
  description?: string;
  position: NodePosition;
  size: NodeSize;
  status: NodeStatus;
  pins: NodePin[];
  properties: Record<string, any>;
  selected?: boolean;
  dragging?: boolean;
}

// 节点事件类型
export interface NodeEvent {
  type: 'click' | 'doubleClick' | 'dragStart' | 'drag' | 'dragEnd' | 'pinConnect' | 'pinDisconnect' | 'propertyChange' | 'testRun';
  nodeId: string;
  data?: any;
}

// 节点组件属性
export interface NodeComponentProps {
  node: BaseNodeData;
  onEvent: (event: NodeEvent) => void;
  onUpdateNode: (nodeId: string, updates: Partial<BaseNodeData>) => void;
}