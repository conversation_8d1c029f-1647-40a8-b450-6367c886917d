'use client';

import React, { useState, useRef, useCallback } from 'react';
import { NodeComponentProps, NodeEvent } from './node';

export interface BaseNodeComponentProps extends NodeComponentProps {
  children?: React.ReactNode;
}

export const BaseNodeComponent: React.FC<BaseNodeComponentProps> = ({
  node,
  onEvent,
  onUpdateNode,
  children,
}) => {
  const [isDragging, setIsDragging] = useState(false);
  const nodeRef = useRef<HTMLDivElement>(null);
  const animationFrameRef = useRef<number | null>(null);
  const pendingUpdateRef = useRef<{ x: number; y: number } | null>(null);
  // 使用ref来存储拖拽起始信息，避免不必要的重渲染
  const dragInfoRef = useRef<{
    startX: number;
    startY: number;
    startNodeX: number;
    startNodeY: number;
  } | null>(null);

  // 计算引脚位置
  const calculatePinPositions = () => {
    const inputPins = node.pins.filter(pin => pin.type === 'input');
    const outputPins = node.pins.filter(pin => pin.type === 'output');
    const maxPins = Math.max(inputPins.length, outputPins.length);

    // 标题栏高度
    const titleHeight = 37;
    // 引脚区域的起始Y坐标（相对于内容区）
    const pinStartY = 14; // (pinSlotHeight / 2)
    // 每个引脚的固定垂直间距
    const pinSpacing = 28;

    return {
      inputPins,
      outputPins,
      maxPins,
      titleHeight,
      pinStartY,
      pinSpacing,
    };
  };

  const { inputPins, outputPins, maxPins, titleHeight, pinStartY, pinSpacing } = calculatePinPositions();

  // 使用requestAnimationFrame节流更新
  const flushPendingUpdate = useCallback(() => {
    if (pendingUpdateRef.current) {
      onUpdateNode(node.id, { position: pendingUpdateRef.current });
      pendingUpdateRef.current = null;
    }
    animationFrameRef.current = null;
  }, [onUpdateNode, node.id]);

  const handleMouseMove = useCallback((e: MouseEvent) => {
    // 拖拽逻辑不应依赖isDragging state，而是依赖ref，以避免陈旧闭包问题
    if (!dragInfoRef.current) return;

    const deltaX = e.clientX - dragInfoRef.current.startX;
    const deltaY = e.clientY - dragInfoRef.current.startY;

    const newPosition = {
      x: dragInfoRef.current.startNodeX + deltaX,
      y: dragInfoRef.current.startNodeY + deltaY,
    };

    // 恢复直接DOM操作以实现流畅视觉效果
    if (nodeRef.current) {
      nodeRef.current.style.left = `${newPosition.x}px`;
      nodeRef.current.style.top = `${newPosition.y}px`;
    }

    // 节流状态更新
    pendingUpdateRef.current = newPosition;
    if (animationFrameRef.current === null) {
      animationFrameRef.current = requestAnimationFrame(flushPendingUpdate);
    }
  }, [flushPendingUpdate]);

  const handleMouseUp = useCallback(() => {
    setIsDragging(false);
    dragInfoRef.current = null; // 清理拖拽信息

    // 清理动画帧
    if (animationFrameRef.current) {
      cancelAnimationFrame(animationFrameRef.current);
      animationFrameRef.current = null;
    }

    // 确保最后的位置更新被应用
    if (pendingUpdateRef.current) {
      onUpdateNode(node.id, { position: pendingUpdateRef.current });
      pendingUpdateRef.current = null;
    }

    onEvent({
      type: 'dragEnd',
      nodeId: node.id,
    });

    // 移除全局鼠标事件监听
    document.removeEventListener('mousemove', handleMouseMove);
    document.removeEventListener('mouseup', handleMouseUp);
  }, [handleMouseMove, onEvent, onUpdateNode, node.id]);

  const handleMouseDown = (e: React.MouseEvent) => {
    if (e.button !== 0) return; // 只处理左键

    // 防止在引脚上开始拖拽
    const target = e.target as HTMLElement;
    if (target.closest('.pin-connector')) {
      return;
    }

    e.preventDefault();
    e.stopPropagation();

    // 记录拖拽起始信息
    dragInfoRef.current = {
      startX: e.clientX,
      startY: e.clientY,
      startNodeX: node.position.x,
      startNodeY: node.position.y,
    };
    setIsDragging(true);

    onEvent({
      type: 'dragStart',
      nodeId: node.id,
      data: { position: node.position },
    });

    // 添加全局鼠标事件监听
    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
  };

  const handleClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    onEvent({
      type: 'click',
      nodeId: node.id,
    });
  };

  const handleDoubleClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    onEvent({
      type: 'doubleClick',
      nodeId: node.id,
    });
  };

  const getStatusColor = () => {
    switch (node.status) {
      case 'running':
        return 'border-yellow-400 bg-yellow-50';
      case 'success':
        return 'border-green-400 bg-green-50';
      case 'error':
        return 'border-red-400 bg-red-50';
      case 'warning':
        return 'border-orange-400 bg-orange-50';
      default:
        return 'border-gray-300 bg-white';
    }
  };

  const getTitleBarColorClasses = (color: string = 'gray') => {
    const colorMap: Record<string, string> = {
      gray: 'bg-gray-100 border-gray-200',
      red: 'bg-red-100 border-red-200',
      yellow: 'bg-yellow-100 border-yellow-200',
      green: 'bg-green-100 border-green-200',
      blue: 'bg-blue-100 border-blue-200',
      purple: 'bg-purple-100 border-purple-200',
    };
    return colorMap[color] || colorMap.gray;
  }

  return (
    <div
      ref={nodeRef}
      className={`absolute border-2 rounded-lg shadow-md cursor-move select-none ${
        getStatusColor()
      } ${node.selected ? 'ring-2 ring-blue-400' : ''} ${
        isDragging ? 'shadow-lg' : ''
      }`}
      style={{
        left: node.position.x,
        top: node.position.y,
        width: node.size.width,
        height: node.size.height,
      }}
      onMouseDown={handleMouseDown}
      onClick={handleClick}
      onDoubleClick={handleDoubleClick}
    >
      {/* 节点标题栏 */}
      <div className={`flex items-center justify-between px-2 py-2 border-b rounded-t-md ${getTitleBarColorClasses(node.color)}`}>
        <div className="flex items-center gap-2 flex-1 min-w-0">
          <span className="text-lg">{node.icon}</span>
          <div className="text-sm font-medium text-gray-800 truncate">
            {node.name}
          </div>
        </div>
        <button
          onClick={(e) => {
            e.stopPropagation();
            onEvent({ type: 'testRun', nodeId: node.id });
          }}
          className="p-1 rounded-full text-gray-400 hover:bg-gray-200 hover:text-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-300"
          title="测试运行节点"
        >
          <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clipRule="evenodd" />
          </svg>
        </button>
      </div>

      {/* 节点内容区域 */}
      <div className="p-2 flex-1">
        {children}
      </div>

      {/* 输入引脚 */}
      <div className="absolute left-0" style={{ top: titleHeight }}>
        {inputPins.map((pin, index) => {
          const pinY = pinStartY + (index * pinSpacing);
          return (
            <div
              key={pin.id}
              className="absolute flex items-center"
              style={{
                top: pinY,
                left: -6, // 引脚圆点的一半在节点外
                transform: 'translateY(-50%)' // 垂直居中对齐
              }}
            >
              <div
                className={`pin-connector w-3 h-3 rounded-full border-2 cursor-pointer transition-colors ${
                  pin.connected
                    ? 'bg-blue-500 border-blue-600'
                    : 'bg-white border-gray-400 hover:border-blue-400'
                }`}
                title={pin.name}
              />
              <span className="absolute left-4 top-1/2 transform -translate-y-1/2 text-xs text-gray-600 whitespace-nowrap">
                {pin.name}
              </span>
            </div>
          );
        })}
      </div>

      {/* 输出引脚 */}
      <div className="absolute right-0" style={{ top: titleHeight }}>
        {outputPins.map((pin, index) => {
          const pinY = pinStartY + (index * pinSpacing);
          return (
            <div
              key={pin.id}
              className="absolute flex items-center justify-end"
              style={{
                top: pinY,
                right: -6, // 引脚圆点的一半在节点外
                transform: 'translateY(-50%)' // 垂直居中对齐
              }}
            >
              <span className="absolute right-4 top-1/2 transform -translate-y-1/2 text-xs text-gray-600 whitespace-nowrap">
                {pin.name}
              </span>
              <div
                className={`pin-connector w-3 h-3 rounded-full border-2 cursor-pointer transition-colors ${
                  pin.connected
                    ? 'bg-green-500 border-green-600'
                    : 'bg-white border-gray-400 hover:border-green-400'
                }`}
                title={pin.name}
              />
            </div>
          );
        })}
      </div>
    </div>
  );
};