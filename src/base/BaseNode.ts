import { BaseNodeData, NodePin, NodeEvent, NodeStatus, NodeComponentProps } from './node';
import React from 'react';

export abstract class BaseNode {
  protected data: BaseNodeData;

  constructor(data: Pick<BaseNodeData, 'type'> & Partial<BaseNodeData>) {
    this.data = {
      id: data.id || this.generateId(),
      type: data.type,
      name: data.name || 'Base Node',
      icon: data.icon || '❓',
      color: data.color || 'gray',
      description: data.description || '',
      position: data.position || { x: 0, y: 0 },
      size: data.size || { width: 200, height: 100 },
      status: data.status || 'idle',
      pins: data.pins || [],
      properties: data.properties || {},
      selected: data.selected || false,
      dragging: data.dragging || false,
    };
  }

  // --- Static method for property panel rendering ---
  public static renderProperties?(props: NodeComponentProps): React.ReactNode {
    return null; // Default implementation
  }

  // --- Core Methods ---
  private generateId(): string {
    return `node_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  public getData(): BaseNodeData {
    return { ...this.data };
  }

  public updateData(updates: Partial<BaseNodeData>): void {
    this.data = { ...this.data, ...updates };
  }

  public setStatus(status: NodeStatus): void {
    this.data.status = status;
  }

  public addPin(pin: NodePin): void {
    this.data.pins.push(pin);
  }

  public removePin(pinId: string): void {
    this.data.pins = this.data.pins.filter(pin => pin.id !== pinId);
  }

  public getPin(pinId: string): NodePin | undefined {
    return this.data.pins.find(pin => pin.id === pinId);
  }

  public setPinValue(pinId: string, value: any): void {
    const pin = this.getPin(pinId);
    if (pin) {
      pin.value = value;
      this.onPinValueChanged(pinId, value);
    }
  }

  public getPinValue(pinId: string): any {
    const pin = this.getPin(pinId);
    return pin?.value;
  }
  
  public getInputValue(pinId: string): any {
    const pin = this.getPin(pinId);
    if (!pin || pin.type !== 'input') {
      return undefined;
    }
    if (pin.connected) {
      return pin.value;
    }
    return pin.defaultValue ?? pin.value;
  }

  public setProperty(key: string, value: any): void {
    this.data.properties[key] = value;
    this.onPropertyChanged(key, value);
  }

  public getProperty(key: string): any {
    return this.data.properties[key];
  }

  public handleEvent(event: NodeEvent): void {
    switch (event.type) {
      case 'click': this.onClick(event); break;
      case 'doubleClick': this.onDoubleClick(event); break;
      case 'dragStart': this.onDragStart(event); break;
      case 'drag': this.onDrag(event); break;
      case 'dragEnd': this.onDragEnd(event); break;
      case 'pinConnect': this.onPinConnect(event); break;
      case 'pinDisconnect': this.onPinDisconnect(event); break;
      case 'propertyChange':
        if (event.data) {
          this.onPropertyChanged(event.data.key, event.data.value);
        }
        break;
      case 'testRun': this.process(); break;
    }
  }

  public abstract process(): Promise<void>;

  // --- Event Handlers (for overriding) ---
  protected onClick(event: NodeEvent): void {}
  protected onDoubleClick(event: NodeEvent): void {}
  protected onDragStart(event: NodeEvent): void { this.data.dragging = true; }
  protected onDrag(event: NodeEvent): void { if (event.data && this.data.dragging) { this.data.position = event.data.position; } }
  protected onDragEnd(event: NodeEvent): void { this.data.dragging = false; }
  protected onPinConnect(event: NodeEvent): void {}
  protected onPinDisconnect(event: NodeEvent): void {}
  protected onPropertyChanged(key: string, value: any): void {
    this.updatePinsConfiguration();
  }
  
  protected onPinValueChanged(pinId: string, value: any): void {
    const pin = this.getPin(pinId);
    if (pin && pin.type === 'input') {
      this.process();
    }
  }


  protected updatePinsConfiguration(): boolean {
    return this.updateNodeSize();
  }

  protected updateNodeSize(): boolean {
    const inputPins = this.data.pins.filter(pin => pin.type === 'input');
    const outputPins = this.data.pins.filter(pin => pin.type === 'output');
    const maxPins = Math.max(inputPins.length, outputPins.length);

    const titleHeight = 37;
    const minContentHeight = 40;
    const pinPadding = 20; 
    const pinSlotHeight = 28;

    let contentHeight = minContentHeight;
    if (maxPins > 0) {
      const requiredPinsHeight = maxPins * pinSlotHeight + pinPadding;
      contentHeight = Math.max(minContentHeight, requiredPinsHeight);
    }
    
    // For nodes with dynamic sizing enabled or when pins change, always update height
    // Only skip auto-sizing if explicitly disabled AND no pin configuration changes
    if (this.data.size.width && this.data.size.height && this.data.properties.dynamicSize === false) {
        // Even for fixed-size nodes, allow adjustment if pins overflow or underflow
        const requiredHeight = titleHeight + contentHeight;
        if (this.data.size.height !== requiredHeight) {
            this.data.size.height = requiredHeight;
            return true;
        }
        return false;
    }


    const newHeight = titleHeight + contentHeight;
    if (this.data.size.height !== newHeight) {
      console.log(`节点 ${this.data.name} 高度变化: ${this.data.size.height} -> ${newHeight} (引脚数: ${maxPins})`);
      this.data.size.height = newHeight;
      return true;
    }
    return false;
  }
}