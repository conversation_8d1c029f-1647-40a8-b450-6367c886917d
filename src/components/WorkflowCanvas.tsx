'use client';

import React, { useState, useRef, useCallback, lazy, Suspense } from 'react';
import { BaseNodeData, NodeEvent, NodeType } from '@/base/node';
import { BaseNode } from '@/base/BaseNode';
import { PropertyPanel } from './PropertyPanel';
import { nodeRegistry } from '@/generated/node-registry';

const nodeComponents = Object.keys(nodeRegistry).reduce((acc, key) => {
  const type = key as NodeType;
  const { category, nodeName } = nodeRegistry[type];
  const componentName = `${nodeName}Component`;
  acc[type] = lazy(() =>
    import(`../nodes/${category}/${componentName}.tsx`).then(module => ({
      default: module[componentName],
    }))
  );
  return acc;
}, {} as Record<NodeType, React.ComponentType<any>>);

export const WorkflowCanvas: React.FC = () => {
  const [nodes, setNodes] = useState<BaseNodeData[]>([]);
  const [selectedNode, setSelectedNode] = useState<BaseNodeData | null>(null);
  const [showPropertyPanel, setShowPropertyPanel] = useState(false);
  const canvasRef = useRef<HTMLDivElement>(null);
  const nodeInstancesRef = useRef<Map<string, BaseNode>>(new Map());

  const createNode = useCallback(async (nodeType: NodeType, position: { x: number; y: number }) => {
    const nodeInfo = nodeRegistry[nodeType];
    try {
      const { category, nodeName } = nodeInfo;
      const module = await import(`../nodes/${category}/${nodeName}.tsx`);
      const NodeClass = module[nodeName];
      const instance = new NodeClass({ position });
      const nodeData = instance.getData();
      setNodes(prev => [...prev, nodeData]);
      nodeInstancesRef.current.set(nodeData.id, instance);
    } catch (error) {
      console.error(`Error creating node of type "${nodeType}":`, error);
    }
  }, []);

  const updateNode = useCallback((nodeId: string, updates: Partial<BaseNodeData>) => {
    const nodeInstance = nodeInstancesRef.current.get(nodeId);
    if (!nodeInstance) return;

    nodeInstance.updateData(updates);
    if (updates.properties) {
      Object.entries(updates.properties).forEach(([key, value]) => {
        nodeInstance.setProperty(key, value);
      });
    }

    const finalNodeData = nodeInstance.getData();

    setNodes(prev => {
      const nodeIndex = prev.findIndex(node => node.id === nodeId);
      if (nodeIndex === -1) return prev;
      if (JSON.stringify(prev[nodeIndex]) === JSON.stringify(finalNodeData)) return prev;
      const newNodes = [...prev];
      newNodes[nodeIndex] = finalNodeData;
      return newNodes;
    });

    if (selectedNode?.id === nodeId) {
      setSelectedNode(finalNodeData);
    }
  }, [selectedNode]);

  const handleNodeEvent = useCallback((event: NodeEvent) => {
    const nodeInstance = nodeInstancesRef.current.get(event.nodeId);
    if (!nodeInstance) return;

    nodeInstance.handleEvent(event);

    if (event.type === 'testRun') {
      nodeInstance.process().then(() => {
        const { status, pins, properties } = nodeInstance.getData();
        updateNode(event.nodeId, { status, pins, properties });
      });
    } else if (event.type === 'click') {
      const clickedNode = nodes.find(n => n.id === event.nodeId);
      if (clickedNode) {
        setNodes(prev => prev.map(node => ({ ...node, selected: node.id === event.nodeId })));
        setSelectedNode(clickedNode);
      }
    } else if (event.type === 'doubleClick') {
        const doubleClickedNode = nodes.find(n => n.id === event.nodeId);
        if (doubleClickedNode) {
          setSelectedNode(doubleClickedNode);
          setShowPropertyPanel(true);
        }
    }
  }, [nodes, updateNode]);

  const handleCanvasClick = useCallback((e: React.MouseEvent) => {
    if (e.target === canvasRef.current) {
      setNodes(prev => prev.map(node => ({ ...node, selected: false })));
      setSelectedNode(null);
    }
  }, []);

  const renderNode = (node: BaseNodeData) => {
    const NodeComponent = nodeComponents[node.type];
    if (!NodeComponent) return null;
    return (
      <Suspense key={node.id} fallback={<div style={{ left: node.position.x, top: node.position.y }} className="absolute p-4 bg-gray-200 rounded animate-pulse">Loading...</div>}>
        <NodeComponent
          node={node}
          onEvent={handleNodeEvent}
          onUpdateNode={updateNode}
        />
      </Suspense>
    );
  };

  return (
    <div className="relative w-full h-screen bg-gray-50 overflow-hidden">
      <div className="absolute top-4 left-4 z-10 bg-white rounded-lg shadow-md p-2 flex flex-wrap gap-2">
        {Object.entries(nodeRegistry).map(([type, { name }]) => (
          <button
            key={type}
            onClick={() => createNode(type as NodeType, { x: 100, y: 100 })}
            className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
          >
            添加 {name}
          </button>
        ))}
        <button
          onClick={() => {
            setNodes([]);
            nodeInstancesRef.current.clear();
            setSelectedNode(null);
            setShowPropertyPanel(false);
          }}
          className="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600 transition-colors"
        >
          清空画布
        </button>
      </div>

      <div className="absolute top-4 right-4 z-10 bg-white rounded-lg shadow-md p-2">
        <div className="text-sm text-gray-600">
          节点数量: {nodes.length}
          {selectedNode && <div className="mt-1">选中: {selectedNode.name}</div>}
        </div>
      </div>

      <div ref={canvasRef} className="w-full h-full relative" onClick={handleCanvasClick}>
        <div
          className="absolute inset-0 opacity-20"
          style={{
            backgroundImage: `linear-gradient(to right, #e5e7eb 1px, transparent 1px), linear-gradient(to bottom, #e5e7eb 1px, transparent 1px)`,
            backgroundSize: '20px 20px',
          }}
        />
        {nodes.map(renderNode)}
        {nodes.length === 0 && (
          <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
            <div className="text-gray-400 text-center">
              <div className="text-lg mb-2">工作流画布</div>
              <div className="text-sm">点击工具栏按钮添加节点</div>
            </div>
          </div>
        )}
      </div>

      {showPropertyPanel && (
        <PropertyPanel
          node={selectedNode}
          nodeInstance={selectedNode ? nodeInstancesRef.current.get(selectedNode.id) : undefined}
          onUpdateNode={updateNode}
          onClose={() => setShowPropertyPanel(false)}
        />
      )}
    </div>
  );
};
