'use client';

import React, { useState, useRef, useCallback } from 'react';
import { BaseNodeData, NodeEvent } from '@/types/node';
import { LedNode } from '@/nodes/LedNode';
import { LedNodeComponent } from './nodes/LedNodeComponent';
import { PropertyPanel } from './PropertyPanel';

export const WorkflowCanvas: React.FC = () => {
  const [nodes, setNodes] = useState<BaseNodeData[]>([]);
  const [selectedNode, setSelectedNode] = useState<BaseNodeData | null>(null);
  const [showPropertyPanel, setShowPropertyPanel] = useState(false);
  const canvasRef = useRef<HTMLDivElement>(null);
  const nodeInstancesRef = useRef<Map<string, LedNode>>(new Map());

  // 创建新的LED节点
  const createLedNode = useCallback((position: { x: number; y: number }) => {
    const ledNode = new LedNode({
      position,
    });

    const nodeData = ledNode.getData();
    setNodes(prev => [...prev, nodeData]);
    nodeInstancesRef.current.set(nodeData.id, ledNode);

    return nodeData;
  }, []);

  // 更新节点数据
  const updateNode = useCallback((nodeId: string, updates: Partial<BaseNodeData>) => {
    // 使用函数式更新减少重新渲染
    setNodes(prev => {
      const nodeIndex = prev.findIndex(node => node.id === nodeId);
      if (nodeIndex === -1) return prev;

      const currentNode = prev[nodeIndex];
      const updatedNode = { ...currentNode, ...updates };

      // 检查是否真的有变化，避免不必要的更新
      if (JSON.stringify(currentNode) === JSON.stringify(updatedNode)) {
        return prev;
      }

      // 更新节点实例
      const nodeInstance = nodeInstancesRef.current.get(nodeId);
      if (nodeInstance) {
        nodeInstance.updateData(updatedNode);

        // 如果属性发生变化，触发处理
        if (updates.properties) {
          Object.entries(updates.properties).forEach(([key, value]) => {
            nodeInstance.setProperty(key, value);
          });

          // 获取更新后的节点数据（可能包含新的尺寸）
          const updatedNodeData = nodeInstance.getData();
          if (updatedNodeData.size.height !== updatedNode.size.height ||
              updatedNodeData.size.width !== updatedNode.size.width) {
            console.log('检测到节点尺寸变化，更新React状态');
            updatedNode.size = updatedNodeData.size;
          }
        }
      }

      // 创建新数组，只更新变化的节点
      const newNodes = [...prev];
      newNodes[nodeIndex] = updatedNode;
      return newNodes;
    });

    // 如果更新的是当前选中的节点，也更新选中状态
    if (selectedNode?.id === nodeId) {
      setSelectedNode(prev => prev ? { ...prev, ...updates } : null);
    }
  }, [selectedNode]);

  // 处理节点事件
  const handleNodeEvent = useCallback((event: NodeEvent) => {
    const nodeInstance = nodeInstancesRef.current.get(event.nodeId);
    if (nodeInstance) {
      nodeInstance.handleEvent(event);
    }

    switch (event.type) {
      case 'click':
        const clickedNode = nodes.find(n => n.id === event.nodeId);
        if (clickedNode) {
          // 取消其他节点的选中状态
          setNodes(prev => prev.map(node => ({
            ...node,
            selected: node.id === event.nodeId,
          })));
          setSelectedNode(clickedNode);
        }
        break;

      case 'doubleClick':
        const doubleClickedNode = nodes.find(n => n.id === event.nodeId);
        if (doubleClickedNode) {
          setSelectedNode(doubleClickedNode);
          setShowPropertyPanel(true);
        }
        break;
    }
  }, [nodes]);

  // 处理画布点击
  const handleCanvasClick = useCallback((e: React.MouseEvent) => {
    if (e.target === canvasRef.current) {
      // 取消所有节点的选中状态
      setNodes(prev => prev.map(node => ({ ...node, selected: false })));
      setSelectedNode(null);
    }
  }, []);

  // 处理画布双击 - 创建新节点
  const handleCanvasDoubleClick = useCallback((e: React.MouseEvent) => {
    if (e.target === canvasRef.current) {
      const rect = canvasRef.current.getBoundingClientRect();
      const position = {
        x: e.clientX - rect.left - 60, // 减去节点宽度的一半
        y: e.clientY - rect.top - 40,  // 减去节点高度的一半
      };
      createLedNode(position);
    }
  }, [createLedNode]);

  // 渲染节点
  const renderNode = (node: BaseNodeData) => {
    switch (node.type) {
      case 'led':
        return (
          <LedNodeComponent
            key={node.id}
            node={node}
            onEvent={handleNodeEvent}
            onUpdateNode={updateNode}
          />
        );
      default:
        return null;
    }
  };

  return (
    <div className="relative w-full h-screen bg-gray-50 overflow-hidden">
      {/* 工具栏 */}
      <div className="absolute top-4 left-4 z-10 bg-white rounded-lg shadow-md p-2 flex space-x-2">
        <button
          onClick={() => createLedNode({ x: 100, y: 100 })}
          className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
        >
          添加LED节点
        </button>
        <button
          onClick={() => {
            setNodes([]);
            nodeInstancesRef.current.clear();
            setSelectedNode(null);
            setShowPropertyPanel(false);
          }}
          className="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600 transition-colors"
        >
          清空画布
        </button>
      </div>

      {/* 状态信息 */}
      <div className="absolute top-4 right-4 z-10 bg-white rounded-lg shadow-md p-2">
        <div className="text-sm text-gray-600">
          节点数量: {nodes.length}
          {selectedNode && (
            <div className="mt-1">
              选中: {selectedNode.name}
            </div>
          )}
        </div>
      </div>

      {/* 画布 */}
      <div
        ref={canvasRef}
        className="w-full h-full relative cursor-crosshair"
        onClick={handleCanvasClick}
        onDoubleClick={handleCanvasDoubleClick}
      >
        {/* 网格背景 */}
        <div
          className="absolute inset-0 opacity-20"
          style={{
            backgroundImage: `
              linear-gradient(to right, #e5e7eb 1px, transparent 1px),
              linear-gradient(to bottom, #e5e7eb 1px, transparent 1px)
            `,
            backgroundSize: '20px 20px',
          }}
        />

        {/* 渲染所有节点 */}
        {nodes.map(renderNode)}

        {/* 提示文本 */}
        {nodes.length === 0 && (
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="text-gray-400 text-center">
              <div className="text-lg mb-2">工作流画布</div>
              <div className="text-sm">双击画布创建LED节点，或点击工具栏按钮</div>
            </div>
          </div>
        )}
      </div>

      {/* 属性面板 */}
      {showPropertyPanel && (
        <PropertyPanel
          node={selectedNode}
          onUpdateNode={updateNode}
          onClose={() => setShowPropertyPanel(false)}
        />
      )}
    </div>
  );
};
