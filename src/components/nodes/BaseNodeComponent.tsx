'use client';

import React, { useState, useRef, useCallback } from 'react';
import { NodeComponentProps, NodeEvent } from '@/types/node';

export interface BaseNodeComponentProps extends NodeComponentProps {
  children?: React.ReactNode;
}

export const BaseNodeComponent: React.FC<BaseNodeComponentProps> = ({
  node,
  onEvent,
  onUpdateNode,
  children,
}) => {
  const [isDragging, setIsDragging] = useState(false);
  const [dragOffset, setDragOffset] = useState({ x: 0, y: 0 });
  const nodeRef = useRef<HTMLDivElement>(null);

  const handleMouseMove = useCallback((e: MouseEvent) => {
    const newPosition = {
      x: e.clientX - dragOffset.x,
      y: e.clientY - dragOffset.y,
    };

    onEvent({
      type: 'drag',
      nodeId: node.id,
      data: { position: newPosition },
    });

    onUpdateNode(node.id, { position: newPosition });
  }, [dragOffset.x, dragOffset.y, onEvent, onUpdateNode, node.id]);

  const handleMouseUp = useCallback(() => {
    setIsDragging(false);
    onEvent({
      type: 'dragEnd',
      nodeId: node.id,
    });

    // 移除全局鼠标事件监听
    document.removeEventListener('mousemove', handleMouseMove);
    document.removeEventListener('mouseup', handleMouseUp);
  }, [handleMouseMove, onEvent, node.id]);

  const handleMouseDown = (e: React.MouseEvent) => {
    if (e.button !== 0) return; // 只处理左键

    // 防止在引脚上开始拖拽
    const target = e.target as HTMLElement;
    if (target.closest('.pin-connector')) {
      return;
    }

    e.preventDefault();
    e.stopPropagation();

    const rect = nodeRef.current?.getBoundingClientRect();
    if (rect) {
      setDragOffset({
        x: e.clientX - rect.left,
        y: e.clientY - rect.top,
      });
    }

    setIsDragging(true);
    onEvent({
      type: 'dragStart',
      nodeId: node.id,
      data: { position: node.position },
    });

    // 添加全局鼠标事件监听
    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
  };

  const handleClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    onEvent({
      type: 'click',
      nodeId: node.id,
    });
  };

  const handleDoubleClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    onEvent({
      type: 'doubleClick',
      nodeId: node.id,
    });
  };

  const getStatusColor = () => {
    switch (node.status) {
      case 'running':
        return 'border-yellow-400 bg-yellow-50';
      case 'success':
        return 'border-green-400 bg-green-50';
      case 'error':
        return 'border-red-400 bg-red-50';
      case 'warning':
        return 'border-orange-400 bg-orange-50';
      default:
        return 'border-gray-300 bg-white';
    }
  };

  return (
    <div
      ref={nodeRef}
      className={`absolute border-2 rounded-lg shadow-md cursor-move select-none transition-all duration-200 ${
        getStatusColor()
      } ${node.selected ? 'ring-2 ring-blue-400' : ''} ${
        isDragging ? 'shadow-lg scale-105' : ''
      }`}
      style={{
        left: node.position.x,
        top: node.position.y,
        width: node.size.width,
        height: node.size.height,
      }}
      onMouseDown={handleMouseDown}
      onClick={handleClick}
      onDoubleClick={handleDoubleClick}
    >
      {/* 节点标题栏 */}
      <div className="px-2 py-1 bg-gray-100 border-b border-gray-200 rounded-t-md">
        <div className="text-sm font-medium text-gray-800 truncate">
          {node.name}
        </div>
        {node.description && (
          <div className="text-xs text-gray-500 truncate">
            {node.description}
          </div>
        )}
      </div>

      {/* 节点内容区域 */}
      <div className="p-2 flex-1">
        {children}
      </div>

      {/* 输入引脚 */}
      <div className="absolute left-0 top-8">
        {node.pins
          .filter(pin => pin.type === 'input')
          .map((pin, index) => (
            <div
              key={pin.id}
              className="relative flex items-center"
              style={{ top: index * 24, left: -6 }}
            >
              <div
                className={`pin-connector w-3 h-3 rounded-full border-2 cursor-pointer transition-colors ${
                  pin.connected
                    ? 'bg-blue-500 border-blue-600'
                    : 'bg-white border-gray-400 hover:border-blue-400'
                }`}
                title={pin.name}
              />
              <span className="absolute left-4 top-0 text-xs text-gray-600 whitespace-nowrap leading-3">
                {pin.name}
              </span>
            </div>
          ))}
      </div>

      {/* 输出引脚 */}
      <div className="absolute right-0 top-8">
        {node.pins
          .filter(pin => pin.type === 'output')
          .map((pin, index) => (
            <div
              key={pin.id}
              className="relative flex items-center justify-end"
              style={{ top: index * 24, right: -6 }}
            >
              <span className="absolute right-4 top-0 text-xs text-gray-600 whitespace-nowrap leading-3">
                {pin.name}
              </span>
              <div
                className={`pin-connector w-3 h-3 rounded-full border-2 cursor-pointer transition-colors ${
                  pin.connected
                    ? 'bg-green-500 border-green-600'
                    : 'bg-white border-gray-400 hover:border-green-400'
                }`}
                title={pin.name}
              />
            </div>
          ))}
      </div>
    </div>
  );
};
