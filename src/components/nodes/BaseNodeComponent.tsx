'use client';

import React, { useState, useRef, useCallback } from 'react';
import { NodeComponentProps, NodeEvent } from '@/types/node';

export interface BaseNodeComponentProps extends NodeComponentProps {
  children?: React.ReactNode;
}

export const BaseNodeComponent: React.FC<BaseNodeComponentProps> = ({
  node,
  onEvent,
  onUpdateNode,
  children,
}) => {
  const [isDragging, setIsDragging] = useState(false);
  const [dragOffset, setDragOffset] = useState({ x: 0, y: 0 });
  const nodeRef = useRef<HTMLDivElement>(null);
  const animationFrameRef = useRef<number | null>(null);
  const pendingUpdateRef = useRef<{ x: number; y: number } | null>(null);

  // 计算引脚位置
  const calculatePinPositions = () => {
    const inputPins = node.pins.filter(pin => pin.type === 'input');
    const outputPins = node.pins.filter(pin => pin.type === 'output');
    const maxPins = Math.max(inputPins.length, outputPins.length);

    // 标题栏高度：padding(16px) + 文字高度(20px) + border(1px) = 37px
    const titleHeight = 37;
    // 底部留白
    const bottomMargin = 12;
    // 引脚区域顶部留白
    const topMargin = 12;
    // 可用于引脚的高度
    const availableHeight = node.size.height - titleHeight - bottomMargin - topMargin;

    // 引脚间距，如果只有一个引脚则居中，多个引脚则均匀分布
    let pinSpacing = 0;
    if (maxPins > 1) {
      pinSpacing = availableHeight / (maxPins - 1);
    }



    return {
      inputPins,
      outputPins,
      titleHeight,
      topMargin,
      pinSpacing,
      availableHeight,
      maxPins
    };
  };

  const { inputPins, outputPins, titleHeight, topMargin, pinSpacing, maxPins } = calculatePinPositions();

  // 使用requestAnimationFrame节流更新
  const flushPendingUpdate = useCallback(() => {
    if (pendingUpdateRef.current) {
      onUpdateNode(node.id, { position: pendingUpdateRef.current });
      pendingUpdateRef.current = null;
    }
    animationFrameRef.current = null;
  }, [onUpdateNode, node.id]);

  const handleMouseMove = useCallback((e: MouseEvent) => {
    // 确保拖拽偏移量已经设置
    if (dragOffset.x === 0 && dragOffset.y === 0) return;

    const newPosition = {
      x: e.clientX - dragOffset.x,
      y: e.clientY - dragOffset.y,
    };

    // 立即更新DOM位置以获得流畅的视觉效果
    if (nodeRef.current) {
      nodeRef.current.style.left = `${newPosition.x}px`;
      nodeRef.current.style.top = `${newPosition.y}px`;
    }

    // 节流状态更新
    pendingUpdateRef.current = newPosition;
    if (animationFrameRef.current === null) {
      animationFrameRef.current = requestAnimationFrame(flushPendingUpdate);
    }
  }, [dragOffset.x, dragOffset.y, flushPendingUpdate]);

  const handleMouseUp = useCallback(() => {
    setIsDragging(false);

    // 清理动画帧
    if (animationFrameRef.current) {
      cancelAnimationFrame(animationFrameRef.current);
      animationFrameRef.current = null;
    }

    // 确保最后的位置更新被应用
    if (pendingUpdateRef.current) {
      onUpdateNode(node.id, { position: pendingUpdateRef.current });
      pendingUpdateRef.current = null;
    }

    onEvent({
      type: 'dragEnd',
      nodeId: node.id,
    });

    // 移除全局鼠标事件监听
    document.removeEventListener('mousemove', handleMouseMove);
    document.removeEventListener('mouseup', handleMouseUp);
  }, [handleMouseMove, onEvent, onUpdateNode, node.id]);

  const handleMouseDown = (e: React.MouseEvent) => {
    if (e.button !== 0) return; // 只处理左键

    // 防止在引脚上开始拖拽
    const target = e.target as HTMLElement;
    if (target.closest('.pin-connector')) {
      return;
    }

    e.preventDefault();
    e.stopPropagation();

    // 使用节点的当前位置而不是getBoundingClientRect来计算偏移量
    // 这样可以避免因为CSS变换或其他因素导致的位置偏差
    setDragOffset({
      x: e.clientX - node.position.x,
      y: e.clientY - node.position.y,
    });

    setIsDragging(true);
    onEvent({
      type: 'dragStart',
      nodeId: node.id,
      data: { position: node.position },
    });

    // 添加全局鼠标事件监听
    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
  };

  const handleClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    onEvent({
      type: 'click',
      nodeId: node.id,
    });
  };

  const handleDoubleClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    onEvent({
      type: 'doubleClick',
      nodeId: node.id,
    });
  };

  const getStatusColor = () => {
    switch (node.status) {
      case 'running':
        return 'border-yellow-400 bg-yellow-50';
      case 'success':
        return 'border-green-400 bg-green-50';
      case 'error':
        return 'border-red-400 bg-red-50';
      case 'warning':
        return 'border-orange-400 bg-orange-50';
      default:
        return 'border-gray-300 bg-white';
    }
  };

  return (
    <div
      ref={nodeRef}
      className={`absolute border-2 rounded-lg shadow-md cursor-move select-none ${
        getStatusColor()
      } ${node.selected ? 'ring-2 ring-blue-400' : ''} ${
        isDragging ? 'shadow-lg' : ''
      }`}
      style={{
        left: node.position.x,
        top: node.position.y,
        width: node.size.width,
        height: node.size.height,
      }}
      onMouseDown={handleMouseDown}
      onClick={handleClick}
      onDoubleClick={handleDoubleClick}
    >
      {/* 节点标题栏 */}
      <div className="px-2 py-2 bg-gray-100 border-b border-gray-200 rounded-t-md">
        <div className="text-sm font-medium text-gray-800 truncate">
          {node.name}
        </div>
      </div>

      {/* 节点内容区域 */}
      <div className="p-2 flex-1">
        {children}
      </div>

      {/* 输入引脚 */}
      <div className="absolute left-0" style={{ top: titleHeight + topMargin }}>
        {inputPins.map((pin, index) => {
          // 如果只有一个引脚，居中显示；多个引脚则均匀分布
          const pinY = maxPins === 1 ? 0 : index * pinSpacing;
          return (
            <div
              key={pin.id}
              className="absolute flex items-center"
              style={{
                top: pinY,
                left: -6, // 引脚圆点的一半在节点外
                transform: 'translateY(-50%)' // 垂直居中对齐
              }}
            >
              <div
                className={`pin-connector w-3 h-3 rounded-full border-2 cursor-pointer transition-colors ${
                  pin.connected
                    ? 'bg-blue-500 border-blue-600'
                    : 'bg-white border-gray-400 hover:border-blue-400'
                }`}
                title={pin.name}
              />
              <span className="absolute left-4 top-1/2 transform -translate-y-1/2 text-xs text-gray-600 whitespace-nowrap">
                {pin.name}
              </span>
            </div>
          );
        })}
      </div>

      {/* 输出引脚 */}
      <div className="absolute right-0" style={{ top: titleHeight + topMargin }}>
        {outputPins.map((pin, index) => {
          // 如果只有一个引脚，居中显示；多个引脚则均匀分布
          const pinY = maxPins === 1 ? 0 : index * pinSpacing;
          return (
            <div
              key={pin.id}
              className="absolute flex items-center justify-end"
              style={{
                top: pinY,
                right: -6, // 引脚圆点的一半在节点外
                transform: 'translateY(-50%)' // 垂直居中对齐
              }}
            >
              <span className="absolute right-4 top-1/2 transform -translate-y-1/2 text-xs text-gray-600 whitespace-nowrap">
                {pin.name}
              </span>
              <div
                className={`pin-connector w-3 h-3 rounded-full border-2 cursor-pointer transition-colors ${
                  pin.connected
                    ? 'bg-green-500 border-green-600'
                    : 'bg-white border-gray-400 hover:border-green-400'
                }`}
                title={pin.name}
              />
            </div>
          );
        })}
      </div>
    </div>
  );
};
