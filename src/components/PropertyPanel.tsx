'use client';

import React from 'react';
import { BaseNodeData } from '@/base/node';
import { BaseNode } from '@/base/BaseNode';

interface PropertyPanelProps {
  node: BaseNodeData | null;
  nodeInstance: BaseNode | undefined;
  onUpdateNode: (nodeId: string, updates: Partial<BaseNodeData>) => void;
  onClose: () => void;
}

export const PropertyPanel: React.FC<PropertyPanelProps> = ({
  node,
  nodeInstance,
  onUpdateNode,
  onClose,
}) => {
  if (!node || !nodeInstance) return null;

  const handleNameChange = (name: string) => {
    onUpdateNode(node.id, { name });
  };

  const handleDescriptionChange = (description: string) => {
    onUpdateNode(node.id, { description });
  };

  // Dynamically render properties using the static method on the node's class
  const NodeProperties = (nodeInstance.constructor as typeof BaseNode).renderProperties?.({
    node,
    onUpdateNode,
    onEvent: () => {}, // onEvent is not needed in property panel
  });

  return (
    <div className="fixed right-0 top-0 h-full w-80 bg-white shadow-lg border-l border-gray-200 z-50 overflow-y-auto">
      <div className="p-4">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-800">节点属性</h3>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* --- Generic Properties --- */}
        <div className="space-y-4 mb-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">节点名称</label>
            <input
              type="text"
              value={node.name}
              onChange={(e) => handleNameChange(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">描述</label>
            <textarea
              value={node.description || ''}
              onChange={(e) => handleDescriptionChange(e.target.value)}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">节点类型</label>
            <input
              type="text"
              value={node.type}
              disabled
              className="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-gray-500"
            />
          </div>
        </div>

        {/* --- Node-Specific Properties --- */}
        {NodeProperties && (
            <div className="border-t pt-4">
                <h4 className="text-md font-medium text-gray-800 mb-4">节点配置</h4>
                {NodeProperties}
            </div>
        )}
      </div>
    </div>
  );
};
