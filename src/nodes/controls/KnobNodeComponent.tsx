'use client';

import React from 'react';
import { BaseNodeComponent } from '@/base/BaseNodeComponent';
import { NodeComponentProps } from '@/base/node';
import { KnobNodeProperties } from './KnobNode';

export const KnobNodeComponent: React.FC<NodeComponentProps> = ({
  node,
  onEvent,
  onUpdateNode,
}) => {
  const properties = node.properties as KnobNodeProperties;

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = parseInt(e.target.value, 10);
    onUpdateNode(node.id, {
      properties: { ...properties, value: newValue },
    });
    // onPropertyChanged will trigger process, no need for testRun event
  };

  const getRangeAttributes = () => {
    switch (properties.outputRange) {
      case '0-255':
        return { min: 0, max: 255, step: 1 };
      case '0-1':
        return { min: 0, max: 1, step: 0.01 };
      case 'boolean':
        return { min: 0, max: 1, step: 1 };
      case 'percentage':
      default:
        return { min: 0, max: 100, step: 1 };
    }
  };

  const { min, max, step } = getRangeAttributes();

  return (
    <BaseNodeComponent
      node={node}
      onEvent={onEvent}
      onUpdateNode={onUpdateNode}
    >
      <div className="p-3">
        <input
          type="range"
          min={min}
          max={max}
          step={step}
          value={properties.value}
          onChange={handleChange}
          onMouseDown={(e) => e.stopPropagation()} // 阻止拖动滑块时移动整个节点
          className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
        />
        <div className="mt-1 text-center text-gray-700 font-medium">
          {properties.outputRange === 'boolean' ? (properties.value > 0 ? 'On' : 'Off') : properties.value}
        </div>
      </div>
    </BaseNodeComponent>
  );
};