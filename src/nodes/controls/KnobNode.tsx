import React from 'react';
import { BaseNode } from '../../base/BaseNode';
import { BaseNodeData, NodeComponentProps } from '../../base/node';

export interface KnobNodeProperties {
  value: number;
  outputRange: 'percentage' | '0-255' | '0-1' | 'boolean';
}

export class KnobNode extends BaseNode {
  constructor(data?: Partial<BaseNodeData>) {
    super({
      type: 'knob',
      name: '旋钮',
      icon: '🎛️',
      color: 'blue',
      description: '一个可以调节数值的旋钮',
      properties: {
        value: 50,
        outputRange: 'percentage',
        dynamicSize: false, // 旋钮节点保持固定尺寸，不根据引脚数量调整
      },
      size: { width: 160, height: 120 },
      ...data,
    });
    this.initializePins();
  }

  private initializePins(): void {
    this.updatePinsConfiguration();
  }

  protected updatePinsConfiguration(): boolean {
    const value = this.getProperty('value');
    this.data.pins = [
      { id: 'value_output', name: 'Value', type: 'output', dataType: 'number', value }
    ];
    return super.updatePinsConfiguration();
  }

  public async process(): Promise<void> {
    this.setStatus('running');
    const value = this.getProperty('value');
    this.setPinValue('value_output', value);
    this.setStatus('success');
  }

  protected onPropertyChanged(key: string, value: any): void {
    if (key === 'value' || key === 'outputRange') {
      this.process();
    }
    super.onPropertyChanged(key, value);
  }

  public static renderProperties({ node, onUpdateNode }: NodeComponentProps): React.ReactNode {
    const properties = node.properties as KnobNodeProperties;
    
    const handlePropertyChange = (key: string, value: any) => {
        onUpdateNode(node.id, { properties: { ...node.properties, [key]: value } });
    };

    return (
      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            输出范围
          </label>
          <select
            value={properties.outputRange || 'percentage'}
            onChange={(e) => handlePropertyChange('outputRange', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="percentage">百分比 (0-100)</option>
            <option value="0-255">0-255</option>
            <option value="0-1">0-1</option>
            <option value="boolean">布尔值</option>
          </select>
        </div>
      </div>
    );
  }
}