import { BaseNode } from './BaseNode';
import { BaseNodeData, NodePin } from '@/types/node';

export interface LedNodeProperties {
  colorMode: '1color' | '2color' | '3color';
  inputRange: 'percentage' | '0-255' | '0-1' | 'boolean';
  redValue: number;
  greenValue: number;
  blueValue: number;
  brightness: number;
}

export class LedNode extends BaseNode {
  constructor(data?: Partial<BaseNodeData>) {
    super({
      type: 'led',
      name: 'RGB LED',
      description: '3色变色LED灯节点',
      size: { width: 140, height: 100 }, // 初始高度，会根据引脚数量自动调整
      properties: {
        colorMode: '3color',
        inputRange: 'percentage',
        redValue: 0,
        greenValue: 0,
        blueValue: 0,
        brightness: 100,
      },
      ...data,
    });

    this.initializePins();
  }

  private initializePins(): void {
    this.updatePinsConfiguration();
  }

  protected updatePinsConfiguration(): void {
    // 清除现有引脚
    this.data.pins = [];

    const colorMode = this.getProperty('colorMode') as string;
    const inputRange = this.getProperty('inputRange') as string;

    // 根据颜色模式添加引脚
    if (colorMode === '1color' || colorMode === '2color' || colorMode === '3color') {
      this.addPin({
        id: 'red_input',
        name: 'R',
        type: 'input',
        dataType: inputRange === 'boolean' ? 'boolean' : 'number',
        value: 0,
      });
    }

    if (colorMode === '2color' || colorMode === '3color') {
      this.addPin({
        id: 'green_input',
        name: 'G',
        type: 'input',
        dataType: inputRange === 'boolean' ? 'boolean' : 'number',
        value: 0,
      });
    }

    if (colorMode === '3color') {
      this.addPin({
        id: 'blue_input',
        name: 'B',
        type: 'input',
        dataType: inputRange === 'boolean' ? 'boolean' : 'number',
        value: 0,
      });
    }

    // 添加亮度控制引脚
    this.addPin({
      id: 'brightness_input',
      name: 'Brightness',
      type: 'input',
      dataType: 'number',
      value: 100,
    });

    // 添加输出引脚
    this.addPin({
      id: 'color_output',
      name: 'Color',
      type: 'output',
      dataType: 'color',
      value: '#000000',
    });
  }

  public async process(): Promise<void> {
    try {
      this.setStatus('running');

      const colorMode = this.getProperty('colorMode') as string;
      const inputRange = this.getProperty('inputRange') as string;
      const brightness = this.getPinValue('brightness_input') || this.getProperty('brightness') || 100;

      let red = 0, green = 0, blue = 0;

      // 获取输入值
      if (colorMode === '1color' || colorMode === '2color' || colorMode === '3color') {
        red = this.normalizeInputValue(this.getPinValue('red_input') || 0, inputRange);
      }

      if (colorMode === '2color' || colorMode === '3color') {
        green = this.normalizeInputValue(this.getPinValue('green_input') || 0, inputRange);
      }

      if (colorMode === '3color') {
        blue = this.normalizeInputValue(this.getPinValue('blue_input') || 0, inputRange);
      }

      // 应用亮度
      const brightnessMultiplier = Math.max(0, Math.min(100, brightness)) / 100;
      red = Math.round(red * brightnessMultiplier);
      green = Math.round(green * brightnessMultiplier);
      blue = Math.round(blue * brightnessMultiplier);

      // 更新属性值
      this.setProperty('redValue', red);
      this.setProperty('greenValue', green);
      this.setProperty('blueValue', blue);

      // 生成颜色输出
      const colorHex = `#${red.toString(16).padStart(2, '0')}${green.toString(16).padStart(2, '0')}${blue.toString(16).padStart(2, '0')}`;
      this.setPinValue('color_output', colorHex);

      this.setStatus('success');
    } catch (error) {
      console.error('LED Node processing error:', error);
      this.setStatus('error');
    }
  }

  private normalizeInputValue(value: any, inputRange: string): number {
    if (typeof value === 'boolean') {
      return value ? 255 : 0;
    }

    const numValue = Number(value) || 0;

    switch (inputRange) {
      case 'percentage':
        return Math.round(Math.max(0, Math.min(100, numValue)) * 2.55);
      case '0-255':
        return Math.round(Math.max(0, Math.min(255, numValue)));
      case '0-1':
        return Math.round(Math.max(0, Math.min(1, numValue)) * 255);
      case 'boolean':
        return numValue > 0 ? 255 : 0;
      default:
        return Math.round(Math.max(0, Math.min(255, numValue)));
    }
  }

  protected onPropertyChanged(key: string, value: any): void {
    if (key === 'colorMode' || key === 'inputRange') {
      this.updatePinsConfiguration();
    }
    super.onPropertyChanged(key, value);
  }
}
