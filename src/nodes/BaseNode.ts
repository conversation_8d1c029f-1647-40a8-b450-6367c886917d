import { BaseNodeData, NodePin, NodeEvent, NodeStatus } from '@/types/node';

export abstract class BaseNode {
  protected data: BaseNodeData;

  constructor(data: Partial<BaseNodeData>) {
    this.data = {
      id: data.id || this.generateId(),
      type: data.type || 'base',
      name: data.name || 'Base Node',
      description: data.description || '',
      position: data.position || { x: 0, y: 0 },
      size: data.size || { width: 200, height: 100 },
      status: data.status || 'idle',
      pins: data.pins || [],
      properties: data.properties || {},
      selected: data.selected || false,
      dragging: data.dragging || false,
    };
  }

  // 生成唯一ID
  private generateId(): string {
    return `node_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  // 获取节点数据
  public getData(): BaseNodeData {
    return { ...this.data };
  }

  // 更新节点数据
  public updateData(updates: Partial<BaseNodeData>): void {
    this.data = { ...this.data, ...updates };
  }

  // 设置节点状态
  public setStatus(status: NodeStatus): void {
    this.data.status = status;
  }

  // 添加引脚
  public addPin(pin: NodePin): void {
    this.data.pins.push(pin);
  }

  // 移除引脚
  public removePin(pinId: string): void {
    this.data.pins = this.data.pins.filter(pin => pin.id !== pinId);
  }

  // 获取引脚
  public getPin(pinId: string): NodePin | undefined {
    return this.data.pins.find(pin => pin.id === pinId);
  }

  // 设置引脚值
  public setPinValue(pinId: string, value: any): void {
    const pin = this.getPin(pinId);
    if (pin) {
      pin.value = value;
      this.onPinValueChanged(pinId, value);
    }
  }

  // 获取引脚值
  public getPinValue(pinId: string): any {
    const pin = this.getPin(pinId);
    return pin?.value;
  }

  // 设置属性
  public setProperty(key: string, value: any): void {
    this.data.properties[key] = value;
    this.onPropertyChanged(key, value);
  }

  // 获取属性
  public getProperty(key: string): any {
    return this.data.properties[key];
  }

  // 处理事件
  public handleEvent(event: NodeEvent): void {
    switch (event.type) {
      case 'click':
        this.onClick(event);
        break;
      case 'doubleClick':
        this.onDoubleClick(event);
        break;
      case 'dragStart':
        this.onDragStart(event);
        break;
      case 'drag':
        this.onDrag(event);
        break;
      case 'dragEnd':
        this.onDragEnd(event);
        break;
      case 'pinConnect':
        this.onPinConnect(event);
        break;
      case 'pinDisconnect':
        this.onPinDisconnect(event);
        break;
      case 'propertyChange':
        this.onPropertyChange(event);
        break;
    }
  }

  // 抽象方法 - 子类必须实现
  public abstract process(): Promise<void>;

  // 事件处理方法 - 子类可以重写
  protected onClick(event: NodeEvent): void {}
  protected onDoubleClick(event: NodeEvent): void {}
  protected onDragStart(event: NodeEvent): void {
    this.data.dragging = true;
  }
  protected onDrag(event: NodeEvent): void {
    if (event.data && this.data.dragging) {
      this.data.position = event.data.position;
    }
  }
  protected onDragEnd(event: NodeEvent): void {
    this.data.dragging = false;
  }
  protected onPinConnect(event: NodeEvent): void {}
  protected onPinDisconnect(event: NodeEvent): void {}
  protected onPropertyChange(event: NodeEvent): void {}
  protected onPinValueChanged(pinId: string, value: any): void {
    // 当引脚值改变时触发处理
    this.process();
  }
  protected onPropertyChanged(key: string, value: any): void {
    // 当属性改变时可能需要重新配置引脚
    this.updatePinsConfiguration();
  }

  // 更新引脚配置 - 子类可以重写
  protected updatePinsConfiguration(): void {
    // 更新引脚配置后，重新计算节点高度
    this.updateNodeSize();
  }

  // 根据引脚数量计算并更新节点尺寸
  protected updateNodeSize(): void {
    const inputPins = this.data.pins.filter(pin => pin.type === 'input');
    const outputPins = this.data.pins.filter(pin => pin.type === 'output');
    const maxPins = Math.max(inputPins.length, outputPins.length);

    // 标题栏高度：29px
    const titleHeight = 29;
    // 内容区域最小高度
    const minContentHeight = 60;
    // 引脚区域上下边距
    const pinMargins = 16; // 上边距8px + 下边距8px

    // 计算引脚所需的高度
    let pinsHeight = 0;
    if (maxPins > 0) {
      // 每个引脚需要的最小间距
      const minPinSpacing = 20;
      pinsHeight = Math.max(minContentHeight - pinMargins, (maxPins - 1) * minPinSpacing + 20);
    }

    const newHeight = titleHeight + pinMargins + pinsHeight;

    // 只有当高度发生变化时才更新
    if (this.data.size.height !== newHeight) {
      this.data.size.height = newHeight;
    }
  }
}
