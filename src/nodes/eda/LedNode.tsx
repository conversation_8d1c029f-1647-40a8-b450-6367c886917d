import React from 'react';
import { BaseNode } from '../../base/BaseNode';
import { BaseNodeData, NodeComponentProps } from '../../base/node';

export interface LedNodeProperties {
  visiblePins: {
    r: boolean;
    g: boolean;
    b: boolean;
  };
  inputRange: 'percentage' | '0-255' | '0-1' | 'boolean';
  redValue: number;
  greenValue: number;
  blueValue: number;
  brightness: number;
}

export class LedNode extends BaseNode {
  constructor(data?: Partial<BaseNodeData>) {
    super({
      type: 'led',
      name: 'RGB LED',
      icon: '💡',
      color: 'yellow',
      description: '3色变色LED灯节点',
      size: { width: 140, height: 100 },
      properties: {
        visiblePins: { r: true, g: true, b: true },
        inputRange: 'percentage',
        redValue: 0,
        greenValue: 0,
        blueValue: 0,
        brightness: 100,
      },
      ...data,
    });
    this.initializePins();
  }

  private initializePins(): void {
    this.updatePinsConfiguration();
  }

  protected updatePinsConfiguration(): boolean {
    this.data.pins = [];
    const { visiblePins, inputRange } = this.getProperties();
    const dataType = inputRange === 'boolean' ? 'boolean' : 'number';
    if (visiblePins.r) this.addPin({ id: 'red_input', name: 'R', type: 'input', dataType, value: 0, defaultValue: 128 });
    if (visiblePins.g) this.addPin({ id: 'green_input', name: 'G', type: 'input', dataType, value: 0, defaultValue: 128 });
    if (visiblePins.b) this.addPin({ id: 'blue_input', name: 'B', type: 'input', dataType, value: 0, defaultValue: 128 });
    this.addPin({ id: 'brightness_input', name: 'Brightness', type: 'input', dataType: 'number', value: 100, defaultValue: 100 });
    this.addPin({ id: 'color_output', name: 'Color', type: 'output', dataType: 'color', value: '#000000' });
    return super.updatePinsConfiguration();
  }

  public async process(): Promise<void> {
    try {
      this.setStatus('running');
      const { inputRange } = this.getProperties();
      const brightness = this.getInputValue('brightness_input');
      const red = this.normalizeInputValue(this.getInputValue('red_input') || 0, inputRange);
      const green = this.normalizeInputValue(this.getInputValue('green_input') || 0, inputRange);
      const blue = this.normalizeInputValue(this.getInputValue('blue_input') || 0, inputRange);
      const brightnessMultiplier = Math.max(0, Math.min(100, brightness)) / 100;
      const finalRed = Math.round(red * brightnessMultiplier);
      const finalGreen = Math.round(green * brightnessMultiplier);
      const finalBlue = Math.round(blue * brightnessMultiplier);
      this.setProperty('redValue', finalRed);
      this.setProperty('greenValue', finalGreen);
      this.setProperty('blueValue', finalBlue);
      const colorHex = `#${finalRed.toString(16).padStart(2, '0')}${finalGreen.toString(16).padStart(2, '0')}${finalBlue.toString(16).padStart(2, '0')}`;
      this.setPinValue('color_output', colorHex);
      this.setStatus('success');
    } catch (error) {
      console.error('LED Node processing error:', error);
      this.setStatus('error');
    }
  }

  private normalizeInputValue(value: any, inputRange: string): number {
    if (typeof value === 'boolean') return value ? 255 : 0;
    const numValue = Number(value) || 0;
    switch (inputRange) {
      case 'percentage': return Math.round(Math.max(0, Math.min(100, numValue)) * 2.55);
      case '0-255': return Math.round(Math.max(0, Math.min(255, numValue)));
      case '0-1': return Math.round(Math.max(0, Math.min(1, numValue)) * 255);
      case 'boolean': return numValue > 0 ? 255 : 0;
      default: return Math.round(Math.max(0, Math.min(255, numValue)));
    }
  }


  private getProperties(): LedNodeProperties {
    return this.data.properties as LedNodeProperties;
  }

  // --- 属性面板渲染逻辑 ---
  public static renderProperties({ node, onUpdateNode }: NodeComponentProps): React.ReactNode {
    const properties = node.properties as LedNodeProperties;

    const handlePropertyChange = (key: string, value: any) => {
        onUpdateNode(node.id, { properties: { ...node.properties, [key]: value } });
    };
    
    const handlePinVisibilityChange = (pin: 'r' | 'g' | 'b', isVisible: boolean) => {
        const currentVisibility = properties.visiblePins || { r: true, g: true, b: true };
        const visibleCount = Object.values(currentVisibility).filter(v => v).length;
        if (visibleCount === 1 && !isVisible) {
            alert("至少需要一个可见的颜色引脚。");
            return;
        }
        const newVisibility = { ...currentVisibility, [pin]: isVisible };
        handlePropertyChange('visiblePins', newVisibility);
    };

    return (
      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">可见引脚</label>
          <div className="flex items-center space-x-4">
            <label className="flex items-center">
              <input type="checkbox" checked={properties.visiblePins?.r ?? true} onChange={(e) => handlePinVisibilityChange('r', e.target.checked)} className="h-4 w-4 rounded border-gray-300 text-red-600 focus:ring-red-500"/>
              <span className="ml-2 text-sm text-red-600">R</span>
            </label>
            <label className="flex items-center">
              <input type="checkbox" checked={properties.visiblePins?.g ?? true} onChange={(e) => handlePinVisibilityChange('g', e.target.checked)} className="h-4 w-4 rounded border-gray-300 text-green-600 focus:ring-green-500"/>
              <span className="ml-2 text-sm text-green-600">G</span>
            </label>
            <label className="flex items-center">
              <input type="checkbox" checked={properties.visiblePins?.b ?? true} onChange={(e) => handlePinVisibilityChange('b', e.target.checked)} className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"/>
              <span className="ml-2 text-sm text-blue-600">B</span>
            </label>
          </div>
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">输入范围</label>
          <select value={properties.inputRange || 'percentage'} onChange={(e) => handlePropertyChange('inputRange', e.target.value)} className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
            <option value="percentage">百分比 (0-100)</option>
            <option value="0-255">0-255</option>
            <option value="0-1">0-1</option>
            <option value="boolean">布尔值</option>
          </select>
        </div>
      </div>
    );
  }
}