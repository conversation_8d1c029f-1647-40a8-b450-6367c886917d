import fs from 'fs/promises';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const nodesDir = path.resolve(__dirname, '../src/nodes');
const generatedDir = path.resolve(__dirname, '../src/generated');
const registryFile = path.join(generatedDir, 'node-registry.ts');

async function generateRegistry() {
  const registry = {};
  console.log(`🔍 Scanning for nodes in: ${nodesDir}`);

  try {
    // 确保 a/generated 目录存在
    await fs.mkdir(generatedDir, { recursive: true });

    const categories = await fs.readdir(nodesDir, { withFileTypes: true });
    
    for (const category of categories) {
      if (category.isDirectory() && category.name !== 'base') {
        const categoryName = category.name;
        const categoryDir = path.join(nodesDir, categoryName);
        
        const nodeFiles = await fs.readdir(categoryDir);
        const logicFiles = nodeFiles.filter(f => f.endsWith('.tsx') && !f.endsWith('Component.tsx'));

        for (const logicFile of logicFiles) {
          const nodeName = path.basename(logicFile, '.tsx');
          const componentFile = `${nodeName}Component.tsx`;

          if (nodeFiles.includes(componentFile)) {
            const filePath = path.join(categoryDir, logicFile);
            const content = await fs.readFile(filePath, 'utf-8');

            const typeMatch = content.match(/type:\s*['"]([^'"]+)['"]/);
            const nameMatch = content.match(/name:\s*['"]([^'"]+)['"]/);

            if (typeMatch && nameMatch) {
              const type = typeMatch[1];
              const name = nameMatch[1];
              
              registry[type] = {
                name: name,
                category: categoryName,
                nodeName: nodeName,
              };
            }
          }
        }
      }
    }

    const fileContent = `// This file is auto-generated by scripts/generate-node-registry.mjs
// Do not edit this file directly.

export const nodeRegistry = ${JSON.stringify(registry, null, 2)} as const;
`;

    await fs.writeFile(registryFile, fileContent);
    console.log(`✅ Node registry generated successfully at ${registryFile}`);
    console.log(registry);

  } catch (error) {
    console.error(`❌ Error generating node registry:`);
    console.error(error);
  }
}

generateRegistry();