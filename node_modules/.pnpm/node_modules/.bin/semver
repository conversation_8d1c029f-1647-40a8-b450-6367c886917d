#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/Documents/augment-projects/nextjs-superNode/node_modules/.pnpm/semver@6.3.1/node_modules/semver/bin/node_modules:/Users/<USER>/Documents/augment-projects/nextjs-superNode/node_modules/.pnpm/semver@6.3.1/node_modules/semver/node_modules:/Users/<USER>/Documents/augment-projects/nextjs-superNode/node_modules/.pnpm/semver@6.3.1/node_modules:/Users/<USER>/Documents/augment-projects/nextjs-superNode/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/Documents/augment-projects/nextjs-superNode/node_modules/.pnpm/semver@6.3.1/node_modules/semver/bin/node_modules:/Users/<USER>/Documents/augment-projects/nextjs-superNode/node_modules/.pnpm/semver@6.3.1/node_modules/semver/node_modules:/Users/<USER>/Documents/augment-projects/nextjs-superNode/node_modules/.pnpm/semver@6.3.1/node_modules:/Users/<USER>/Documents/augment-projects/nextjs-superNode/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../semver/bin/semver.js" "$@"
else
  exec node  "$basedir/../semver/bin/semver.js" "$@"
fi
