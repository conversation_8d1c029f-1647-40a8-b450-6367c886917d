{"version": 3, "sources": ["../../../../../src/build/webpack/loaders/resolve-url-loader/index.ts"], "sourcesContent": ["/*\nThe MIT License (MIT)\n\nCopyright (c) 2016 Ben <PERSON>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n*/\n\nimport { SourceMapConsumer } from 'next/dist/compiled/source-map'\nimport valueProcessor from './lib/value-processor'\nimport { defaultJoin } from './lib/join-function'\nimport process from './lib/postcss'\n/**\n * A webpack loader that resolves absolute url() paths relative to their original source file.\n * Requires source-maps to do any meaningful work.\n */\nexport default async function resolveUrlLoader(\n  this: any,\n  /** Css content */\n  content: string,\n  /** The source-map */\n  sourceMap: any\n): Promise<void> {\n  const options = Object.assign(\n    {\n      sourceMap: this.sourceMap,\n      silent: false,\n      absolute: false,\n      keepQuery: false,\n      root: false,\n      debug: false,\n      join: defaultJoin,\n    },\n    this.getOptions()\n  )\n\n  let sourceMapConsumer\n  if (sourceMap) {\n    sourceMapConsumer = new SourceMapConsumer(sourceMap)\n  }\n\n  const callback = this.async()\n  const { postcss } = options.postcss\n    ? await options.postcss()\n    : { postcss: require('postcss') as typeof import('postcss') }\n  process(postcss, this.resourcePath, content, {\n    outputSourceMap: Boolean(options.sourceMap),\n    transformDeclaration: valueProcessor(this.resourcePath, options),\n    inputSourceMap: sourceMap,\n    sourceMapConsumer: sourceMapConsumer,\n  })\n    // eslint-disable-next-line @typescript-eslint/no-use-before-define\n    .catch(onFailure)\n    // eslint-disable-next-line @typescript-eslint/no-use-before-define\n    .then(onSuccess)\n\n  function onFailure(error: Error) {\n    // eslint-disable-next-line @typescript-eslint/no-use-before-define\n    callback(encodeError('CSS error', error))\n  }\n\n  function onSuccess(reworked: any) {\n    if (reworked) {\n      // complete with source-map\n      //  source-map sources are relative to the file being processed\n      if (options.sourceMap) {\n        callback(null, reworked.content, reworked.map)\n      }\n      // complete without source-map\n      else {\n        callback(null, reworked.content)\n      }\n    }\n  }\n\n  function encodeError(label: any, exception: any) {\n    return new Error(\n      [\n        'resolve-url-loader',\n        ': ',\n        [label]\n          .concat(\n            (typeof exception === 'string' && exception) ||\n              (exception instanceof Error && [\n                exception.message,\n                (exception as any).stack.split('\\n', 2)[1].trim(),\n              ]) ||\n              []\n          )\n          .filter(Boolean)\n          .join('\\n  '),\n      ].join('')\n    )\n  }\n}\n"], "names": ["resolveUrlLoader", "content", "sourceMap", "options", "Object", "assign", "silent", "absolute", "<PERSON><PERSON><PERSON><PERSON>", "root", "debug", "join", "defaultJoin", "getOptions", "sourceMapConsumer", "SourceMapConsumer", "callback", "async", "postcss", "require", "process", "resourcePath", "outputSourceMap", "Boolean", "transformDeclaration", "valueProcessor", "inputSourceMap", "catch", "onFailure", "then", "onSuccess", "error", "encodeError", "reworked", "map", "label", "exception", "Error", "concat", "message", "stack", "split", "trim", "filter"], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;;;;AAsBA;;;;+BAMA;;;CAGC,GACD;;;eAA8BA;;;2BARI;uEACP;8BACC;gEACR;;;;;;AAKL,eAAeA,iBAE5B,gBAAgB,GAChBC,OAAe,EACf,mBAAmB,GACnBC,SAAc;IAEd,MAAMC,UAAUC,OAAOC,MAAM,CAC3B;QACEH,WAAW,IAAI,CAACA,SAAS;QACzBI,QAAQ;QACRC,UAAU;QACVC,WAAW;QACXC,MAAM;QACNC,OAAO;QACPC,MAAMC,yBAAW;IACnB,GACA,IAAI,CAACC,UAAU;IAGjB,IAAIC;IACJ,IAAIZ,WAAW;QACbY,oBAAoB,IAAIC,4BAAiB,CAACb;IAC5C;IAEA,MAAMc,WAAW,IAAI,CAACC,KAAK;IAC3B,MAAM,EAAEC,OAAO,EAAE,GAAGf,QAAQe,OAAO,GAC/B,MAAMf,QAAQe,OAAO,KACrB;QAAEA,SAASC,QAAQ;IAAuC;IAC9DC,IAAAA,gBAAO,EAACF,SAAS,IAAI,CAACG,YAAY,EAAEpB,SAAS;QAC3CqB,iBAAiBC,QAAQpB,QAAQD,SAAS;QAC1CsB,sBAAsBC,IAAAA,uBAAc,EAAC,IAAI,CAACJ,YAAY,EAAElB;QACxDuB,gBAAgBxB;QAChBY,mBAAmBA;IACrB,EACE,mEAAmE;KAClEa,KAAK,CAACC,UACP,mEAAmE;KAClEC,IAAI,CAACC;IAER,SAASF,UAAUG,KAAY;QAC7B,mEAAmE;QACnEf,SAASgB,YAAY,aAAaD;IACpC;IAEA,SAASD,UAAUG,QAAa;QAC9B,IAAIA,UAAU;YACZ,2BAA2B;YAC3B,+DAA+D;YAC/D,IAAI9B,QAAQD,SAAS,EAAE;gBACrBc,SAAS,MAAMiB,SAAShC,OAAO,EAAEgC,SAASC,GAAG;YAC/C,OAEK;gBACHlB,SAAS,MAAMiB,SAAShC,OAAO;YACjC;QACF;IACF;IAEA,SAAS+B,YAAYG,KAAU,EAAEC,SAAc;QAC7C,OAAO,qBAgBN,CAhBM,IAAIC,MACT;YACE;YACA;YACA;gBAACF;aAAM,CACJG,MAAM,CACL,AAAC,OAAOF,cAAc,YAAYA,aAC/BA,qBAAqBC,SAAS;gBAC7BD,UAAUG,OAAO;gBAChBH,UAAkBI,KAAK,CAACC,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,CAACC,IAAI;aAChD,IACD,EAAE,EAELC,MAAM,CAACpB,SACPZ,IAAI,CAAC;SACT,CAACA,IAAI,CAAC,MAfF,qBAAA;mBAAA;wBAAA;0BAAA;QAgBP;IACF;AACF", "ignoreList": [0]}