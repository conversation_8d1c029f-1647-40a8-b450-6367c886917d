{"version": 3, "sources": ["../../../../../src/build/webpack/loaders/next-flight-loader/action-client-wrapper.ts"], "sourcesContent": ["// This file must be bundled in the app's client layer, it shouldn't be directly\n// imported by the server.\n\nexport { callServer } from 'next/dist/client/app-call-server'\nexport { findSourceMapURL } from 'next/dist/client/app-find-source-map-url'\n\n// A noop wrapper to let the Flight client create the server reference.\n// See also: https://github.com/facebook/react/pull/26632\n// eslint-disable-next-line import/no-extraneous-dependencies\nexport { createServerReference } from 'react-server-dom-webpack/client'\n"], "names": ["callServer", "createServerReference", "findSourceMapURL"], "mappings": "AAAA,gFAAgF;AAChF,0BAA0B;;;;;;;;;;;;;;;;;IAEjBA,UAAU;eAAVA,yBAAU;;IAMVC,qBAAqB;eAArBA,6BAAqB;;IALrBC,gBAAgB;eAAhBA,qCAAgB;;;+BADE;qCACM;wBAKK", "ignoreList": [0]}