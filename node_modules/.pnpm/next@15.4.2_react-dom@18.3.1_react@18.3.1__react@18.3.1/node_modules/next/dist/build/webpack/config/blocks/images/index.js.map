{"version": 3, "sources": ["../../../../../../src/build/webpack/config/blocks/images/index.ts"], "sourcesContent": ["import curry from 'next/dist/compiled/lodash.curry'\nimport type { webpack } from 'next/dist/compiled/webpack/webpack'\nimport { nextImageLoaderRegex } from '../../../../webpack-config'\nimport { loader } from '../../helpers'\nimport { pipe } from '../../utils'\nimport type { ConfigurationContext, ConfigurationFn } from '../../utils'\nimport { getCustomDocumentImageError } from './messages'\n\nexport const images = curry(async function images(\n  _ctx: ConfigurationContext,\n  config: webpack.Configuration\n) {\n  const fns: ConfigurationFn[] = [\n    loader({\n      oneOf: [\n        {\n          test: nextImageLoaderRegex,\n          use: {\n            loader: 'error-loader',\n            options: {\n              reason: getCustomDocumentImageError(),\n            },\n          },\n          issuer: /pages[\\\\/]_document\\./,\n        },\n      ],\n    }),\n  ]\n\n  const fn = pipe(...fns)\n  return fn(config)\n})\n"], "names": ["images", "curry", "_ctx", "config", "fns", "loader", "oneOf", "test", "nextImageLoaderRegex", "use", "options", "reason", "getCustomDocumentImageError", "issuer", "fn", "pipe"], "mappings": ";;;;+BAQaA;;;eAAAA;;;oEARK;+BAEmB;yBACd;uBACF;0BAEuB;;;;;;AAErC,MAAMA,SAASC,IAAAA,oBAAK,EAAC,eAAeD,OACzCE,IAA0B,EAC1BC,MAA6B;IAE7B,MAAMC,MAAyB;QAC7BC,IAAAA,eAAM,EAAC;YACLC,OAAO;gBACL;oBACEC,MAAMC,mCAAoB;oBAC1BC,KAAK;wBACHJ,QAAQ;wBACRK,SAAS;4BACPC,QAAQC,IAAAA,qCAA2B;wBACrC;oBACF;oBACAC,QAAQ;gBACV;aACD;QACH;KACD;IAED,MAAMC,KAAKC,IAAAA,WAAI,KAAIX;IACnB,OAAOU,GAAGX;AACZ", "ignoreList": [0]}