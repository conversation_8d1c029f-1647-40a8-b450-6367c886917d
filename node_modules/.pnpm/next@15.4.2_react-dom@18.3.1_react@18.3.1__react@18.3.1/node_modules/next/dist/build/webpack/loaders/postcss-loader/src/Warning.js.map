{"version": 3, "sources": ["../../../../../../src/build/webpack/loaders/postcss-loader/src/Warning.ts"], "sourcesContent": ["/**\n * **PostCSS Plugin Warning**\n *\n * Loader wrapper for postcss plugin warnings (`root.messages`)\n *\n * @class Warning\n * @extends Error\n *\n * @param {Object} warning PostCSS Warning\n */\nexport default class Warning extends Error {\n  stack: any\n  constructor(warning: any) {\n    super(warning)\n\n    const { text, line, column, plugin } = warning\n\n    this.name = 'Warning'\n\n    this.message = `${this.name}\\n\\n`\n\n    if (typeof line !== 'undefined') {\n      this.message += `(${line}:${column}) `\n    }\n\n    this.message += plugin ? `${plugin}: ` : ''\n    this.message += text\n\n    this.stack = false\n  }\n}\n"], "names": ["Warning", "Error", "constructor", "warning", "text", "line", "column", "plugin", "name", "message", "stack"], "mappings": "AAAA;;;;;;;;;CASC;;;;+BACD;;;eAAqBA;;;AAAN,MAAMA,gBAAgBC;IAEnCC,YAAYC,OAAY,CAAE;QACxB,KAAK,CAACA;QAEN,MAAM,EAAEC,IAAI,EAAEC,IAAI,EAAEC,MAAM,EAAEC,MAAM,EAAE,GAAGJ;QAEvC,IAAI,CAACK,IAAI,GAAG;QAEZ,IAAI,CAACC,OAAO,GAAG,GAAG,IAAI,CAACD,IAAI,CAAC,IAAI,CAAC;QAEjC,IAAI,OAAOH,SAAS,aAAa;YAC/B,IAAI,CAACI,OAAO,IAAI,CAAC,CAAC,EAAEJ,KAAK,CAAC,EAAEC,OAAO,EAAE,CAAC;QACxC;QAEA,IAAI,CAACG,OAAO,IAAIF,SAAS,GAAGA,OAAO,EAAE,CAAC,GAAG;QACzC,IAAI,CAACE,OAAO,IAAIL;QAEhB,IAAI,CAACM,KAAK,GAAG;IACf;AACF", "ignoreList": [0]}