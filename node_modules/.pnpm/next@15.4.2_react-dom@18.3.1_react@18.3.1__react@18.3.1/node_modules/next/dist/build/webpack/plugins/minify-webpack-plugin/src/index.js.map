{"version": 3, "sources": ["../../../../../../src/build/webpack/plugins/minify-webpack-plugin/src/index.ts"], "sourcesContent": ["import {\n  webpack,\n  ModuleFilenameHelpers,\n  sources,\n  WebpackError,\n  type CacheFacade,\n  type Compilation,\n} from 'next/dist/compiled/webpack/webpack'\nimport pLimit from 'next/dist/compiled/p-limit'\nimport { getCompilationSpan } from '../../../utils'\n\nfunction buildError(error: any, file: string) {\n  if (error.line) {\n    return new WebpackError(\n      `${file} from Minifier\\n${error.message} [${file}:${error.line},${\n        error.col\n      }]${\n        error.stack ? `\\n${error.stack.split('\\n').slice(1).join('\\n')}` : ''\n      }`\n    )\n  }\n\n  if (error.stack) {\n    return new WebpackError(\n      `${file} from Minifier\\n${error.message}\\n${error.stack}`\n    )\n  }\n\n  return new WebpackError(`${file} from Minifier\\n${error.message}`)\n}\n\nconst debugMinify = process.env.NEXT_DEBUG_MINIFY\n\nexport class MinifyPlugin {\n  constructor(\n    private options: { noMangling?: boolean; disableCharFreq?: boolean }\n  ) {}\n\n  async optimize(\n    compiler: any,\n    compilation: Compilation,\n    assets: any,\n    cache: CacheFacade,\n    {\n      SourceMapSource,\n      RawSource,\n    }: {\n      SourceMapSource: typeof sources.SourceMapSource\n      RawSource: typeof sources.RawSource\n    }\n  ) {\n    const mangle = this.options.noMangling\n      ? false\n      : {\n          reserved: ['AbortSignal'],\n          disableCharFreq: !!this.options.disableCharFreq,\n        }\n    const compilationSpan =\n      getCompilationSpan(compilation)! || getCompilationSpan(compiler)\n\n    const MinifierSpan = compilationSpan.traceChild(\n      'minify-webpack-plugin-optimize'\n    )\n\n    if (compilation.name) {\n      MinifierSpan.setAttribute('compilationName', compilation.name)\n    }\n\n    MinifierSpan.setAttribute('mangle', String(mangle))\n\n    return MinifierSpan.traceAsyncFn(async () => {\n      const assetsList = Object.keys(assets)\n\n      const assetsForMinify = await Promise.all(\n        assetsList\n          .filter((name) => {\n            if (\n              !ModuleFilenameHelpers.matchObject.bind(\n                // eslint-disable-next-line no-undefined\n                undefined,\n                { test: /\\.[cm]?js(\\?.*)?$/i }\n              )(name)\n            ) {\n              return false\n            }\n\n            const res = compilation.getAsset(name)\n            if (!res) {\n              console.log(name)\n              return false\n            }\n\n            const { info } = res\n\n            // Skip double minimize assets from child compilation\n            if (info.minimized) {\n              return false\n            }\n\n            return true\n          })\n          .map(async (name) => {\n            const { info, source } = compilation.getAsset(name)!\n\n            const eTag = cache.mergeEtags(\n              cache.getLazyHashedEtag(source),\n              JSON.stringify(this.options)\n            )\n\n            const output = await cache.getPromise<\n              { source: sources.Source } | undefined\n            >(name, eTag)\n\n            if (debugMinify && debugMinify === '1') {\n              console.log(\n                JSON.stringify({ name, source: source.source().toString() }),\n                { breakLength: Infinity, maxStringLength: Infinity }\n              )\n            }\n            return { name, info, inputSource: source, output, eTag }\n          })\n      )\n\n      let initializedWorker: any\n\n      // eslint-disable-next-line consistent-return\n      const getWorker = () => {\n        return {\n          minify: async (options: {\n            input: string\n            inputSourceMap: Object\n          }) => {\n            const result = await (\n              require('../../../../swc') as typeof import('../../../../swc')\n            ).minify(options.input, {\n              ...(options.inputSourceMap\n                ? {\n                    sourceMap: {\n                      content: JSON.stringify(options.inputSourceMap),\n                    },\n                  }\n                : {}),\n              compress: {\n                inline: 2,\n                global_defs: {\n                  'process.env.__NEXT_PRIVATE_MINIMIZE_MACRO_FALSE': false,\n                },\n                keep_classnames: this.options.noMangling,\n                keep_fnames: this.options.noMangling,\n              },\n              mangle,\n              module: 'unknown',\n              output: {\n                comments: false,\n              },\n            })\n\n            return result\n          },\n        }\n      }\n\n      // The limit in the SWC minifier will be handled by Node.js\n      const limit = pLimit(Infinity)\n      const scheduledTasks = []\n\n      for (const asset of assetsForMinify) {\n        scheduledTasks.push(\n          limit(async () => {\n            const { name, inputSource, eTag } = asset\n            let { output } = asset\n\n            const minifySpan = MinifierSpan.traceChild('minify-js')\n            minifySpan.setAttribute('name', name)\n            minifySpan.setAttribute(\n              'cache',\n              typeof output === 'undefined' ? 'MISS' : 'HIT'\n            )\n\n            return minifySpan.traceAsyncFn(async () => {\n              if (!output) {\n                const { source: sourceFromInputSource, map: inputSourceMap } =\n                  inputSource.sourceAndMap()\n\n                const input = Buffer.isBuffer(sourceFromInputSource)\n                  ? sourceFromInputSource.toString()\n                  : sourceFromInputSource\n\n                let minifiedOutput: { code: string; map: any } | undefined\n\n                try {\n                  minifiedOutput = await getWorker().minify({\n                    input,\n                    inputSourceMap,\n                  })\n                } catch (error) {\n                  compilation.errors.push(buildError(error, name))\n\n                  return\n                }\n\n                const source = minifiedOutput.map\n                  ? new SourceMapSource(\n                      minifiedOutput.code,\n                      name,\n                      minifiedOutput.map,\n                      input,\n                      inputSourceMap,\n                      true\n                    )\n                  : new RawSource(minifiedOutput.code)\n\n                await cache.storePromise(name, eTag, { source })\n\n                output = { source }\n              }\n\n              const newInfo = { minimized: true }\n\n              compilation.updateAsset(name, output.source, newInfo)\n            })\n          })\n        )\n      }\n\n      await Promise.all(scheduledTasks)\n\n      if (initializedWorker) {\n        await initializedWorker.end()\n      }\n    })\n  }\n\n  apply(compiler: any) {\n    const { SourceMapSource, RawSource } = (compiler?.webpack?.sources ||\n      sources) as typeof sources\n\n    const pluginName = this.constructor.name\n\n    compiler.hooks.thisCompilation.tap(\n      pluginName,\n      (compilation: Compilation) => {\n        const cache = compilation.getCache('MinifierWebpackPlugin')\n\n        const handleHashForChunk = (hash: any, _chunk: any) => {\n          // increment 'c' to invalidate cache\n          hash.update('c')\n        }\n\n        const JSModulesHooks =\n          webpack.javascript.JavascriptModulesPlugin.getCompilationHooks(\n            compilation\n          )\n        JSModulesHooks.chunkHash.tap(pluginName, (chunk, hash) => {\n          if (!chunk.hasRuntime()) return\n          return handleHashForChunk(hash, chunk)\n        })\n\n        compilation.hooks.processAssets.tapPromise(\n          {\n            name: pluginName,\n            stage: webpack.Compilation.PROCESS_ASSETS_STAGE_OPTIMIZE_SIZE,\n          },\n          (assets: any) =>\n            this.optimize(compiler, compilation, assets, cache, {\n              SourceMapSource,\n              RawSource,\n            })\n        )\n\n        compilation.hooks.statsPrinter.tap(pluginName, (stats: any) => {\n          stats.hooks.print\n            .for('asset.info.minimized')\n            .tap(\n              'minify-webpack-plugin',\n              (minimized: any, { green, formatFlag }: any) =>\n                // eslint-disable-next-line no-undefined\n                minimized ? green(formatFlag('minimized')) : undefined\n            )\n        })\n      }\n    )\n  }\n}\n"], "names": ["MinifyPlugin", "buildError", "error", "file", "line", "WebpackError", "message", "col", "stack", "split", "slice", "join", "debugMinify", "process", "env", "NEXT_DEBUG_MINIFY", "constructor", "options", "optimize", "compiler", "compilation", "assets", "cache", "SourceMapSource", "RawSource", "mangle", "noMangling", "reserved", "disableCharFreq", "compilationSpan", "getCompilationSpan", "MinifierSpan", "<PERSON><PERSON><PERSON><PERSON>", "name", "setAttribute", "String", "traceAsyncFn", "assetsList", "Object", "keys", "assetsForMinify", "Promise", "all", "filter", "ModuleFilenameHelpers", "matchObject", "bind", "undefined", "test", "res", "getAsset", "console", "log", "info", "minimized", "map", "source", "eTag", "mergeEtags", "getLazyHashedEtag", "JSON", "stringify", "output", "getPromise", "toString", "<PERSON><PERSON><PERSON><PERSON>", "Infinity", "max<PERSON><PERSON><PERSON><PERSON><PERSON>", "inputSource", "initializedWorker", "getWorker", "minify", "result", "require", "input", "inputSourceMap", "sourceMap", "content", "compress", "inline", "global_defs", "keep_classnames", "keep_fnames", "module", "comments", "limit", "pLimit", "scheduledTasks", "asset", "push", "minifySpan", "sourceFromInputSource", "sourceAndMap", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "minifiedOutput", "errors", "code", "storePromise", "newInfo", "updateAsset", "end", "apply", "webpack", "sources", "pluginName", "hooks", "thisCompilation", "tap", "getCache", "handleHashForChunk", "hash", "_chunk", "update", "JSModulesHooks", "javascript", "JavascriptModulesPlugin", "getCompilationHooks", "chunkHash", "chunk", "hasRuntime", "processAssets", "tapPromise", "stage", "Compilation", "PROCESS_ASSETS_STAGE_OPTIMIZE_SIZE", "statsPrinter", "stats", "print", "for", "green", "formatFlag"], "mappings": ";;;;+BAiCaA;;;eAAAA;;;yBA1BN;+DACY;uBACgB;;;;;;AAEnC,SAASC,WAAWC,KAAU,EAAEC,IAAY;IAC1C,IAAID,MAAME,IAAI,EAAE;QACd,OAAO,IAAIC,qBAAY,CACrB,GAAGF,KAAK,gBAAgB,EAAED,MAAMI,OAAO,CAAC,EAAE,EAAEH,KAAK,CAAC,EAAED,MAAME,IAAI,CAAC,CAAC,EAC9DF,MAAMK,GAAG,CACV,CAAC,EACAL,MAAMM,KAAK,GAAG,CAAC,EAAE,EAAEN,MAAMM,KAAK,CAACC,KAAK,CAAC,MAAMC,KAAK,CAAC,GAAGC,IAAI,CAAC,OAAO,GAAG,IACnE;IAEN;IAEA,IAAIT,MAAMM,KAAK,EAAE;QACf,OAAO,IAAIH,qBAAY,CACrB,GAAGF,KAAK,gBAAgB,EAAED,MAAMI,OAAO,CAAC,EAAE,EAAEJ,MAAMM,KAAK,EAAE;IAE7D;IAEA,OAAO,IAAIH,qBAAY,CAAC,GAAGF,KAAK,gBAAgB,EAAED,MAAMI,OAAO,EAAE;AACnE;AAEA,MAAMM,cAAcC,QAAQC,GAAG,CAACC,iBAAiB;AAE1C,MAAMf;IACXgB,YACE,AAAQC,OAA4D,CACpE;aADQA,UAAAA;IACP;IAEH,MAAMC,SACJC,QAAa,EACbC,WAAwB,EACxBC,MAAW,EACXC,KAAkB,EAClB,EACEC,eAAe,EACfC,SAAS,EAIV,EACD;QACA,MAAMC,SAAS,IAAI,CAACR,OAAO,CAACS,UAAU,GAClC,QACA;YACEC,UAAU;gBAAC;aAAc;YACzBC,iBAAiB,CAAC,CAAC,IAAI,CAACX,OAAO,CAACW,eAAe;QACjD;QACJ,MAAMC,kBACJC,IAAAA,yBAAkB,EAACV,gBAAiBU,IAAAA,yBAAkB,EAACX;QAEzD,MAAMY,eAAeF,gBAAgBG,UAAU,CAC7C;QAGF,IAAIZ,YAAYa,IAAI,EAAE;YACpBF,aAAaG,YAAY,CAAC,mBAAmBd,YAAYa,IAAI;QAC/D;QAEAF,aAAaG,YAAY,CAAC,UAAUC,OAAOV;QAE3C,OAAOM,aAAaK,YAAY,CAAC;YAC/B,MAAMC,aAAaC,OAAOC,IAAI,CAAClB;YAE/B,MAAMmB,kBAAkB,MAAMC,QAAQC,GAAG,CACvCL,WACGM,MAAM,CAAC,CAACV;gBACP,IACE,CAACW,8BAAqB,CAACC,WAAW,CAACC,IAAI,CACrC,wCAAwC;gBACxCC,WACA;oBAAEC,MAAM;gBAAqB,GAC7Bf,OACF;oBACA,OAAO;gBACT;gBAEA,MAAMgB,MAAM7B,YAAY8B,QAAQ,CAACjB;gBACjC,IAAI,CAACgB,KAAK;oBACRE,QAAQC,GAAG,CAACnB;oBACZ,OAAO;gBACT;gBAEA,MAAM,EAAEoB,IAAI,EAAE,GAAGJ;gBAEjB,qDAAqD;gBACrD,IAAII,KAAKC,SAAS,EAAE;oBAClB,OAAO;gBACT;gBAEA,OAAO;YACT,GACCC,GAAG,CAAC,OAAOtB;gBACV,MAAM,EAAEoB,IAAI,EAAEG,MAAM,EAAE,GAAGpC,YAAY8B,QAAQ,CAACjB;gBAE9C,MAAMwB,OAAOnC,MAAMoC,UAAU,CAC3BpC,MAAMqC,iBAAiB,CAACH,SACxBI,KAAKC,SAAS,CAAC,IAAI,CAAC5C,OAAO;gBAG7B,MAAM6C,SAAS,MAAMxC,MAAMyC,UAAU,CAEnC9B,MAAMwB;gBAER,IAAI7C,eAAeA,gBAAgB,KAAK;oBACtCuC,QAAQC,GAAG,CACTQ,KAAKC,SAAS,CAAC;wBAAE5B;wBAAMuB,QAAQA,OAAOA,MAAM,GAAGQ,QAAQ;oBAAG,IAC1D;wBAAEC,aAAaC;wBAAUC,iBAAiBD;oBAAS;gBAEvD;gBACA,OAAO;oBAAEjC;oBAAMoB;oBAAMe,aAAaZ;oBAAQM;oBAAQL;gBAAK;YACzD;YAGJ,IAAIY;YAEJ,6CAA6C;YAC7C,MAAMC,YAAY;gBAChB,OAAO;oBACLC,QAAQ,OAAOtD;wBAIb,MAAMuD,SAAS,MAAM,AACnBC,QAAQ,mBACRF,MAAM,CAACtD,QAAQyD,KAAK,EAAE;4BACtB,GAAIzD,QAAQ0D,cAAc,GACtB;gCACEC,WAAW;oCACTC,SAASjB,KAAKC,SAAS,CAAC5C,QAAQ0D,cAAc;gCAChD;4BACF,IACA,CAAC,CAAC;4BACNG,UAAU;gCACRC,QAAQ;gCACRC,aAAa;oCACX,mDAAmD;gCACrD;gCACAC,iBAAiB,IAAI,CAAChE,OAAO,CAACS,UAAU;gCACxCwD,aAAa,IAAI,CAACjE,OAAO,CAACS,UAAU;4BACtC;4BACAD;4BACA0D,QAAQ;4BACRrB,QAAQ;gCACNsB,UAAU;4BACZ;wBACF;wBAEA,OAAOZ;oBACT;gBACF;YACF;YAEA,2DAA2D;YAC3D,MAAMa,QAAQC,IAAAA,eAAM,EAACpB;YACrB,MAAMqB,iBAAiB,EAAE;YAEzB,KAAK,MAAMC,SAAShD,gBAAiB;gBACnC+C,eAAeE,IAAI,CACjBJ,MAAM;oBACJ,MAAM,EAAEpD,IAAI,EAAEmC,WAAW,EAAEX,IAAI,EAAE,GAAG+B;oBACpC,IAAI,EAAE1B,MAAM,EAAE,GAAG0B;oBAEjB,MAAME,aAAa3D,aAAaC,UAAU,CAAC;oBAC3C0D,WAAWxD,YAAY,CAAC,QAAQD;oBAChCyD,WAAWxD,YAAY,CACrB,SACA,OAAO4B,WAAW,cAAc,SAAS;oBAG3C,OAAO4B,WAAWtD,YAAY,CAAC;wBAC7B,IAAI,CAAC0B,QAAQ;4BACX,MAAM,EAAEN,QAAQmC,qBAAqB,EAAEpC,KAAKoB,cAAc,EAAE,GAC1DP,YAAYwB,YAAY;4BAE1B,MAAMlB,QAAQmB,OAAOC,QAAQ,CAACH,yBAC1BA,sBAAsB3B,QAAQ,KAC9B2B;4BAEJ,IAAII;4BAEJ,IAAI;gCACFA,iBAAiB,MAAMzB,YAAYC,MAAM,CAAC;oCACxCG;oCACAC;gCACF;4BACF,EAAE,OAAOzE,OAAO;gCACdkB,YAAY4E,MAAM,CAACP,IAAI,CAACxF,WAAWC,OAAO+B;gCAE1C;4BACF;4BAEA,MAAMuB,SAASuC,eAAexC,GAAG,GAC7B,IAAIhC,gBACFwE,eAAeE,IAAI,EACnBhE,MACA8D,eAAexC,GAAG,EAClBmB,OACAC,gBACA,QAEF,IAAInD,UAAUuE,eAAeE,IAAI;4BAErC,MAAM3E,MAAM4E,YAAY,CAACjE,MAAMwB,MAAM;gCAAED;4BAAO;4BAE9CM,SAAS;gCAAEN;4BAAO;wBACpB;wBAEA,MAAM2C,UAAU;4BAAE7C,WAAW;wBAAK;wBAElClC,YAAYgF,WAAW,CAACnE,MAAM6B,OAAON,MAAM,EAAE2C;oBAC/C;gBACF;YAEJ;YAEA,MAAM1D,QAAQC,GAAG,CAAC6C;YAElB,IAAIlB,mBAAmB;gBACrB,MAAMA,kBAAkBgC,GAAG;YAC7B;QACF;IACF;IAEAC,MAAMnF,QAAa,EAAE;YACqBA;QAAxC,MAAM,EAAEI,eAAe,EAAEC,SAAS,EAAE,GAAIL,CAAAA,6BAAAA,oBAAAA,SAAUoF,OAAO,qBAAjBpF,kBAAmBqF,OAAO,KAChEA,gBAAO;QAET,MAAMC,aAAa,IAAI,CAACzF,WAAW,CAACiB,IAAI;QAExCd,SAASuF,KAAK,CAACC,eAAe,CAACC,GAAG,CAChCH,YACA,CAACrF;YACC,MAAME,QAAQF,YAAYyF,QAAQ,CAAC;YAEnC,MAAMC,qBAAqB,CAACC,MAAWC;gBACrC,oCAAoC;gBACpCD,KAAKE,MAAM,CAAC;YACd;YAEA,MAAMC,iBACJX,gBAAO,CAACY,UAAU,CAACC,uBAAuB,CAACC,mBAAmB,CAC5DjG;YAEJ8F,eAAeI,SAAS,CAACV,GAAG,CAACH,YAAY,CAACc,OAAOR;gBAC/C,IAAI,CAACQ,MAAMC,UAAU,IAAI;gBACzB,OAAOV,mBAAmBC,MAAMQ;YAClC;YAEAnG,YAAYsF,KAAK,CAACe,aAAa,CAACC,UAAU,CACxC;gBACEzF,MAAMwE;gBACNkB,OAAOpB,gBAAO,CAACqB,WAAW,CAACC,kCAAkC;YAC/D,GACA,CAACxG,SACC,IAAI,CAACH,QAAQ,CAACC,UAAUC,aAAaC,QAAQC,OAAO;oBAClDC;oBACAC;gBACF;YAGJJ,YAAYsF,KAAK,CAACoB,YAAY,CAAClB,GAAG,CAACH,YAAY,CAACsB;gBAC9CA,MAAMrB,KAAK,CAACsB,KAAK,CACdC,GAAG,CAAC,wBACJrB,GAAG,CACF,yBACA,CAACtD,WAAgB,EAAE4E,KAAK,EAAEC,UAAU,EAAO,GACzC,wCAAwC;oBACxC7E,YAAY4E,MAAMC,WAAW,gBAAgBpF;YAErD;QACF;IAEJ;AACF", "ignoreList": [0]}