{"version": 3, "sources": ["../../../../../../src/build/webpack/loaders/resolve-url-loader/lib/file-protocol.ts"], "sourcesContent": ["/*\nThe MIT License (MIT)\n\nCopyright (c) 2016 Ben <PERSON>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n*/\n\n/**\n * Prepend file:// protocol to source path string or source-map sources.\n */\nexport function prepend(candidate: any) {\n  if (typeof candidate === 'string') {\n    return 'file://' + candidate\n  } else if (\n    candidate &&\n    typeof candidate === 'object' &&\n    Array.isArray(candidate.sources)\n  ) {\n    return Object.assign({}, candidate, {\n      sources: candidate.sources.map(prepend),\n    })\n  } else {\n    throw new Error('expected string|object')\n  }\n}\n\n/**\n * Remove file:// protocol from source path string or source-map sources.\n */\nexport function remove(candidate: any) {\n  if (typeof candidate === 'string') {\n    return candidate.replace(/^file:\\/{2}/, '')\n  } else if (\n    candidate &&\n    typeof candidate === 'object' &&\n    Array.isArray(candidate.sources)\n  ) {\n    return Object.assign({}, candidate, {\n      sources: candidate.sources.map(remove),\n    })\n  } else {\n    throw new Error('expected string|object')\n  }\n}\n"], "names": ["prepend", "remove", "candidate", "Array", "isArray", "sources", "Object", "assign", "map", "Error", "replace"], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;;;;AAsBA,GAEA;;CAEC;;;;;;;;;;;;;;;IACeA,OAAO;eAAPA;;IAmBAC,MAAM;eAANA;;;AAnBT,SAASD,QAAQE,SAAc;IACpC,IAAI,OAAOA,cAAc,UAAU;QACjC,OAAO,YAAYA;IACrB,OAAO,IACLA,aACA,OAAOA,cAAc,YACrBC,MAAMC,OAAO,CAACF,UAAUG,OAAO,GAC/B;QACA,OAAOC,OAAOC,MAAM,CAAC,CAAC,GAAGL,WAAW;YAClCG,SAASH,UAAUG,OAAO,CAACG,GAAG,CAACR;QACjC;IACF,OAAO;QACL,MAAM,qBAAmC,CAAnC,IAAIS,MAAM,2BAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAAkC;IAC1C;AACF;AAKO,SAASR,OAAOC,SAAc;IACnC,IAAI,OAAOA,cAAc,UAAU;QACjC,OAAOA,UAAUQ,OAAO,CAAC,eAAe;IAC1C,OAAO,IACLR,aACA,OAAOA,cAAc,YACrBC,MAAMC,OAAO,CAACF,UAAUG,OAAO,GAC/B;QACA,OAAOC,OAAOC,MAAM,CAAC,CAAC,GAAGL,WAAW;YAClCG,SAASH,UAAUG,OAAO,CAACG,GAAG,CAACP;QACjC;IACF,OAAO;QACL,MAAM,qBAAmC,CAAnC,IAAIQ,MAAM,2BAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAAkC;IAC1C;AACF", "ignoreList": [0]}