{"version": 3, "sources": ["../../../../src/build/webpack/loaders/next-flight-action-entry-loader.ts"], "sourcesContent": ["import type { webpack } from 'next/dist/compiled/webpack/webpack'\n\nexport type NextFlightActionEntryLoaderOptions = {\n  actions: string\n}\n\nexport type FlightActionEntryLoaderActions = [\n  path: string,\n  actions: { id: string; exportedName: string }[],\n][]\n\nfunction nextFlightActionEntryLoader(\n  this: webpack.LoaderContext<NextFlightActionEntryLoaderOptions>\n) {\n  const { actions }: NextFlightActionEntryLoaderOptions = this.getOptions()\n\n  const actionList = JSON.parse(actions) as FlightActionEntryLoaderActions\n  const individualActions = actionList\n    .map(([path, actionsFromModule]) => {\n      return actionsFromModule.map(({ id, exportedName }) => {\n        return [id, path, exportedName] as const\n      })\n    })\n    .flat()\n\n  return `\n${individualActions\n  .map(([id, path, exportedName]) => {\n    // Re-export the same functions from the original module path as action IDs.\n    return `export { ${exportedName} as \"${id}\" } from ${JSON.stringify(path)}`\n  })\n  .join('\\n')}\n`\n}\n\nexport default nextFlightActionEntryLoader\n"], "names": ["nextFlightActionEntryLoader", "actions", "getOptions", "actionList", "JSON", "parse", "individualActions", "map", "path", "actionsFromModule", "id", "exportedName", "flat", "stringify", "join"], "mappings": ";;;;+BAmCA;;;eAAA;;;AAxBA,SAASA;IAGP,MAAM,EAAEC,OAAO,EAAE,GAAuC,IAAI,CAACC,UAAU;IAEvE,MAAMC,aAAaC,KAAKC,KAAK,CAACJ;IAC9B,MAAMK,oBAAoBH,WACvBI,GAAG,CAAC,CAAC,CAACC,MAAMC,kBAAkB;QAC7B,OAAOA,kBAAkBF,GAAG,CAAC,CAAC,EAAEG,EAAE,EAAEC,YAAY,EAAE;YAChD,OAAO;gBAACD;gBAAIF;gBAAMG;aAAa;QACjC;IACF,GACCC,IAAI;IAEP,OAAO,CAAC;AACV,EAAEN,kBACCC,GAAG,CAAC,CAAC,CAACG,IAAIF,MAAMG,aAAa;QAC5B,4EAA4E;QAC5E,OAAO,CAAC,SAAS,EAAEA,aAAa,KAAK,EAAED,GAAG,SAAS,EAAEN,KAAKS,SAAS,CAACL,OAAO;IAC7E,GACCM,IAAI,CAAC,MAAM;AACd,CAAC;AACD;MAEA,WAAed", "ignoreList": [0]}