{"version": 3, "sources": ["../../../../../../src/build/webpack/loaders/lightningcss-loader/src/minify.ts"], "sourcesContent": ["// @ts-ignore\nimport { ModuleFilenameHelpers } from 'next/dist/compiled/webpack/webpack'\nimport { webpack } from 'next/dist/compiled/webpack/webpack'\n// @ts-ignore\nimport { RawSource, SourceMapSource } from 'next/dist/compiled/webpack-sources3'\nimport { ECacheKey } from './interface'\nimport type { Compilation, Compiler } from 'webpack'\nimport { getTargets } from './utils'\nimport { Buffer } from 'buffer'\n\nconst PLUGIN_NAME = 'lightning-css-minify'\nconst CSS_FILE_REG = /\\.css(?:\\?.*)?$/i\n\nexport class LightningCssMinifyPlugin {\n  private readonly options: any\n  private transform: any | undefined\n\n  constructor(opts: any = {}) {\n    const { implementation, ...otherOpts } = opts\n    if (implementation && typeof implementation.transformCss !== 'function') {\n      throw new TypeError(\n        `[LightningCssMinifyPlugin]: implementation.transformCss must be an 'lightningcss' transform function. Received ${typeof implementation.transformCss}`\n      )\n    }\n\n    this.transform = implementation?.transformCss\n    this.options = otherOpts\n  }\n\n  apply(compiler: Compiler) {\n    const meta = JSON.stringify({\n      name: '@next/lightningcss-loader',\n      version: '0.0.0',\n      options: this.options,\n    })\n\n    compiler.hooks.compilation.tap(PLUGIN_NAME, (compilation) => {\n      compilation.hooks.chunkHash.tap(PLUGIN_NAME, (_, hash) =>\n        hash.update(meta)\n      )\n\n      compilation.hooks.processAssets.tapPromise(\n        {\n          name: PLUGIN_NAME,\n          stage: webpack.Compilation.PROCESS_ASSETS_STAGE_OPTIMIZE_SIZE,\n          additionalAssets: true,\n        },\n        async () => await this.transformAssets(compilation)\n      )\n\n      compilation.hooks.statsPrinter.tap(PLUGIN_NAME, (statsPrinter) => {\n        statsPrinter.hooks.print\n          .for('asset.info.minimized')\n          // @ts-ignore\n          .tap(PLUGIN_NAME, (minimized, { green, formatFlag }) => {\n            // @ts-ignore\n            return minimized ? green(formatFlag('minimized')) : undefined\n          })\n      })\n    })\n  }\n\n  private async transformAssets(compilation: Compilation): Promise<void> {\n    const {\n      options: { devtool },\n    } = compilation.compiler\n\n    if (!this.transform) {\n      const { loadBindings } =\n        require('../../../../../build/swc') as typeof import('../../../../../build/swc')\n      this.transform = (await loadBindings()).css.lightning.transform\n    }\n\n    const sourcemap =\n      this.options.sourceMap === undefined\n        ? ((devtool && (devtool as string).includes('source-map')) as boolean)\n        : this.options.sourceMap\n\n    const {\n      include,\n      exclude,\n      test: testRegExp,\n      targets: userTargets,\n      ...transformOptions\n    } = this.options\n\n    const assets = compilation.getAssets().filter(\n      (asset) =>\n        // Filter out already minimized\n        !asset.info.minimized &&\n        // Filter out by file type\n        (testRegExp || CSS_FILE_REG).test(asset.name) &&\n        ModuleFilenameHelpers.matchObject({ include, exclude }, asset.name)\n    )\n\n    await Promise.all(\n      assets.map(async (asset) => {\n        const { source, map } = asset.source.sourceAndMap()\n        const sourceAsString = source.toString()\n        const code = typeof source === 'string' ? Buffer.from(source) : source\n        const targets = getTargets({\n          targets: userTargets,\n          key: ECacheKey.minify,\n        })\n\n        const result = await this.transform!({\n          filename: asset.name,\n          code,\n          minify: true,\n          sourceMap: sourcemap,\n          targets,\n          ...transformOptions,\n        })\n        const codeString = result.code.toString()\n\n        compilation.updateAsset(\n          asset.name,\n          // @ts-ignore\n          sourcemap\n            ? new SourceMapSource(\n                codeString,\n                asset.name,\n                JSON.parse(result.map!.toString()),\n                sourceAsString,\n                map as any,\n                true\n              )\n            : new RawSource(codeString),\n          {\n            ...asset.info,\n            minimized: true,\n          }\n        )\n      })\n    )\n  }\n}\n"], "names": ["LightningCssMinifyPlugin", "PLUGIN_NAME", "CSS_FILE_REG", "constructor", "opts", "implementation", "otherOpts", "transformCss", "TypeError", "transform", "options", "apply", "compiler", "meta", "JSON", "stringify", "name", "version", "hooks", "compilation", "tap", "chunkHash", "_", "hash", "update", "processAssets", "tapPromise", "stage", "webpack", "Compilation", "PROCESS_ASSETS_STAGE_OPTIMIZE_SIZE", "additionalAssets", "transformAssets", "statsPrinter", "print", "for", "minimized", "green", "formatFlag", "undefined", "devtool", "loadBindings", "require", "css", "lightning", "sourcemap", "sourceMap", "includes", "include", "exclude", "test", "testRegExp", "targets", "userTargets", "transformOptions", "assets", "getAssets", "filter", "asset", "info", "ModuleFilenameHelpers", "matchObject", "Promise", "all", "map", "source", "sourceAndMap", "sourceAsString", "toString", "code", "<PERSON><PERSON><PERSON>", "from", "getTargets", "key", "<PERSON><PERSON><PERSON><PERSON>", "minify", "result", "filename", "codeString", "updateAsset", "SourceMapSource", "parse", "RawSource"], "mappings": "AAAA,aAAa;;;;;+BAaAA;;;eAAAA;;;yBAZyB;iCAGK;2BACjB;uBAEC;wBACJ;AAEvB,MAAMC,cAAc;AACpB,MAAMC,eAAe;AAEd,MAAMF;IAIXG,YAAYC,OAAY,CAAC,CAAC,CAAE;QAC1B,MAAM,EAAEC,cAAc,EAAE,GAAGC,WAAW,GAAGF;QACzC,IAAIC,kBAAkB,OAAOA,eAAeE,YAAY,KAAK,YAAY;YACvE,MAAM,qBAEL,CAFK,IAAIC,UACR,CAAC,+GAA+G,EAAE,OAAOH,eAAeE,YAAY,EAAE,GADlJ,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,IAAI,CAACE,SAAS,GAAGJ,kCAAAA,eAAgBE,YAAY;QAC7C,IAAI,CAACG,OAAO,GAAGJ;IACjB;IAEAK,MAAMC,QAAkB,EAAE;QACxB,MAAMC,OAAOC,KAAKC,SAAS,CAAC;YAC1BC,MAAM;YACNC,SAAS;YACTP,SAAS,IAAI,CAACA,OAAO;QACvB;QAEAE,SAASM,KAAK,CAACC,WAAW,CAACC,GAAG,CAACnB,aAAa,CAACkB;YAC3CA,YAAYD,KAAK,CAACG,SAAS,CAACD,GAAG,CAACnB,aAAa,CAACqB,GAAGC,OAC/CA,KAAKC,MAAM,CAACX;YAGdM,YAAYD,KAAK,CAACO,aAAa,CAACC,UAAU,CACxC;gBACEV,MAAMf;gBACN0B,OAAOC,gBAAO,CAACC,WAAW,CAACC,kCAAkC;gBAC7DC,kBAAkB;YACpB,GACA,UAAY,MAAM,IAAI,CAACC,eAAe,CAACb;YAGzCA,YAAYD,KAAK,CAACe,YAAY,CAACb,GAAG,CAACnB,aAAa,CAACgC;gBAC/CA,aAAaf,KAAK,CAACgB,KAAK,CACrBC,GAAG,CAAC,uBACL,aAAa;iBACZf,GAAG,CAACnB,aAAa,CAACmC,WAAW,EAAEC,KAAK,EAAEC,UAAU,EAAE;oBACjD,aAAa;oBACb,OAAOF,YAAYC,MAAMC,WAAW,gBAAgBC;gBACtD;YACJ;QACF;IACF;IAEA,MAAcP,gBAAgBb,WAAwB,EAAiB;QACrE,MAAM,EACJT,SAAS,EAAE8B,OAAO,EAAE,EACrB,GAAGrB,YAAYP,QAAQ;QAExB,IAAI,CAAC,IAAI,CAACH,SAAS,EAAE;YACnB,MAAM,EAAEgC,YAAY,EAAE,GACpBC,QAAQ;YACV,IAAI,CAACjC,SAAS,GAAG,AAAC,CAAA,MAAMgC,cAAa,EAAGE,GAAG,CAACC,SAAS,CAACnC,SAAS;QACjE;QAEA,MAAMoC,YACJ,IAAI,CAACnC,OAAO,CAACoC,SAAS,KAAKP,YACrBC,WAAW,AAACA,QAAmBO,QAAQ,CAAC,gBAC1C,IAAI,CAACrC,OAAO,CAACoC,SAAS;QAE5B,MAAM,EACJE,OAAO,EACPC,OAAO,EACPC,MAAMC,UAAU,EAChBC,SAASC,WAAW,EACpB,GAAGC,kBACJ,GAAG,IAAI,CAAC5C,OAAO;QAEhB,MAAM6C,SAASpC,YAAYqC,SAAS,GAAGC,MAAM,CAC3C,CAACC,QACC,+BAA+B;YAC/B,CAACA,MAAMC,IAAI,CAACvB,SAAS,IAErB,AADA,0BAA0B;YACzBe,CAAAA,cAAcjD,YAAW,EAAGgD,IAAI,CAACQ,MAAM1C,IAAI,KAC5C4C,8BAAqB,CAACC,WAAW,CAAC;gBAAEb;gBAASC;YAAQ,GAAGS,MAAM1C,IAAI;QAGtE,MAAM8C,QAAQC,GAAG,CACfR,OAAOS,GAAG,CAAC,OAAON;YAChB,MAAM,EAAEO,MAAM,EAAED,GAAG,EAAE,GAAGN,MAAMO,MAAM,CAACC,YAAY;YACjD,MAAMC,iBAAiBF,OAAOG,QAAQ;YACtC,MAAMC,OAAO,OAAOJ,WAAW,WAAWK,cAAM,CAACC,IAAI,CAACN,UAAUA;YAChE,MAAMb,UAAUoB,IAAAA,iBAAU,EAAC;gBACzBpB,SAASC;gBACToB,KAAKC,oBAAS,CAACC,MAAM;YACvB;YAEA,MAAMC,SAAS,MAAM,IAAI,CAACnE,SAAS,CAAE;gBACnCoE,UAAUnB,MAAM1C,IAAI;gBACpBqD;gBACAM,QAAQ;gBACR7B,WAAWD;gBACXO;gBACA,GAAGE,gBAAgB;YACrB;YACA,MAAMwB,aAAaF,OAAOP,IAAI,CAACD,QAAQ;YAEvCjD,YAAY4D,WAAW,CACrBrB,MAAM1C,IAAI,EACV,aAAa;YACb6B,YACI,IAAImC,gCAAe,CACjBF,YACApB,MAAM1C,IAAI,EACVF,KAAKmE,KAAK,CAACL,OAAOZ,GAAG,CAAEI,QAAQ,KAC/BD,gBACAH,KACA,QAEF,IAAIkB,0BAAS,CAACJ,aAClB;gBACE,GAAGpB,MAAMC,IAAI;gBACbvB,WAAW;YACb;QAEJ;IAEJ;AACF", "ignoreList": [0]}