{"version": 3, "sources": ["../../../src/build/turborepo-access-trace/result.ts"], "sourcesContent": ["import type {\n  Addresses,\n  EnvVars,\n  FS,\n  SerializableTurborepoAccessTraceResult,\n  PublicTurborepoAccessTraceResult,\n} from './types'\n\nexport class TurborepoAccessTraceResult {\n  constructor(\n    private envVars: EnvVars = new Set([]),\n    private addresses: Addresses = [],\n    private fsPaths: FS = new Set([])\n  ) {}\n\n  /**\n   * Merge another `TurborepoAccessTraceResult` into this one, mutating this `TurborepoAccessTraceResult`.\n   */\n  public merge(other: TurborepoAccessTraceResult) {\n    other.envVars.forEach((envVar) => this.envVars.add(envVar))\n    other.fsPaths.forEach((path) => this.fsPaths.add(path))\n    this.addresses.push(...other.addresses)\n\n    return this\n  }\n\n  /**\n   * Serialize this `TurborepoAccessTraceResult` into a serializable object. Used for passing\n   * the `TurborepoAccessTraceResult` between workers where Sets are not serializable.\n   */\n  public serialize(): SerializableTurborepoAccessTraceResult {\n    return {\n      fs: Array.from(this.fsPaths).map(String),\n      addresses: this.addresses,\n      envVars: Array.from(this.envVars).map(String),\n    }\n  }\n\n  /**\n   * Squash this `TurborepoAccessTraceResult` into a public trace object that can be written to a file\n   */\n  public toPublicTrace(): PublicTurborepoAccessTraceResult {\n    return {\n      network: this.addresses.length > 0,\n      envVarKeys: Array.from(this.envVars).map(String),\n      filePaths: Array.from(this.fsPaths).map(String),\n    }\n  }\n\n  /**\n   * Create an `TurborepoAccessTraceResult` from a serialized `SerializableTurborepoAccessTraceResult`\n   */\n  public static fromSerialized(\n    serialized: SerializableTurborepoAccessTraceResult\n  ) {\n    return new TurborepoAccessTraceResult(\n      new Set(serialized.envVars),\n      serialized.addresses,\n      new Set(serialized.fs)\n    )\n  }\n}\n"], "names": ["TurborepoAccessTraceResult", "constructor", "envVars", "Set", "addresses", "fsPaths", "merge", "other", "for<PERSON>ach", "envVar", "add", "path", "push", "serialize", "fs", "Array", "from", "map", "String", "toPublicTrace", "network", "length", "env<PERSON><PERSON><PERSON><PERSON><PERSON>", "filePaths", "fromSerialized", "serialized"], "mappings": ";;;;+BAQaA;;;eAAAA;;;AAAN,MAAMA;IACXC,YACE,AAAQC,UAAmB,IAAIC,IAAI,EAAE,CAAC,EACtC,AAAQC,YAAuB,EAAE,EACjC,AAAQC,UAAc,IAAIF,IAAI,EAAE,CAAC,CACjC;aAHQD,UAAAA;aACAE,YAAAA;aACAC,UAAAA;IACP;IAEH;;GAEC,GACD,AAAOC,MAAMC,KAAiC,EAAE;QAC9CA,MAAML,OAAO,CAACM,OAAO,CAAC,CAACC,SAAW,IAAI,CAACP,OAAO,CAACQ,GAAG,CAACD;QACnDF,MAAMF,OAAO,CAACG,OAAO,CAAC,CAACG,OAAS,IAAI,CAACN,OAAO,CAACK,GAAG,CAACC;QACjD,IAAI,CAACP,SAAS,CAACQ,IAAI,IAAIL,MAAMH,SAAS;QAEtC,OAAO,IAAI;IACb;IAEA;;;GAGC,GACD,AAAOS,YAAoD;QACzD,OAAO;YACLC,IAAIC,MAAMC,IAAI,CAAC,IAAI,CAACX,OAAO,EAAEY,GAAG,CAACC;YACjCd,WAAW,IAAI,CAACA,SAAS;YACzBF,SAASa,MAAMC,IAAI,CAAC,IAAI,CAACd,OAAO,EAAEe,GAAG,CAACC;QACxC;IACF;IAEA;;GAEC,GACD,AAAOC,gBAAkD;QACvD,OAAO;YACLC,SAAS,IAAI,CAAChB,SAAS,CAACiB,MAAM,GAAG;YACjCC,YAAYP,MAAMC,IAAI,CAAC,IAAI,CAACd,OAAO,EAAEe,GAAG,CAACC;YACzCK,WAAWR,MAAMC,IAAI,CAAC,IAAI,CAACX,OAAO,EAAEY,GAAG,CAACC;QAC1C;IACF;IAEA;;GAEC,GACD,OAAcM,eACZC,UAAkD,EAClD;QACA,OAAO,IAAIzB,2BACT,IAAIG,IAAIsB,WAAWvB,OAAO,GAC1BuB,WAAWrB,SAAS,EACpB,IAAID,IAAIsB,WAAWX,EAAE;IAEzB;AACF", "ignoreList": [0]}