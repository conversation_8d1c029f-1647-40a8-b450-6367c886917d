{"version": 3, "sources": ["../../../../src/build/jest/__mocks__/fileMock.ts"], "sourcesContent": ["module.exports = {\n  src: '/img.jpg',\n  height: 40,\n  width: 40,\n  blurDataURL: 'data:image/png;base64,imagedata',\n}\n"], "names": ["module", "exports", "src", "height", "width", "blurDataURL"], "mappings": ";AAAAA,OAAOC,OAAO,GAAG;IACfC,KAAK;IACLC,QAAQ;IACRC,OAAO;IACPC,aAAa;AACf", "ignoreList": [0]}