{"version": 3, "sources": ["../../../src/build/turbopack-build/index.ts"], "sourcesContent": ["import path from 'path'\n\nimport { Worker } from '../../lib/worker'\nimport { NextBuildContext } from '../build-context'\n\nasync function turbopackBuildWithWorker() {\n  try {\n    const worker = new Worker(path.join(__dirname, 'impl.js'), {\n      exposedMethods: ['workerMain', 'waitForShutdown'],\n      debuggerPortOffset: -1,\n      isolatedMemory: false,\n      numWorkers: 1,\n      maxRetries: 0,\n      forkOptions: {\n        env: {\n          NEXT_PRIVATE_BUILD_WORKER: '1',\n        },\n      },\n    }) as Worker & typeof import('./impl')\n    const { nextBuildSpan, ...prunedBuildContext } = NextBuildContext\n    const result = await worker.workerMain({\n      buildContext: prunedBuildContext,\n    })\n\n    // destroy worker when Turbo<PERSON> has shutdown so it's not sticking around using memory\n    // We need to wait for shutdown to make sure persistent cache is flushed\n    result.shutdownPromise = worker.waitForShutdown().then(() => {\n      worker.end()\n    })\n\n    return result\n  } catch (err: any) {\n    // When the error is a serialized `Error` object we need to recreate the `Error` instance\n    // in order to keep the consistent error reporting behavior.\n    if (err.type === 'Error') {\n      const error = new Error(err.message)\n      if (err.name) {\n        error.name = err.name\n      }\n      if (err.cause) {\n        error.cause = err.cause\n      }\n      error.message = err.message\n      error.stack = err.stack\n      throw error\n    }\n    throw err\n  }\n}\n\nexport function turbopackBuild(\n  withWorker: boolean\n): ReturnType<typeof import('./impl').turbopackBuild> {\n  if (withWorker) {\n    return turbopackBuildWithWorker()\n  } else {\n    const build = (require('./impl') as typeof import('./impl')).turbopackBuild\n    return build()\n  }\n}\n"], "names": ["turbopackBuild", "turbopackBuildWithWorker", "worker", "Worker", "path", "join", "__dirname", "exposedMethods", "debuggerPortOffset", "isolated<PERSON><PERSON><PERSON>", "numWorkers", "maxRetries", "forkOptions", "env", "NEXT_PRIVATE_BUILD_WORKER", "nextBuildSpan", "prunedBuildContext", "NextBuildContext", "result", "worker<PERSON>ain", "buildContext", "shutdownPromise", "waitForShutdown", "then", "end", "err", "type", "error", "Error", "message", "name", "cause", "stack", "with<PERSON><PERSON>ker", "build", "require"], "mappings": ";;;;+BAkDgBA;;;eAAAA;;;6DAlDC;wBAEM;8BACU;;;;;;AAEjC,eAAeC;IACb,IAAI;QACF,MAAMC,SAAS,IAAIC,cAAM,CAACC,aAAI,CAACC,IAAI,CAACC,WAAW,YAAY;YACzDC,gBAAgB;gBAAC;gBAAc;aAAkB;YACjDC,oBAAoB,CAAC;YACrBC,gBAAgB;YAChBC,YAAY;YACZC,YAAY;YACZC,aAAa;gBACXC,KAAK;oBACHC,2BAA2B;gBAC7B;YACF;QACF;QACA,MAAM,EAAEC,aAAa,EAAE,GAAGC,oBAAoB,GAAGC,8BAAgB;QACjE,MAAMC,SAAS,MAAMhB,OAAOiB,UAAU,CAAC;YACrCC,cAAcJ;QAChB;QAEA,sFAAsF;QACtF,wEAAwE;QACxEE,OAAOG,eAAe,GAAGnB,OAAOoB,eAAe,GAAGC,IAAI,CAAC;YACrDrB,OAAOsB,GAAG;QACZ;QAEA,OAAON;IACT,EAAE,OAAOO,KAAU;QACjB,yFAAyF;QACzF,4DAA4D;QAC5D,IAAIA,IAAIC,IAAI,KAAK,SAAS;YACxB,MAAMC,QAAQ,qBAAsB,CAAtB,IAAIC,MAAMH,IAAII,OAAO,GAArB,qBAAA;uBAAA;4BAAA;8BAAA;YAAqB;YACnC,IAAIJ,IAAIK,IAAI,EAAE;gBACZH,MAAMG,IAAI,GAAGL,IAAIK,IAAI;YACvB;YACA,IAAIL,IAAIM,KAAK,EAAE;gBACbJ,MAAMI,KAAK,GAAGN,IAAIM,KAAK;YACzB;YACAJ,MAAME,OAAO,GAAGJ,IAAII,OAAO;YAC3BF,MAAMK,KAAK,GAAGP,IAAIO,KAAK;YACvB,MAAML;QACR;QACA,MAAMF;IACR;AACF;AAEO,SAASzB,eACdiC,UAAmB;IAEnB,IAAIA,YAAY;QACd,OAAOhC;IACT,OAAO;QACL,MAAMiC,QAAQ,AAACC,QAAQ,UAAsCnC,cAAc;QAC3E,OAAOkC;IACT;AACF", "ignoreList": [0]}