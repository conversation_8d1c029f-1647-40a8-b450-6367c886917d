{"version": 3, "sources": ["../../../src/build/templates/edge-app-route.ts"], "sourcesContent": ["import { createServerModuleMap } from '../../server/app-render/action-utils'\nimport { setReferenceManifestsSingleton } from '../../server/app-render/encryption-utils'\nimport type { NextConfigComplete } from '../../server/config-shared'\nimport { EdgeRouteModuleWrapper } from '../../server/web/edge-route-module-wrapper'\n\n// Import the userland code.\nimport * as module from 'VAR_USERLAND'\n\n// injected by the loader afterwards.\ndeclare const nextConfig: NextConfigComplete\n// INJECT:nextConfig\n\nconst maybeJSONParse = (str?: string) => (str ? JSON.parse(str) : undefined)\n\nconst rscManifest = self.__RSC_MANIFEST?.['VAR_PAGE']\nconst rscServerManifest = maybeJSONParse(self.__RSC_SERVER_MANIFEST)\n\nif (rscManifest && rscServerManifest) {\n  setReferenceManifestsSingleton({\n    page: 'VAR_PAGE',\n    clientReferenceManifest: rscManifest,\n    serverActionsManifest: rscServerManifest,\n    serverModuleMap: createServerModuleMap({\n      serverActionsManifest: rscServerManifest,\n    }),\n  })\n}\n\nexport const ComponentMod = module\n\nexport default EdgeRouteModuleWrapper.wrap(module.routeModule, { nextConfig })\n"], "names": ["ComponentMod", "self", "maybeJSONParse", "str", "JSON", "parse", "undefined", "rscManifest", "__RSC_MANIFEST", "rscServerManifest", "__RSC_SERVER_MANIFEST", "setReferenceManifestsSingleton", "page", "clientReferenceManifest", "serverActionsManifest", "serverModuleMap", "createServerModuleMap", "module", "EdgeRouteModuleWrapper", "wrap", "routeModule", "nextConfig"], "mappings": ";;;;;;;;;;;;;;;IA4BaA,YAAY;eAAZA;;IAEb,OAA8E;eAA9E;;;6BA9BsC;iCACS;wCAER;sEAGf;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAQJC;AAJpB,oBAAoB;AAEpB,MAAMC,iBAAiB,CAACC,MAAkBA,MAAMC,KAAKC,KAAK,CAACF,OAAOG;AAElE,MAAMC,eAAcN,uBAAAA,KAAKO,cAAc,qBAAnBP,oBAAqB,CAAC,WAAW;AACrD,MAAMQ,oBAAoBP,eAAeD,KAAKS,qBAAqB;AAEnE,IAAIH,eAAeE,mBAAmB;IACpCE,IAAAA,+CAA8B,EAAC;QAC7BC,MAAM;QACNC,yBAAyBN;QACzBO,uBAAuBL;QACvBM,iBAAiBC,IAAAA,kCAAqB,EAAC;YACrCF,uBAAuBL;QACzB;IACF;AACF;AAEO,MAAMT,eAAeiB;MAE5B,WAAeC,8CAAsB,CAACC,IAAI,CAACF,cAAOG,WAAW,EAAE;IAAEC;AAAW", "ignoreList": [0]}