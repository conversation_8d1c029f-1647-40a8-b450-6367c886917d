{"version": 3, "sources": ["../../../src/build/templates/app-page.ts"], "sourcesContent": ["import type { LoaderTree } from '../../server/lib/app-dir-module'\nimport type { IncomingMessage, ServerResponse } from 'node:http'\n\nimport {\n  AppPageRouteModule,\n  type AppPageRouteHandlerContext,\n} from '../../server/route-modules/app-page/module.compiled' with { 'turbopack-transition': 'next-ssr' }\n\nimport { RouteKind } from '../../server/route-kind' with { 'turbopack-transition': 'next-server-utility' }\n\nimport { getRevalidateReason } from '../../server/instrumentation/utils'\nimport { getTracer, SpanKind, type Span } from '../../server/lib/trace/tracer'\nimport { getRequestMeta } from '../../server/request-meta'\nimport { BaseServerSpan } from '../../server/lib/trace/constants'\nimport { interopDefault } from '../../server/app-render/interop-default'\nimport { NodeNextRequest, NodeNextResponse } from '../../server/base-http/node'\nimport { checkIsAppPPREnabled } from '../../server/lib/experimental/ppr'\nimport {\n  getFallbackRouteParams,\n  type FallbackRouteParams,\n} from '../../server/request/fallback-params'\nimport { setReferenceManifestsSingleton } from '../../server/app-render/encryption-utils'\nimport {\n  isHtmlBotRequest,\n  shouldServeStreamingMetadata,\n} from '../../server/lib/streaming-metadata'\nimport { createServerModuleMap } from '../../server/app-render/action-utils'\nimport { normalizeAppPath } from '../../shared/lib/router/utils/app-paths'\nimport { getIsPossibleServerAction } from '../../server/lib/server-action-request-meta'\nimport {\n  RSC_HEADER,\n  NEXT_ROUTER_PREFETCH_HEADER,\n  NEXT_IS_PRERENDER_HEADER,\n  NEXT_DID_POSTPONE_HEADER,\n} from '../../client/components/app-router-headers'\nimport { getBotType, isBot } from '../../shared/lib/router/utils/is-bot'\nimport {\n  CachedRouteKind,\n  type CachedAppPageValue,\n  type CachedPageValue,\n  type ResponseCacheEntry,\n  type ResponseGenerator,\n} from '../../server/response-cache'\nimport { FallbackMode, parseFallbackField } from '../../lib/fallback'\nimport RenderResult from '../../server/render-result'\nimport { CACHE_ONE_YEAR, NEXT_CACHE_TAGS_HEADER } from '../../lib/constants'\nimport type { CacheControl } from '../../server/lib/cache-control'\nimport { ENCODED_TAGS } from '../../server/stream-utils/encoded-tags'\nimport { sendRenderResult } from '../../server/send-payload'\nimport { NoFallbackError } from '../../shared/lib/no-fallback-error.external'\n\n// These are injected by the loader afterwards.\n\n/**\n * The tree created in next-app-loader that holds component segments and modules\n * and I've updated it.\n */\ndeclare const tree: LoaderTree\ndeclare const pages: any\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\n// INJECT:tree\n// INJECT:pages\n\nexport { tree, pages }\n\nimport GlobalError from 'VAR_MODULE_GLOBAL_ERROR' with { 'turbopack-transition': 'next-server-utility' }\n\nexport { GlobalError }\n\n// These are injected by the loader afterwards.\ndeclare const __next_app_require__: (id: string | number) => unknown\ndeclare const __next_app_load_chunk__: (id: string | number) => Promise<unknown>\n\n// INJECT:__next_app_require__\n// INJECT:__next_app_load_chunk__\n\nexport const __next_app__ = {\n  require: __next_app_require__,\n  loadChunk: __next_app_load_chunk__,\n}\n\nimport * as entryBase from '../../server/app-render/entry-base' with { 'turbopack-transition': 'next-server-utility' }\nimport { RedirectStatusCode } from '../../client/components/redirect-status-code'\n\nexport * from '../../server/app-render/entry-base' with { 'turbopack-transition': 'next-server-utility' }\n\n// Create and export the route module that will be consumed.\nexport const routeModule = new AppPageRouteModule({\n  definition: {\n    kind: RouteKind.APP_PAGE,\n    page: 'VAR_DEFINITION_PAGE',\n    pathname: 'VAR_DEFINITION_PATHNAME',\n    // The following aren't used in production.\n    bundlePath: '',\n    filename: '',\n    appPaths: [],\n  },\n  userland: {\n    loaderTree: tree,\n  },\n  distDir: process.env.__NEXT_RELATIVE_DIST_DIR || '',\n  projectDir: process.env.__NEXT_RELATIVE_PROJECT_DIR || '',\n})\n\nexport async function handler(\n  req: IncomingMessage,\n  res: ServerResponse,\n  ctx: {\n    waitUntil: (prom: Promise<void>) => void\n  }\n) {\n  let srcPage = 'VAR_DEFINITION_PAGE'\n\n  // turbopack doesn't normalize `/index` in the page name\n  // so we need to to process dynamic routes properly\n  // TODO: fix turbopack providing differing value from webpack\n  if (process.env.TURBOPACK) {\n    srcPage = srcPage.replace(/\\/index$/, '') || '/'\n  } else if (srcPage === '/index') {\n    // we always normalize /index specifically\n    srcPage = '/'\n  }\n  const multiZoneDraftMode = process.env\n    .__NEXT_MULTI_ZONE_DRAFT_MODE as any as boolean\n\n  const initialPostponed = getRequestMeta(req, 'postponed')\n  // TODO: replace with more specific flags\n  const minimalMode = getRequestMeta(req, 'minimalMode')\n\n  const prepareResult = await routeModule.prepare(req, res, {\n    srcPage,\n    multiZoneDraftMode,\n  })\n\n  if (!prepareResult) {\n    res.statusCode = 400\n    res.end('Bad Request')\n    ctx.waitUntil?.(Promise.resolve())\n    return null\n  }\n\n  const {\n    buildId,\n    query,\n    params,\n    parsedUrl,\n    pageIsDynamic,\n    buildManifest,\n    nextFontManifest,\n    reactLoadableManifest,\n    serverActionsManifest,\n    clientReferenceManifest,\n    subresourceIntegrityManifest,\n    prerenderManifest,\n    isDraftMode,\n    resolvedPathname,\n    revalidateOnlyGenerated,\n    routerServerContext,\n    nextConfig,\n  } = prepareResult\n\n  const pathname = parsedUrl.pathname || '/'\n  const normalizedSrcPage = normalizeAppPath(srcPage)\n\n  let { isOnDemandRevalidate } = prepareResult\n\n  const prerenderInfo = prerenderManifest.dynamicRoutes[normalizedSrcPage]\n  const isPrerendered = prerenderManifest.routes[resolvedPathname]\n\n  let isSSG = Boolean(\n    prerenderInfo ||\n      isPrerendered ||\n      prerenderManifest.routes[normalizedSrcPage]\n  )\n\n  const userAgent = req.headers['user-agent'] || ''\n  const botType = getBotType(userAgent)\n  const isHtmlBot = isHtmlBotRequest(req)\n\n  /**\n   * If true, this indicates that the request being made is for an app\n   * prefetch request.\n   */\n  const isPrefetchRSCRequest =\n    getRequestMeta(req, 'isPrefetchRSCRequest') ??\n    Boolean(req.headers[NEXT_ROUTER_PREFETCH_HEADER])\n\n  // NOTE: Don't delete headers[RSC] yet, it still needs to be used in renderToHTML later\n\n  const isRSCRequest =\n    getRequestMeta(req, 'isRSCRequest') ?? Boolean(req.headers[RSC_HEADER])\n\n  const isPossibleServerAction = getIsPossibleServerAction(req)\n\n  /**\n   * If the route being rendered is an app page, and the ppr feature has been\n   * enabled, then the given route _could_ support PPR.\n   */\n  const couldSupportPPR: boolean = checkIsAppPPREnabled(\n    nextConfig.experimental.ppr\n  )\n\n  // When enabled, this will allow the use of the `?__nextppronly` query to\n  // enable debugging of the static shell.\n  const hasDebugStaticShellQuery =\n    process.env.__NEXT_EXPERIMENTAL_STATIC_SHELL_DEBUGGING === '1' &&\n    typeof query.__nextppronly !== 'undefined' &&\n    couldSupportPPR\n\n  // When enabled, this will allow the use of the `?__nextppronly` query\n  // to enable debugging of the fallback shell.\n  const hasDebugFallbackShellQuery =\n    hasDebugStaticShellQuery && query.__nextppronly === 'fallback'\n\n  // This page supports PPR if it is marked as being `PARTIALLY_STATIC` in the\n  // prerender manifest and this is an app page.\n  const isRoutePPREnabled: boolean =\n    couldSupportPPR &&\n    ((\n      prerenderManifest.routes[normalizedSrcPage] ??\n      prerenderManifest.dynamicRoutes[normalizedSrcPage]\n    )?.renderingMode === 'PARTIALLY_STATIC' ||\n      // Ideally we'd want to check the appConfig to see if this page has PPR\n      // enabled or not, but that would require plumbing the appConfig through\n      // to the server during development. We assume that the page supports it\n      // but only during development.\n      (hasDebugStaticShellQuery &&\n        (routeModule.isDev === true ||\n          routerServerContext?.experimentalTestProxy === true)))\n\n  const isDebugStaticShell: boolean =\n    hasDebugStaticShellQuery && isRoutePPREnabled\n\n  // We should enable debugging dynamic accesses when the static shell\n  // debugging has been enabled and we're also in development mode.\n  const isDebugDynamicAccesses =\n    isDebugStaticShell && routeModule.isDev === true\n\n  const isDebugFallbackShell = hasDebugFallbackShellQuery && isRoutePPREnabled\n\n  // If we're in minimal mode, then try to get the postponed information from\n  // the request metadata. If available, use it for resuming the postponed\n  // render.\n  const minimalPostponed = isRoutePPREnabled ? initialPostponed : undefined\n\n  // If PPR is enabled, and this is a RSC request (but not a prefetch), then\n  // we can use this fact to only generate the flight data for the request\n  // because we can't cache the HTML (as it's also dynamic).\n  const isDynamicRSCRequest =\n    isRoutePPREnabled && isRSCRequest && !isPrefetchRSCRequest\n\n  // Need to read this before it's stripped by stripFlightHeaders. We don't\n  // need to transfer it to the request meta because it's only read\n  // within this function; the static segment data should have already been\n  // generated, so we will always either return a static response or a 404.\n  const segmentPrefetchHeader = getRequestMeta(req, 'segmentPrefetchRSCRequest')\n\n  // TODO: investigate existing bug with shouldServeStreamingMetadata always\n  // being true for a revalidate due to modifying the base-server this.renderOpts\n  // when fixing this to correct logic it causes hydration issue since we set\n  // serveStreamingMetadata to true during export\n  let serveStreamingMetadata = !userAgent\n    ? true\n    : shouldServeStreamingMetadata(userAgent, nextConfig.htmlLimitedBots)\n\n  if (isHtmlBot && isRoutePPREnabled) {\n    isSSG = false\n    serveStreamingMetadata = false\n  }\n\n  // In development, we always want to generate dynamic HTML.\n  let supportsDynamicResponse: boolean =\n    // If we're in development, we always support dynamic HTML, unless it's\n    // a data request, in which case we only produce static HTML.\n    routeModule.isDev === true ||\n    // If this is not SSG or does not have static paths, then it supports\n    // dynamic HTML.\n    !isSSG ||\n    // If this request has provided postponed data, it supports dynamic\n    // HTML.\n    typeof initialPostponed === 'string' ||\n    // If this is a dynamic RSC request, then this render supports dynamic\n    // HTML (it's dynamic).\n    isDynamicRSCRequest\n\n  // When html bots request PPR page, perform the full dynamic rendering.\n  const shouldWaitOnAllReady = isHtmlBot && isRoutePPREnabled\n\n  let ssgCacheKey: string | null = null\n  if (\n    !isDraftMode &&\n    isSSG &&\n    !supportsDynamicResponse &&\n    !isPossibleServerAction &&\n    !minimalPostponed &&\n    !isDynamicRSCRequest\n  ) {\n    ssgCacheKey = resolvedPathname\n  }\n\n  // the staticPathKey differs from ssgCacheKey since\n  // ssgCacheKey is null in dev since we're always in \"dynamic\"\n  // mode in dev to bypass the cache, but we still need to honor\n  // dynamicParams = false in dev mode\n  let staticPathKey = ssgCacheKey\n  if (!staticPathKey && routeModule.isDev) {\n    staticPathKey = resolvedPathname\n  }\n\n  const ComponentMod = {\n    ...entryBase,\n    tree,\n    pages,\n    GlobalError,\n    handler,\n    routeModule,\n    __next_app__,\n  }\n\n  // Before rendering (which initializes component tree modules), we have to\n  // set the reference manifests to our global store so Server Action's\n  // encryption util can access to them at the top level of the page module.\n  if (serverActionsManifest && clientReferenceManifest) {\n    setReferenceManifestsSingleton({\n      page: srcPage,\n      clientReferenceManifest,\n      serverActionsManifest,\n      serverModuleMap: createServerModuleMap({\n        serverActionsManifest,\n      }),\n    })\n  }\n\n  const method = req.method || 'GET'\n  const tracer = getTracer()\n  const activeSpan = tracer.getActiveScopeSpan()\n\n  try {\n    const invokeRouteModule = async (\n      span: Span | undefined,\n      context: AppPageRouteHandlerContext\n    ) => {\n      const nextReq = new NodeNextRequest(req)\n      const nextRes = new NodeNextResponse(res)\n\n      // TODO: adapt for putting the RDC inside the postponed data\n      // If we're in dev, and this isn't a prefetch or a server action,\n      // we should seed the resume data cache.\n      if (process.env.NODE_ENV === 'development') {\n        if (\n          nextConfig.experimental.dynamicIO &&\n          !isPrefetchRSCRequest &&\n          !context.renderOpts.isPossibleServerAction\n        ) {\n          const warmup = await routeModule.warmup(nextReq, nextRes, context)\n\n          // If the warmup is successful, we should use the resume data\n          // cache from the warmup.\n          if (warmup.metadata.renderResumeDataCache) {\n            context.renderOpts.renderResumeDataCache =\n              warmup.metadata.renderResumeDataCache\n          }\n        }\n      }\n\n      return routeModule.render(nextReq, nextRes, context).finally(() => {\n        if (!span) return\n\n        span.setAttributes({\n          'http.status_code': res.statusCode,\n          'next.rsc': false,\n        })\n\n        const rootSpanAttributes = tracer.getRootSpanAttributes()\n        // We were unable to get attributes, probably OTEL is not enabled\n        if (!rootSpanAttributes) {\n          return\n        }\n\n        if (\n          rootSpanAttributes.get('next.span_type') !==\n          BaseServerSpan.handleRequest\n        ) {\n          console.warn(\n            `Unexpected root span type '${rootSpanAttributes.get(\n              'next.span_type'\n            )}'. Please report this Next.js issue https://github.com/vercel/next.js`\n          )\n          return\n        }\n\n        const route = rootSpanAttributes.get('next.route')\n        if (route) {\n          const name = `${method} ${route}`\n\n          span.setAttributes({\n            'next.route': route,\n            'http.route': route,\n            'next.span_name': name,\n          })\n          span.updateName(name)\n        } else {\n          span.updateName(`${method} ${req.url}`)\n        }\n      })\n    }\n\n    const doRender = async ({\n      span,\n      postponed,\n      fallbackRouteParams,\n    }: {\n      span?: Span\n      /**\n       * The postponed data for this render. This is only provided when resuming\n       * a render that has been postponed.\n       */\n      postponed: string | undefined\n\n      /**\n       * The unknown route params for this render.\n       */\n      fallbackRouteParams: FallbackRouteParams | null\n    }): Promise<ResponseCacheEntry> => {\n      const context: AppPageRouteHandlerContext = {\n        query,\n        params,\n        page: normalizedSrcPage,\n        sharedContext: {\n          buildId,\n        },\n        serverComponentsHmrCache: getRequestMeta(\n          req,\n          'serverComponentsHmrCache'\n        ),\n        fallbackRouteParams,\n        renderOpts: {\n          App: () => null,\n          Document: () => null,\n          pageConfig: {},\n          ComponentMod,\n          Component: interopDefault(ComponentMod),\n\n          params,\n          routeModule,\n          page: srcPage,\n          postponed,\n          shouldWaitOnAllReady,\n          serveStreamingMetadata,\n          supportsDynamicResponse:\n            typeof postponed === 'string' || supportsDynamicResponse,\n          buildManifest,\n          nextFontManifest,\n          reactLoadableManifest,\n          subresourceIntegrityManifest,\n          serverActionsManifest,\n          clientReferenceManifest,\n          setIsrStatus: routerServerContext?.setIsrStatus,\n\n          dir: routeModule.projectDir,\n          isDraftMode,\n          isRevalidate: isSSG && !postponed && !isDynamicRSCRequest,\n          botType,\n          isOnDemandRevalidate,\n          isPossibleServerAction,\n          assetPrefix: nextConfig.assetPrefix,\n          nextConfigOutput: nextConfig.output,\n          crossOrigin: nextConfig.crossOrigin,\n          trailingSlash: nextConfig.trailingSlash,\n          previewProps: prerenderManifest.preview,\n          deploymentId: nextConfig.deploymentId,\n          enableTainting: nextConfig.experimental.taint,\n          htmlLimitedBots: nextConfig.htmlLimitedBots,\n          devtoolSegmentExplorer:\n            nextConfig.experimental.devtoolSegmentExplorer,\n          reactMaxHeadersLength: nextConfig.reactMaxHeadersLength,\n\n          multiZoneDraftMode,\n          incrementalCache: getRequestMeta(req, 'incrementalCache'),\n          cacheLifeProfiles: nextConfig.experimental.cacheLife,\n          basePath: nextConfig.basePath,\n          serverActions: nextConfig.experimental.serverActions,\n\n          ...(isDebugStaticShell || isDebugDynamicAccesses\n            ? {\n                nextExport: true,\n                supportsDynamicResponse: false,\n                isStaticGeneration: true,\n                isRevalidate: true,\n                isDebugDynamicAccesses: isDebugDynamicAccesses,\n              }\n            : {}),\n\n          experimental: {\n            isRoutePPREnabled,\n            expireTime: nextConfig.expireTime,\n            staleTimes: nextConfig.experimental.staleTimes,\n            dynamicIO: Boolean(nextConfig.experimental.dynamicIO),\n            clientSegmentCache: Boolean(\n              nextConfig.experimental.clientSegmentCache\n            ),\n            dynamicOnHover: Boolean(nextConfig.experimental.dynamicOnHover),\n            inlineCss: Boolean(nextConfig.experimental.inlineCss),\n            authInterrupts: Boolean(nextConfig.experimental.authInterrupts),\n            clientTraceMetadata:\n              nextConfig.experimental.clientTraceMetadata || ([] as any),\n          },\n\n          waitUntil: ctx.waitUntil,\n          onClose: (cb) => {\n            res.on('close', cb)\n          },\n          onAfterTaskError: () => {},\n\n          onInstrumentationRequestError: (error, _request, errorContext) =>\n            routeModule.onRequestError(\n              req,\n              error,\n              errorContext,\n              routerServerContext\n            ),\n          err: getRequestMeta(req, 'invokeError'),\n          dev: routeModule.isDev,\n        },\n      }\n\n      const result = await invokeRouteModule(span, context)\n\n      const { metadata } = result\n\n      const {\n        cacheControl,\n        headers = {},\n        // Add any fetch tags that were on the page to the response headers.\n        fetchTags: cacheTags,\n      } = metadata\n\n      if (cacheTags) {\n        headers[NEXT_CACHE_TAGS_HEADER] = cacheTags\n      }\n\n      // Pull any fetch metrics from the render onto the request.\n      ;(req as any).fetchMetrics = metadata.fetchMetrics\n\n      // we don't throw static to dynamic errors in dev as isSSG\n      // is a best guess in dev since we don't have the prerender pass\n      // to know whether the path is actually static or not\n      if (\n        isSSG &&\n        cacheControl?.revalidate === 0 &&\n        !routeModule.isDev &&\n        !isRoutePPREnabled\n      ) {\n        const staticBailoutInfo = metadata.staticBailoutInfo\n\n        const err = new Error(\n          `Page changed from static to dynamic at runtime ${resolvedPathname}${\n            staticBailoutInfo?.description\n              ? `, reason: ${staticBailoutInfo.description}`\n              : ``\n          }` +\n            `\\nsee more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`\n        )\n\n        if (staticBailoutInfo?.stack) {\n          const stack = staticBailoutInfo.stack\n          err.stack = err.message + stack.substring(stack.indexOf('\\n'))\n        }\n\n        throw err\n      }\n\n      return {\n        value: {\n          kind: CachedRouteKind.APP_PAGE,\n          html: result,\n          headers,\n          rscData: metadata.flightData,\n          postponed: metadata.postponed,\n          status: metadata.statusCode,\n          segmentData: metadata.segmentData,\n        } satisfies CachedAppPageValue,\n        cacheControl,\n      } satisfies ResponseCacheEntry\n    }\n\n    const responseGenerator: ResponseGenerator = async ({\n      hasResolved,\n      previousCacheEntry,\n      isRevalidating,\n      span,\n    }) => {\n      const isProduction = routeModule.isDev === false\n      const didRespond = hasResolved || res.writableEnded\n\n      // skip on-demand revalidate if cache is not present and\n      // revalidate-if-generated is set\n      if (\n        isOnDemandRevalidate &&\n        revalidateOnlyGenerated &&\n        !previousCacheEntry &&\n        !minimalMode\n      ) {\n        if (routerServerContext?.render404) {\n          await routerServerContext.render404(req, res)\n        } else {\n          res.statusCode = 404\n          res.end('This page could not be found')\n        }\n        return null\n      }\n\n      let fallbackMode: FallbackMode | undefined\n\n      if (prerenderInfo) {\n        fallbackMode = parseFallbackField(prerenderInfo.fallback)\n      }\n\n      // When serving a bot request, we want to serve a blocking render and not\n      // the prerendered page. This ensures that the correct content is served\n      // to the bot in the head.\n      if (fallbackMode === FallbackMode.PRERENDER && isBot(userAgent)) {\n        fallbackMode = FallbackMode.BLOCKING_STATIC_RENDER\n      }\n\n      if (previousCacheEntry?.isStale === -1) {\n        isOnDemandRevalidate = true\n      }\n\n      // TODO: adapt for PPR\n      // only allow on-demand revalidate for fallback: true/blocking\n      // or for prerendered fallback: false paths\n      if (\n        isOnDemandRevalidate &&\n        (fallbackMode !== FallbackMode.NOT_FOUND || previousCacheEntry)\n      ) {\n        fallbackMode = FallbackMode.BLOCKING_STATIC_RENDER\n      }\n\n      if (\n        !minimalMode &&\n        fallbackMode !== FallbackMode.BLOCKING_STATIC_RENDER &&\n        staticPathKey &&\n        !didRespond &&\n        !isDraftMode &&\n        pageIsDynamic &&\n        (isProduction || !isPrerendered)\n      ) {\n        // if the page has dynamicParams: false and this pathname wasn't\n        // prerendered trigger the no fallback handling\n        if (\n          // In development, fall through to render to handle missing\n          // getStaticPaths.\n          (isProduction || prerenderInfo) &&\n          // When fallback isn't present, abort this render so we 404\n          fallbackMode === FallbackMode.NOT_FOUND\n        ) {\n          throw new NoFallbackError()\n        }\n\n        let fallbackResponse: ResponseCacheEntry | null | undefined\n\n        if (isRoutePPREnabled && !isRSCRequest) {\n          // We use the response cache here to handle the revalidation and\n          // management of the fallback shell.\n          fallbackResponse = await routeModule.handleResponse({\n            cacheKey: isProduction ? normalizedSrcPage : null,\n            req,\n            nextConfig,\n            routeKind: RouteKind.APP_PAGE,\n            isFallback: true,\n            prerenderManifest,\n            isRoutePPREnabled,\n            responseGenerator: async () =>\n              doRender({\n                span,\n                // We pass `undefined` as rendering a fallback isn't resumed\n                // here.\n                postponed: undefined,\n                fallbackRouteParams:\n                  // If we're in production or we're debugging the fallback\n                  // shell then we should postpone when dynamic params are\n                  // accessed.\n                  isProduction || isDebugFallbackShell\n                    ? getFallbackRouteParams(normalizedSrcPage)\n                    : null,\n              }),\n            waitUntil: ctx.waitUntil,\n          })\n\n          // If the fallback response was set to null, then we should return null.\n          if (fallbackResponse === null) return null\n\n          // Otherwise, if we did get a fallback response, we should return it.\n          if (fallbackResponse) {\n            // Remove the cache control from the response to prevent it from being\n            // used in the surrounding cache.\n            delete fallbackResponse.cacheControl\n\n            return fallbackResponse\n          }\n        }\n      }\n      // Only requests that aren't revalidating can be resumed. If we have the\n      // minimal postponed data, then we should resume the render with it.\n      const postponed =\n        !isOnDemandRevalidate && !isRevalidating && minimalPostponed\n          ? minimalPostponed\n          : undefined\n\n      // When we're in minimal mode, if we're trying to debug the static shell,\n      // we should just return nothing instead of resuming the dynamic render.\n      if (\n        (isDebugStaticShell || isDebugDynamicAccesses) &&\n        typeof postponed !== 'undefined'\n      ) {\n        return {\n          cacheControl: { revalidate: 1, expire: undefined },\n          value: {\n            kind: CachedRouteKind.PAGES,\n            html: RenderResult.fromStatic(''),\n            pageData: {},\n            headers: undefined,\n            status: undefined,\n          } satisfies CachedPageValue,\n        }\n      }\n\n      // If this is a dynamic route with PPR enabled and the default route\n      // matches were set, then we should pass the fallback route params to\n      // the renderer as this is a fallback revalidation request.\n      const fallbackRouteParams =\n        pageIsDynamic &&\n        isRoutePPREnabled &&\n        (getRequestMeta(req, 'renderFallbackShell') || isDebugFallbackShell)\n          ? getFallbackRouteParams(pathname)\n          : null\n\n      // Perform the render.\n      return doRender({\n        span,\n        postponed,\n        fallbackRouteParams,\n      })\n    }\n\n    const handleResponse = async (span?: Span): Promise<null | void> => {\n      const cacheEntry = await routeModule.handleResponse({\n        cacheKey: ssgCacheKey,\n        responseGenerator: (c) =>\n          responseGenerator({\n            span,\n            ...c,\n          }),\n        routeKind: RouteKind.APP_PAGE,\n        isOnDemandRevalidate,\n        isRoutePPREnabled,\n        req,\n        nextConfig,\n        prerenderManifest,\n        waitUntil: ctx.waitUntil,\n      })\n\n      if (isDraftMode) {\n        res.setHeader(\n          'Cache-Control',\n          'private, no-cache, no-store, max-age=0, must-revalidate'\n        )\n      }\n\n      // In dev, we should not cache pages for any reason.\n      if (routeModule.isDev) {\n        res.setHeader('Cache-Control', 'no-store, must-revalidate')\n      }\n\n      if (!cacheEntry) {\n        if (ssgCacheKey) {\n          // A cache entry might not be generated if a response is written\n          // in `getInitialProps` or `getServerSideProps`, but those shouldn't\n          // have a cache key. If we do have a cache key but we don't end up\n          // with a cache entry, then either Next.js or the application has a\n          // bug that needs fixing.\n          throw new Error('invariant: cache entry required but not generated')\n        }\n        return null\n      }\n\n      if (cacheEntry.value?.kind !== CachedRouteKind.APP_PAGE) {\n        throw new Error(\n          `Invariant app-page handler received invalid cache entry ${cacheEntry.value?.kind}`\n        )\n      }\n\n      const didPostpone = typeof cacheEntry.value.postponed === 'string'\n\n      if (\n        isSSG &&\n        // We don't want to send a cache header for requests that contain dynamic\n        // data. If this is a Dynamic RSC request or wasn't a Prefetch RSC\n        // request, then we should set the cache header.\n        !isDynamicRSCRequest &&\n        (!didPostpone || isPrefetchRSCRequest)\n      ) {\n        if (!minimalMode) {\n          // set x-nextjs-cache header to match the header\n          // we set for the image-optimizer\n          res.setHeader(\n            'x-nextjs-cache',\n            isOnDemandRevalidate\n              ? 'REVALIDATED'\n              : cacheEntry.isMiss\n                ? 'MISS'\n                : cacheEntry.isStale\n                  ? 'STALE'\n                  : 'HIT'\n          )\n        }\n        // Set a header used by the client router to signal the response is static\n        // and should respect the `static` cache staleTime value.\n        res.setHeader(NEXT_IS_PRERENDER_HEADER, '1')\n      }\n      const { value: cachedData } = cacheEntry\n\n      // Coerce the cache control parameter from the render.\n      let cacheControl: CacheControl | undefined\n\n      // If this is a resume request in minimal mode it is streamed with dynamic\n      // content and should not be cached.\n      if (minimalPostponed) {\n        cacheControl = { revalidate: 0, expire: undefined }\n      }\n\n      // If this is in minimal mode and this is a flight request that isn't a\n      // prefetch request while PPR is enabled, it cannot be cached as it contains\n      // dynamic content.\n      else if (\n        minimalMode &&\n        isRSCRequest &&\n        !isPrefetchRSCRequest &&\n        isRoutePPREnabled\n      ) {\n        cacheControl = { revalidate: 0, expire: undefined }\n      } else if (!routeModule.isDev) {\n        // If this is a preview mode request, we shouldn't cache it\n        if (isDraftMode) {\n          cacheControl = { revalidate: 0, expire: undefined }\n        }\n\n        // If this isn't SSG, then we should set change the header only if it is\n        // not set already.\n        else if (!isSSG) {\n          if (!res.getHeader('Cache-Control')) {\n            cacheControl = { revalidate: 0, expire: undefined }\n          }\n        } else if (cacheEntry.cacheControl) {\n          // If the cache entry has a cache control with a revalidate value that's\n          // a number, use it.\n          if (typeof cacheEntry.cacheControl.revalidate === 'number') {\n            if (cacheEntry.cacheControl.revalidate < 1) {\n              throw new Error(\n                `Invalid revalidate configuration provided: ${cacheEntry.cacheControl.revalidate} < 1`\n              )\n            }\n\n            cacheControl = {\n              revalidate: cacheEntry.cacheControl.revalidate,\n              expire: cacheEntry.cacheControl?.expire ?? nextConfig.expireTime,\n            }\n          }\n          // Otherwise if the revalidate value is false, then we should use the\n          // cache time of one year.\n          else {\n            cacheControl = { revalidate: CACHE_ONE_YEAR, expire: undefined }\n          }\n        }\n      }\n\n      cacheEntry.cacheControl = cacheControl\n\n      if (\n        typeof segmentPrefetchHeader === 'string' &&\n        cachedData?.kind === CachedRouteKind.APP_PAGE &&\n        cachedData.segmentData\n      ) {\n        // This is a prefetch request issued by the client Segment Cache. These\n        // should never reach the application layer (lambda). We should either\n        // respond from the cache (HIT) or respond with 204 No Content (MISS).\n\n        // Set a header to indicate that PPR is enabled for this route. This\n        // lets the client distinguish between a regular cache miss and a cache\n        // miss due to PPR being disabled. In other contexts this header is used\n        // to indicate that the response contains dynamic data, but here we're\n        // only using it to indicate that the feature is enabled — the segment\n        // response itself contains whether the data is dynamic.\n        res.setHeader(NEXT_DID_POSTPONE_HEADER, '2')\n\n        // Add the cache tags header to the response if it exists and we're in\n        // minimal mode while rendering a static page.\n        const tags = cachedData.headers?.[NEXT_CACHE_TAGS_HEADER]\n        if (minimalMode && isSSG && tags && typeof tags === 'string') {\n          res.setHeader(NEXT_CACHE_TAGS_HEADER, tags)\n        }\n\n        const matchedSegment = cachedData.segmentData.get(segmentPrefetchHeader)\n        if (matchedSegment !== undefined) {\n          // Cache hit\n          return sendRenderResult({\n            req,\n            res,\n            type: 'rsc',\n            generateEtags: nextConfig.generateEtags,\n            poweredByHeader: nextConfig.poweredByHeader,\n            result: RenderResult.fromStatic(matchedSegment),\n            cacheControl: cacheEntry.cacheControl,\n          })\n        }\n\n        // Cache miss. Either a cache entry for this route has not been generated\n        // (which technically should not be possible when PPR is enabled, because\n        // at a minimum there should always be a fallback entry) or there's no\n        // match for the requested segment. Respond with a 204 No Content. We\n        // don't bother to respond with 404, because these requests are only\n        // issued as part of a prefetch.\n        res.statusCode = 204\n        return sendRenderResult({\n          req,\n          res,\n          type: 'rsc',\n          generateEtags: nextConfig.generateEtags,\n          poweredByHeader: nextConfig.poweredByHeader,\n          result: RenderResult.fromStatic(''),\n          cacheControl: cacheEntry.cacheControl,\n        })\n      }\n\n      // If there's a callback for `onCacheEntry`, call it with the cache entry\n      // and the revalidate options.\n      const onCacheEntry = getRequestMeta(req, 'onCacheEntry')\n      if (onCacheEntry) {\n        const finished = await onCacheEntry(\n          {\n            ...cacheEntry,\n            // TODO: remove this when upstream doesn't\n            // always expect this value to be \"PAGE\"\n            value: {\n              ...cacheEntry.value,\n              kind: 'PAGE',\n            },\n          },\n          {\n            url: getRequestMeta(req, 'initURL'),\n          }\n        )\n        if (finished) {\n          // TODO: maybe we have to end the request?\n          return null\n        }\n      }\n\n      // If the request has a postponed state and it's a resume request we\n      // should error.\n      if (didPostpone && minimalPostponed) {\n        throw new Error(\n          'Invariant: postponed state should not be present on a resume request'\n        )\n      }\n\n      if (cachedData.headers) {\n        const headers = { ...cachedData.headers }\n\n        if (!minimalMode || !isSSG) {\n          delete headers[NEXT_CACHE_TAGS_HEADER]\n        }\n\n        for (let [key, value] of Object.entries(headers)) {\n          if (typeof value === 'undefined') continue\n\n          if (Array.isArray(value)) {\n            for (const v of value) {\n              res.appendHeader(key, v)\n            }\n          } else if (typeof value === 'number') {\n            value = value.toString()\n            res.appendHeader(key, value)\n          } else {\n            res.appendHeader(key, value)\n          }\n        }\n      }\n\n      // Add the cache tags header to the response if it exists and we're in\n      // minimal mode while rendering a static page.\n      const tags = cachedData.headers?.[NEXT_CACHE_TAGS_HEADER]\n      if (minimalMode && isSSG && tags && typeof tags === 'string') {\n        res.setHeader(NEXT_CACHE_TAGS_HEADER, tags)\n      }\n\n      // If the request is a data request, then we shouldn't set the status code\n      // from the response because it should always be 200. This should be gated\n      // behind the experimental PPR flag.\n      if (cachedData.status && (!isRSCRequest || !isRoutePPREnabled)) {\n        res.statusCode = cachedData.status\n      }\n\n      // Redirect information is encoded in RSC payload, so we don't need to use redirect status codes\n      if (\n        !minimalMode &&\n        cachedData.status &&\n        RedirectStatusCode[cachedData.status] &&\n        isRSCRequest\n      ) {\n        res.statusCode = 200\n      }\n\n      // Mark that the request did postpone.\n      if (didPostpone) {\n        res.setHeader(NEXT_DID_POSTPONE_HEADER, '1')\n      }\n\n      // we don't go through this block when preview mode is true\n      // as preview mode is a dynamic request (bypasses cache) and doesn't\n      // generate both HTML and payloads in the same request so continue to just\n      // return the generated payload\n      if (isRSCRequest && !isDraftMode) {\n        // If this is a dynamic RSC request, then stream the response.\n        if (typeof cachedData.rscData === 'undefined') {\n          if (cachedData.postponed) {\n            throw new Error('Invariant: Expected postponed to be undefined')\n          }\n\n          return sendRenderResult({\n            req,\n            res,\n            type: 'rsc',\n            generateEtags: nextConfig.generateEtags,\n            poweredByHeader: nextConfig.poweredByHeader,\n            result: cachedData.html,\n            // Dynamic RSC responses cannot be cached, even if they're\n            // configured with `force-static` because we have no way of\n            // distinguishing between `force-static` and pages that have no\n            // postponed state.\n            // TODO: distinguish `force-static` from pages with no postponed state (static)\n            cacheControl: isDynamicRSCRequest\n              ? { revalidate: 0, expire: undefined }\n              : cacheEntry.cacheControl,\n          })\n        }\n\n        // As this isn't a prefetch request, we should serve the static flight\n        // data.\n        return sendRenderResult({\n          req,\n          res,\n          type: 'rsc',\n          generateEtags: nextConfig.generateEtags,\n          poweredByHeader: nextConfig.poweredByHeader,\n          result: RenderResult.fromStatic(cachedData.rscData),\n          cacheControl: cacheEntry.cacheControl,\n        })\n      }\n\n      // This is a request for HTML data.\n      let body = cachedData.html\n\n      // If there's no postponed state, we should just serve the HTML. This\n      // should also be the case for a resume request because it's completed\n      // as a server render (rather than a static render).\n      if (!didPostpone || minimalMode) {\n        return sendRenderResult({\n          req,\n          res,\n          type: 'html',\n          generateEtags: nextConfig.generateEtags,\n          poweredByHeader: nextConfig.poweredByHeader,\n          result: body,\n          cacheControl: cacheEntry.cacheControl,\n        })\n      }\n\n      // If we're debugging the static shell or the dynamic API accesses, we\n      // should just serve the HTML without resuming the render. The returned\n      // HTML will be the static shell so all the Dynamic API's will be used\n      // during static generation.\n      if (isDebugStaticShell || isDebugDynamicAccesses) {\n        // Since we're not resuming the render, we need to at least add the\n        // closing body and html tags to create valid HTML.\n        body.chain(\n          new ReadableStream({\n            start(controller) {\n              controller.enqueue(ENCODED_TAGS.CLOSED.BODY_AND_HTML)\n              controller.close()\n            },\n          })\n        )\n\n        return sendRenderResult({\n          req,\n          res,\n          type: 'html',\n          generateEtags: nextConfig.generateEtags,\n          poweredByHeader: nextConfig.poweredByHeader,\n          result: body,\n          cacheControl: { revalidate: 0, expire: undefined },\n        })\n      }\n\n      // This request has postponed, so let's create a new transformer that the\n      // dynamic data can pipe to that will attach the dynamic data to the end\n      // of the response.\n      const transformer = new TransformStream<Uint8Array, Uint8Array>()\n      body.chain(transformer.readable)\n\n      // Perform the render again, but this time, provide the postponed state.\n      // We don't await because we want the result to start streaming now, and\n      // we've already chained the transformer's readable to the render result.\n      doRender({\n        span,\n        postponed: cachedData.postponed,\n        // This is a resume render, not a fallback render, so we don't need to\n        // set this.\n        fallbackRouteParams: null,\n      })\n        .then(async (result) => {\n          if (!result) {\n            throw new Error('Invariant: expected a result to be returned')\n          }\n\n          if (result.value?.kind !== CachedRouteKind.APP_PAGE) {\n            throw new Error(\n              `Invariant: expected a page response, got ${result.value?.kind}`\n            )\n          }\n\n          // Pipe the resume result to the transformer.\n          await result.value.html.pipeTo(transformer.writable)\n        })\n        .catch((err) => {\n          // An error occurred during piping or preparing the render, abort\n          // the transformers writer so we can terminate the stream.\n          transformer.writable.abort(err).catch((e) => {\n            console.error(\"couldn't abort transformer\", e)\n          })\n        })\n\n      return sendRenderResult({\n        req,\n        res,\n        type: 'html',\n        generateEtags: nextConfig.generateEtags,\n        poweredByHeader: nextConfig.poweredByHeader,\n        result: body,\n        // We don't want to cache the response if it has postponed data because\n        // the response being sent to the client it's dynamic parts are streamed\n        // to the client on the same request.\n        cacheControl: { revalidate: 0, expire: undefined },\n      })\n    }\n\n    // TODO: activeSpan code path is for when wrapped by\n    // next-server can be removed when this is no longer used\n    if (activeSpan) {\n      await handleResponse(activeSpan)\n    } else {\n      return await tracer.withPropagatedContext(req.headers, () =>\n        tracer.trace(\n          BaseServerSpan.handleRequest,\n          {\n            spanName: `${method} ${req.url}`,\n            kind: SpanKind.SERVER,\n            attributes: {\n              'http.method': method,\n              'http.target': req.url,\n            },\n          },\n          handleResponse\n        )\n      )\n    }\n  } catch (err) {\n    // if we aren't wrapped by base-server handle here\n    if (!activeSpan) {\n      await routeModule.onRequestError(\n        req,\n        err,\n        {\n          routerKind: 'App Router',\n          routePath: srcPage,\n          routeType: 'render',\n          revalidateReason: getRevalidateReason({\n            isRevalidate: isSSG,\n            isOnDemandRevalidate,\n          }),\n        },\n        routerServerContext\n      )\n    }\n\n    // rethrow so that we can handle serving error page\n    throw err\n  }\n}\n"], "names": ["GlobalError", "__next_app__", "handler", "pages", "routeModule", "tree", "require", "__next_app_require__", "loadChunk", "__next_app_load_chunk__", "AppPageRouteModule", "definition", "kind", "RouteKind", "APP_PAGE", "page", "pathname", "bundlePath", "filename", "appPaths", "userland", "loaderTree", "distDir", "process", "env", "__NEXT_RELATIVE_DIST_DIR", "projectDir", "__NEXT_RELATIVE_PROJECT_DIR", "req", "res", "ctx", "prerenderManifest", "srcPage", "TURBOPACK", "replace", "multiZoneDraftMode", "__NEXT_MULTI_ZONE_DRAFT_MODE", "initialPostponed", "getRequestMeta", "minimalMode", "prepareResult", "prepare", "statusCode", "end", "waitUntil", "Promise", "resolve", "buildId", "query", "params", "parsedUrl", "pageIsDynamic", "buildManifest", "nextFontManifest", "reactLoadableManifest", "serverActionsManifest", "clientReferenceManifest", "subresourceIntegrityManifest", "isDraftMode", "resolvedPathname", "revalidateOnlyGenerated", "routerServerContext", "nextConfig", "normalizedSrcPage", "normalizeAppPath", "isOnDemandRevalidate", "prerenderInfo", "dynamicRoutes", "isP<PERSON>endered", "routes", "isSSG", "Boolean", "userAgent", "headers", "botType", "getBotType", "isHtmlBot", "isHtmlBotRequest", "isPrefetchRSCRequest", "NEXT_ROUTER_PREFETCH_HEADER", "isRSCRequest", "RSC_HEADER", "isPossibleServerAction", "getIsPossibleServerAction", "couldSupportPPR", "checkIsAppPPREnabled", "experimental", "ppr", "hasDebugStaticShellQuery", "__NEXT_EXPERIMENTAL_STATIC_SHELL_DEBUGGING", "__nextppronly", "hasDebugFallbackShellQuery", "isRoutePPREnabled", "renderingMode", "isDev", "experimentalTestProxy", "isDebugStaticShell", "isDebugDynamicAccesses", "isDebugFallbackShell", "minimalPostponed", "undefined", "isDynamicRSCRequest", "segmentPrefetchHeader", "serveStreamingMetadata", "shouldServeStreamingMetadata", "htmlLimitedBots", "supportsDynamicResponse", "shouldWaitOnAllReady", "ssgCacheKey", "static<PERSON><PERSON><PERSON><PERSON>", "ComponentMod", "entryBase", "setReferenceManifestsSingleton", "serverModuleMap", "createServerModuleMap", "method", "tracer", "getTracer", "activeSpan", "getActiveScopeSpan", "invokeRouteModule", "span", "context", "nextReq", "NodeNextRequest", "nextRes", "NodeNextResponse", "NODE_ENV", "dynamicIO", "renderOpts", "warmup", "metadata", "renderResumeDataCache", "render", "finally", "setAttributes", "rootSpanAttributes", "getRootSpanAttributes", "get", "BaseServerSpan", "handleRequest", "console", "warn", "route", "name", "updateName", "url", "doR<PERSON>", "postponed", "fallbackRouteParams", "sharedContext", "serverComponentsHmrCache", "App", "Document", "pageConfig", "Component", "interopDefault", "setIsrStatus", "dir", "isRevalidate", "assetPrefix", "nextConfigOutput", "output", "crossOrigin", "trailingSlash", "previewProps", "preview", "deploymentId", "enableTainting", "taint", "devtoolSegmentExplorer", "reactMaxHeadersLength", "incrementalCache", "cacheLifeProfiles", "cacheLife", "basePath", "serverActions", "nextExport", "isStaticGeneration", "expireTime", "staleTimes", "clientSegmentCache", "dynamicOnHover", "inlineCss", "authInterrupts", "clientTraceMetadata", "onClose", "cb", "on", "onAfterTaskError", "onInstrumentationRequestError", "error", "_request", "errorContext", "onRequestError", "err", "dev", "result", "cacheControl", "fetchTags", "cacheTags", "NEXT_CACHE_TAGS_HEADER", "fetchMetrics", "revalidate", "staticBailoutInfo", "Error", "description", "stack", "message", "substring", "indexOf", "value", "CachedRouteKind", "html", "rscData", "flightData", "status", "segmentData", "responseGenerator", "hasResolved", "previousCacheEntry", "isRevalidating", "isProduction", "didRespond", "writableEnded", "render404", "fallbackMode", "parseFallbackField", "fallback", "FallbackMode", "PRERENDER", "isBot", "BLOCKING_STATIC_RENDER", "isStale", "NOT_FOUND", "NoFallbackError", "fallbackResponse", "handleResponse", "cache<PERSON>ey", "routeKind", "<PERSON><PERSON><PERSON><PERSON>", "getFallbackRouteParams", "expire", "PAGES", "RenderResult", "fromStatic", "pageData", "cacheEntry", "cachedData", "c", "<PERSON><PERSON><PERSON><PERSON>", "didPostpone", "isMiss", "NEXT_IS_PRERENDER_HEADER", "<PERSON><PERSON><PERSON><PERSON>", "CACHE_ONE_YEAR", "NEXT_DID_POSTPONE_HEADER", "tags", "matchedSegment", "sendRenderResult", "type", "generateEtags", "poweredByHeader", "onCacheEntry", "finished", "key", "Object", "entries", "Array", "isArray", "v", "append<PERSON><PERSON>er", "toString", "RedirectStatusCode", "body", "chain", "ReadableStream", "start", "controller", "enqueue", "ENCODED_TAGS", "CLOSED", "BODY_AND_HTML", "close", "transformer", "TransformStream", "readable", "then", "pipeTo", "writable", "catch", "abort", "e", "withPropagatedContext", "trace", "spanName", "SpanKind", "SERVER", "attributes", "routerKind", "routePath", "routeType", "revalidateReason", "getRevalidateReason"], "mappings": ";;;;;;;;;;;;;;;;;;;IAqESA,WAAW;eAAXA,gCAAW;;IASPC,YAAY;eAAZA;;IA4BSC,OAAO;eAAPA;;IAzCPC,KAAK;eAALA;;IAwBFC,WAAW;eAAXA;;IAxBJC,IAAI;eAAJA;;;;gCA3DF;2BAEmB;uBAEU;wBACW;6BAChB;2BACA;gCACA;sBACmB;qBACb;gCAI9B;iCACwC;mCAIxC;6BAC+B;0BACL;yCACS;kCAMnC;uBAC2B;+BAO3B;0BAC0C;qEACxB;4BAC8B;6BAE1B;6BACI;yCACD;gFAkBR;gFAgBG;oCACQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAN5B,MAAMJ,eAAe;IAC1BK,SAASC;IACTC,WAAWC;AACb;AAQO,MAAML,cAAc,IAAIM,kCAAkB,CAAC;IAChDC,YAAY;QACVC,MAAMC,oBAAS,CAACC,QAAQ;QACxBC,MAAM;QACNC,UAAU;QACV,2CAA2C;QAC3CC,YAAY;QACZC,UAAU;QACVC,UAAU,EAAE;IACd;IACAC,UAAU;QACRC,YAAYhB;IACd;IACAiB,SAASC,QAAQC,GAAG,CAACC,wBAAwB,IAAI;IACjDC,YAAYH,QAAQC,GAAG,CAACG,2BAA2B,IAAI;AACzD;AAEO,eAAezB,QACpB0B,GAAoB,EACpBC,GAAmB,EACnBC,GAEC;QA8GGC;IA5GJ,IAAIC,UAAU;IAEd,wDAAwD;IACxD,mDAAmD;IACnD,6DAA6D;IAC7D,IAAIT,QAAQC,GAAG,CAACS,SAAS,EAAE;QACzBD,UAAUA,QAAQE,OAAO,CAAC,YAAY,OAAO;IAC/C,OAAO,IAAIF,YAAY,UAAU;QAC/B,0CAA0C;QAC1CA,UAAU;IACZ;IACA,MAAMG,qBAAqBZ,QAAQC,GAAG,CACnCY,4BAA4B;IAE/B,MAAMC,mBAAmBC,IAAAA,2BAAc,EAACV,KAAK;IAC7C,yCAAyC;IACzC,MAAMW,cAAcD,IAAAA,2BAAc,EAACV,KAAK;IAExC,MAAMY,gBAAgB,MAAMpC,YAAYqC,OAAO,CAACb,KAAKC,KAAK;QACxDG;QACAG;IACF;IAEA,IAAI,CAACK,eAAe;QAClBX,IAAIa,UAAU,GAAG;QACjBb,IAAIc,GAAG,CAAC;QACRb,IAAIc,SAAS,oBAAbd,IAAIc,SAAS,MAAbd,KAAgBe,QAAQC,OAAO;QAC/B,OAAO;IACT;IAEA,MAAM,EACJC,OAAO,EACPC,KAAK,EACLC,MAAM,EACNC,SAAS,EACTC,aAAa,EACbC,aAAa,EACbC,gBAAgB,EAChBC,qBAAqB,EACrBC,qBAAqB,EACrBC,uBAAuB,EACvBC,4BAA4B,EAC5B1B,iBAAiB,EACjB2B,WAAW,EACXC,gBAAgB,EAChBC,uBAAuB,EACvBC,mBAAmB,EACnBC,UAAU,EACX,GAAGtB;IAEJ,MAAMxB,WAAWkC,UAAUlC,QAAQ,IAAI;IACvC,MAAM+C,oBAAoBC,IAAAA,0BAAgB,EAAChC;IAE3C,IAAI,EAAEiC,oBAAoB,EAAE,GAAGzB;IAE/B,MAAM0B,gBAAgBnC,kBAAkBoC,aAAa,CAACJ,kBAAkB;IACxE,MAAMK,gBAAgBrC,kBAAkBsC,MAAM,CAACV,iBAAiB;IAEhE,IAAIW,QAAQC,QACVL,iBACEE,iBACArC,kBAAkBsC,MAAM,CAACN,kBAAkB;IAG/C,MAAMS,YAAY5C,IAAI6C,OAAO,CAAC,aAAa,IAAI;IAC/C,MAAMC,UAAUC,IAAAA,iBAAU,EAACH;IAC3B,MAAMI,YAAYC,IAAAA,mCAAgB,EAACjD;IAEnC;;;GAGC,GACD,MAAMkD,uBACJxC,IAAAA,2BAAc,EAACV,KAAK,2BACpB2C,QAAQ3C,IAAI6C,OAAO,CAACM,6CAA2B,CAAC;IAElD,uFAAuF;IAEvF,MAAMC,eACJ1C,IAAAA,2BAAc,EAACV,KAAK,mBAAmB2C,QAAQ3C,IAAI6C,OAAO,CAACQ,4BAAU,CAAC;IAExE,MAAMC,yBAAyBC,IAAAA,kDAAyB,EAACvD;IAEzD;;;GAGC,GACD,MAAMwD,kBAA2BC,IAAAA,yBAAoB,EACnDvB,WAAWwB,YAAY,CAACC,GAAG;IAG7B,yEAAyE;IACzE,wCAAwC;IACxC,MAAMC,2BACJjE,QAAQC,GAAG,CAACiE,0CAA0C,KAAK,OAC3D,OAAOzC,MAAM0C,aAAa,KAAK,eAC/BN;IAEF,sEAAsE;IACtE,6CAA6C;IAC7C,MAAMO,6BACJH,4BAA4BxC,MAAM0C,aAAa,KAAK;IAEtD,4EAA4E;IAC5E,8CAA8C;IAC9C,MAAME,oBACJR,mBACC,CAAA,EACCrD,QAAAA,kBAAkBsC,MAAM,CAACN,kBAAkB,IAC3ChC,kBAAkBoC,aAAa,CAACJ,kBAAkB,qBAFnD,AACChC,MAEC8D,aAAa,MAAK,sBACnB,uEAAuE;IACvE,wEAAwE;IACxE,wEAAwE;IACxE,+BAA+B;IAC9BL,4BACEpF,CAAAA,YAAY0F,KAAK,KAAK,QACrBjC,CAAAA,uCAAAA,oBAAqBkC,qBAAqB,MAAK,IAAG,CAAE;IAE5D,MAAMC,qBACJR,4BAA4BI;IAE9B,oEAAoE;IACpE,iEAAiE;IACjE,MAAMK,yBACJD,sBAAsB5F,YAAY0F,KAAK,KAAK;IAE9C,MAAMI,uBAAuBP,8BAA8BC;IAE3D,2EAA2E;IAC3E,wEAAwE;IACxE,UAAU;IACV,MAAMO,mBAAmBP,oBAAoBvD,mBAAmB+D;IAEhE,0EAA0E;IAC1E,wEAAwE;IACxE,0DAA0D;IAC1D,MAAMC,sBACJT,qBAAqBZ,gBAAgB,CAACF;IAExC,yEAAyE;IACzE,iEAAiE;IACjE,yEAAyE;IACzE,yEAAyE;IACzE,MAAMwB,wBAAwBhE,IAAAA,2BAAc,EAACV,KAAK;IAElD,0EAA0E;IAC1E,+EAA+E;IAC/E,2EAA2E;IAC3E,+CAA+C;IAC/C,IAAI2E,yBAAyB,CAAC/B,YAC1B,OACAgC,IAAAA,+CAA4B,EAAChC,WAAWV,WAAW2C,eAAe;IAEtE,IAAI7B,aAAagB,mBAAmB;QAClCtB,QAAQ;QACRiC,yBAAyB;IAC3B;IAEA,2DAA2D;IAC3D,IAAIG,0BACF,uEAAuE;IACvE,6DAA6D;IAC7DtG,YAAY0F,KAAK,KAAK,QACtB,qEAAqE;IACrE,gBAAgB;IAChB,CAACxB,SACD,mEAAmE;IACnE,QAAQ;IACR,OAAOjC,qBAAqB,YAC5B,sEAAsE;IACtE,uBAAuB;IACvBgE;IAEF,uEAAuE;IACvE,MAAMM,uBAAuB/B,aAAagB;IAE1C,IAAIgB,cAA6B;IACjC,IACE,CAAClD,eACDY,SACA,CAACoC,2BACD,CAACxB,0BACD,CAACiB,oBACD,CAACE,qBACD;QACAO,cAAcjD;IAChB;IAEA,mDAAmD;IACnD,6DAA6D;IAC7D,8DAA8D;IAC9D,oCAAoC;IACpC,IAAIkD,gBAAgBD;IACpB,IAAI,CAACC,iBAAiBzG,YAAY0F,KAAK,EAAE;QACvCe,gBAAgBlD;IAClB;IAEA,MAAMmD,eAAe;QACnB,GAAGC,UAAS;QACZ1G;QACAF;QACAH,aAAAA,gCAAW;QACXE;QACAE;QACAH;IACF;IAEA,0EAA0E;IAC1E,qEAAqE;IACrE,0EAA0E;IAC1E,IAAIsD,yBAAyBC,yBAAyB;QACpDwD,IAAAA,+CAA8B,EAAC;YAC7BjG,MAAMiB;YACNwB;YACAD;YACA0D,iBAAiBC,IAAAA,kCAAqB,EAAC;gBACrC3D;YACF;QACF;IACF;IAEA,MAAM4D,SAASvF,IAAIuF,MAAM,IAAI;IAC7B,MAAMC,SAASC,IAAAA,iBAAS;IACxB,MAAMC,aAAaF,OAAOG,kBAAkB;IAE5C,IAAI;QACF,MAAMC,oBAAoB,OACxBC,MACAC;YAEA,MAAMC,UAAU,IAAIC,qBAAe,CAAChG;YACpC,MAAMiG,UAAU,IAAIC,sBAAgB,CAACjG;YAErC,4DAA4D;YAC5D,iEAAiE;YACjE,wCAAwC;YACxC,IAAIN,QAAQC,GAAG,CAACuG,QAAQ,KAAK,eAAe;gBAC1C,IACEjE,WAAWwB,YAAY,CAAC0C,SAAS,IACjC,CAAClD,wBACD,CAAC4C,QAAQO,UAAU,CAAC/C,sBAAsB,EAC1C;oBACA,MAAMgD,SAAS,MAAM9H,YAAY8H,MAAM,CAACP,SAASE,SAASH;oBAE1D,6DAA6D;oBAC7D,yBAAyB;oBACzB,IAAIQ,OAAOC,QAAQ,CAACC,qBAAqB,EAAE;wBACzCV,QAAQO,UAAU,CAACG,qBAAqB,GACtCF,OAAOC,QAAQ,CAACC,qBAAqB;oBACzC;gBACF;YACF;YAEA,OAAOhI,YAAYiI,MAAM,CAACV,SAASE,SAASH,SAASY,OAAO,CAAC;gBAC3D,IAAI,CAACb,MAAM;gBAEXA,KAAKc,aAAa,CAAC;oBACjB,oBAAoB1G,IAAIa,UAAU;oBAClC,YAAY;gBACd;gBAEA,MAAM8F,qBAAqBpB,OAAOqB,qBAAqB;gBACvD,iEAAiE;gBACjE,IAAI,CAACD,oBAAoB;oBACvB;gBACF;gBAEA,IACEA,mBAAmBE,GAAG,CAAC,sBACvBC,yBAAc,CAACC,aAAa,EAC5B;oBACAC,QAAQC,IAAI,CACV,CAAC,2BAA2B,EAAEN,mBAAmBE,GAAG,CAClD,kBACA,qEAAqE,CAAC;oBAE1E;gBACF;gBAEA,MAAMK,QAAQP,mBAAmBE,GAAG,CAAC;gBACrC,IAAIK,OAAO;oBACT,MAAMC,OAAO,GAAG7B,OAAO,CAAC,EAAE4B,OAAO;oBAEjCtB,KAAKc,aAAa,CAAC;wBACjB,cAAcQ;wBACd,cAAcA;wBACd,kBAAkBC;oBACpB;oBACAvB,KAAKwB,UAAU,CAACD;gBAClB,OAAO;oBACLvB,KAAKwB,UAAU,CAAC,GAAG9B,OAAO,CAAC,EAAEvF,IAAIsH,GAAG,EAAE;gBACxC;YACF;QACF;QAEA,MAAMC,WAAW,OAAO,EACtB1B,IAAI,EACJ2B,SAAS,EACTC,mBAAmB,EAapB;YACC,MAAM3B,UAAsC;gBAC1C1E;gBACAC;gBACAlC,MAAMgD;gBACNuF,eAAe;oBACbvG;gBACF;gBACAwG,0BAA0BjH,IAAAA,2BAAc,EACtCV,KACA;gBAEFyH;gBACApB,YAAY;oBACVuB,KAAK,IAAM;oBACXC,UAAU,IAAM;oBAChBC,YAAY,CAAC;oBACb5C;oBACA6C,WAAWC,IAAAA,8BAAc,EAAC9C;oBAE1B7D;oBACA7C;oBACAW,MAAMiB;oBACNoH;oBACAzC;oBACAJ;oBACAG,yBACE,OAAO0C,cAAc,YAAY1C;oBACnCtD;oBACAC;oBACAC;oBACAG;oBACAF;oBACAC;oBACAqG,YAAY,EAAEhG,uCAAAA,oBAAqBgG,YAAY;oBAE/CC,KAAK1J,YAAYsB,UAAU;oBAC3BgC;oBACAqG,cAAczF,SAAS,CAAC8E,aAAa,CAAC/C;oBACtC3B;oBACAT;oBACAiB;oBACA8E,aAAalG,WAAWkG,WAAW;oBACnCC,kBAAkBnG,WAAWoG,MAAM;oBACnCC,aAAarG,WAAWqG,WAAW;oBACnCC,eAAetG,WAAWsG,aAAa;oBACvCC,cAActI,kBAAkBuI,OAAO;oBACvCC,cAAczG,WAAWyG,YAAY;oBACrCC,gBAAgB1G,WAAWwB,YAAY,CAACmF,KAAK;oBAC7ChE,iBAAiB3C,WAAW2C,eAAe;oBAC3CiE,wBACE5G,WAAWwB,YAAY,CAACoF,sBAAsB;oBAChDC,uBAAuB7G,WAAW6G,qBAAqB;oBAEvDxI;oBACAyI,kBAAkBtI,IAAAA,2BAAc,EAACV,KAAK;oBACtCiJ,mBAAmB/G,WAAWwB,YAAY,CAACwF,SAAS;oBACpDC,UAAUjH,WAAWiH,QAAQ;oBAC7BC,eAAelH,WAAWwB,YAAY,CAAC0F,aAAa;oBAEpD,GAAIhF,sBAAsBC,yBACtB;wBACEgF,YAAY;wBACZvE,yBAAyB;wBACzBwE,oBAAoB;wBACpBnB,cAAc;wBACd9D,wBAAwBA;oBAC1B,IACA,CAAC,CAAC;oBAENX,cAAc;wBACZM;wBACAuF,YAAYrH,WAAWqH,UAAU;wBACjCC,YAAYtH,WAAWwB,YAAY,CAAC8F,UAAU;wBAC9CpD,WAAWzD,QAAQT,WAAWwB,YAAY,CAAC0C,SAAS;wBACpDqD,oBAAoB9G,QAClBT,WAAWwB,YAAY,CAAC+F,kBAAkB;wBAE5CC,gBAAgB/G,QAAQT,WAAWwB,YAAY,CAACgG,cAAc;wBAC9DC,WAAWhH,QAAQT,WAAWwB,YAAY,CAACiG,SAAS;wBACpDC,gBAAgBjH,QAAQT,WAAWwB,YAAY,CAACkG,cAAc;wBAC9DC,qBACE3H,WAAWwB,YAAY,CAACmG,mBAAmB,IAAK,EAAE;oBACtD;oBAEA7I,WAAWd,IAAIc,SAAS;oBACxB8I,SAAS,CAACC;wBACR9J,IAAI+J,EAAE,CAAC,SAASD;oBAClB;oBACAE,kBAAkB,KAAO;oBAEzBC,+BAA+B,CAACC,OAAOC,UAAUC,eAC/C7L,YAAY8L,cAAc,CACxBtK,KACAmK,OACAE,cACApI;oBAEJsI,KAAK7J,IAAAA,2BAAc,EAACV,KAAK;oBACzBwK,KAAKhM,YAAY0F,KAAK;gBACxB;YACF;YAEA,MAAMuG,SAAS,MAAM7E,kBAAkBC,MAAMC;YAE7C,MAAM,EAAES,QAAQ,EAAE,GAAGkE;YAErB,MAAM,EACJC,YAAY,EACZ7H,UAAU,CAAC,CAAC,EACZ,oEAAoE;YACpE8H,WAAWC,SAAS,EACrB,GAAGrE;YAEJ,IAAIqE,WAAW;gBACb/H,OAAO,CAACgI,kCAAsB,CAAC,GAAGD;YACpC;YAEA,2DAA2D;;YACzD5K,IAAY8K,YAAY,GAAGvE,SAASuE,YAAY;YAElD,0DAA0D;YAC1D,gEAAgE;YAChE,qDAAqD;YACrD,IACEpI,SACAgI,CAAAA,gCAAAA,aAAcK,UAAU,MAAK,KAC7B,CAACvM,YAAY0F,KAAK,IAClB,CAACF,mBACD;gBACA,MAAMgH,oBAAoBzE,SAASyE,iBAAiB;gBAEpD,MAAMT,MAAM,qBAOX,CAPW,IAAIU,MACd,CAAC,+CAA+C,EAAElJ,mBAChDiJ,CAAAA,qCAAAA,kBAAmBE,WAAW,IAC1B,CAAC,UAAU,EAAEF,kBAAkBE,WAAW,EAAE,GAC5C,EAAE,EACN,GACA,CAAC,4EAA4E,CAAC,GANtE,qBAAA;2BAAA;gCAAA;kCAAA;gBAOZ;gBAEA,IAAIF,qCAAAA,kBAAmBG,KAAK,EAAE;oBAC5B,MAAMA,QAAQH,kBAAkBG,KAAK;oBACrCZ,IAAIY,KAAK,GAAGZ,IAAIa,OAAO,GAAGD,MAAME,SAAS,CAACF,MAAMG,OAAO,CAAC;gBAC1D;gBAEA,MAAMf;YACR;YAEA,OAAO;gBACLgB,OAAO;oBACLvM,MAAMwM,8BAAe,CAACtM,QAAQ;oBAC9BuM,MAAMhB;oBACN5H;oBACA6I,SAASnF,SAASoF,UAAU;oBAC5BnE,WAAWjB,SAASiB,SAAS;oBAC7BoE,QAAQrF,SAASzF,UAAU;oBAC3B+K,aAAatF,SAASsF,WAAW;gBACnC;gBACAnB;YACF;QACF;QAEA,MAAMoB,oBAAuC,OAAO,EAClDC,WAAW,EACXC,kBAAkB,EAClBC,cAAc,EACdpG,IAAI,EACL;YACC,MAAMqG,eAAe1N,YAAY0F,KAAK,KAAK;YAC3C,MAAMiI,aAAaJ,eAAe9L,IAAImM,aAAa;YAEnD,wDAAwD;YACxD,iCAAiC;YACjC,IACE/J,wBACAL,2BACA,CAACgK,sBACD,CAACrL,aACD;gBACA,IAAIsB,uCAAAA,oBAAqBoK,SAAS,EAAE;oBAClC,MAAMpK,oBAAoBoK,SAAS,CAACrM,KAAKC;gBAC3C,OAAO;oBACLA,IAAIa,UAAU,GAAG;oBACjBb,IAAIc,GAAG,CAAC;gBACV;gBACA,OAAO;YACT;YAEA,IAAIuL;YAEJ,IAAIhK,eAAe;gBACjBgK,eAAeC,IAAAA,4BAAkB,EAACjK,cAAckK,QAAQ;YAC1D;YAEA,yEAAyE;YACzE,wEAAwE;YACxE,0BAA0B;YAC1B,IAAIF,iBAAiBG,sBAAY,CAACC,SAAS,IAAIC,IAAAA,YAAK,EAAC/J,YAAY;gBAC/D0J,eAAeG,sBAAY,CAACG,sBAAsB;YACpD;YAEA,IAAIZ,CAAAA,sCAAAA,mBAAoBa,OAAO,MAAK,CAAC,GAAG;gBACtCxK,uBAAuB;YACzB;YAEA,sBAAsB;YACtB,8DAA8D;YAC9D,2CAA2C;YAC3C,IACEA,wBACCiK,CAAAA,iBAAiBG,sBAAY,CAACK,SAAS,IAAId,kBAAiB,GAC7D;gBACAM,eAAeG,sBAAY,CAACG,sBAAsB;YACpD;YAEA,IACE,CAACjM,eACD2L,iBAAiBG,sBAAY,CAACG,sBAAsB,IACpD3H,iBACA,CAACkH,cACD,CAACrK,eACDP,iBACC2K,CAAAA,gBAAgB,CAAC1J,aAAY,GAC9B;gBACA,gEAAgE;gBAChE,+CAA+C;gBAC/C,IAGE,AAFA,2DAA2D;gBAC3D,kBAAkB;gBACjB0J,CAAAA,gBAAgB5J,aAAY,KAC7B,2DAA2D;gBAC3DgK,iBAAiBG,sBAAY,CAACK,SAAS,EACvC;oBACA,MAAM,IAAIC,wCAAe;gBAC3B;gBAEA,IAAIC;gBAEJ,IAAIhJ,qBAAqB,CAACZ,cAAc;oBACtC,gEAAgE;oBAChE,oCAAoC;oBACpC4J,mBAAmB,MAAMxO,YAAYyO,cAAc,CAAC;wBAClDC,UAAUhB,eAAe/J,oBAAoB;wBAC7CnC;wBACAkC;wBACAiL,WAAWlO,oBAAS,CAACC,QAAQ;wBAC7BkO,YAAY;wBACZjN;wBACA6D;wBACA8H,mBAAmB,UACjBvE,SAAS;gCACP1B;gCACA,4DAA4D;gCAC5D,QAAQ;gCACR2B,WAAWhD;gCACXiD,qBACE,yDAAyD;gCACzD,wDAAwD;gCACxD,YAAY;gCACZyE,gBAAgB5H,uBACZ+I,IAAAA,sCAAsB,EAAClL,qBACvB;4BACR;wBACFnB,WAAWd,IAAIc,SAAS;oBAC1B;oBAEA,wEAAwE;oBACxE,IAAIgM,qBAAqB,MAAM,OAAO;oBAEtC,qEAAqE;oBACrE,IAAIA,kBAAkB;wBACpB,sEAAsE;wBACtE,iCAAiC;wBACjC,OAAOA,iBAAiBtC,YAAY;wBAEpC,OAAOsC;oBACT;gBACF;YACF;YACA,wEAAwE;YACxE,oEAAoE;YACpE,MAAMxF,YACJ,CAACnF,wBAAwB,CAAC4J,kBAAkB1H,mBACxCA,mBACAC;YAEN,yEAAyE;YACzE,wEAAwE;YACxE,IACE,AAACJ,CAAAA,sBAAsBC,sBAAqB,KAC5C,OAAOmD,cAAc,aACrB;gBACA,OAAO;oBACLkD,cAAc;wBAAEK,YAAY;wBAAGuC,QAAQ9I;oBAAU;oBACjD+G,OAAO;wBACLvM,MAAMwM,8BAAe,CAAC+B,KAAK;wBAC3B9B,MAAM+B,qBAAY,CAACC,UAAU,CAAC;wBAC9BC,UAAU,CAAC;wBACX7K,SAAS2B;wBACToH,QAAQpH;oBACV;gBACF;YACF;YAEA,oEAAoE;YACpE,qEAAqE;YACrE,2DAA2D;YAC3D,MAAMiD,sBACJlG,iBACAyC,qBACCtD,CAAAA,IAAAA,2BAAc,EAACV,KAAK,0BAA0BsE,oBAAmB,IAC9D+I,IAAAA,sCAAsB,EAACjO,YACvB;YAEN,sBAAsB;YACtB,OAAOmI,SAAS;gBACd1B;gBACA2B;gBACAC;YACF;QACF;QAEA,MAAMwF,iBAAiB,OAAOpH;gBAyCxB8H,mBA6MSC;YArPb,MAAMD,aAAa,MAAMnP,YAAYyO,cAAc,CAAC;gBAClDC,UAAUlI;gBACV8G,mBAAmB,CAAC+B,IAClB/B,kBAAkB;wBAChBjG;wBACA,GAAGgI,CAAC;oBACN;gBACFV,WAAWlO,oBAAS,CAACC,QAAQ;gBAC7BmD;gBACA2B;gBACAhE;gBACAkC;gBACA/B;gBACAa,WAAWd,IAAIc,SAAS;YAC1B;YAEA,IAAIc,aAAa;gBACf7B,IAAI6N,SAAS,CACX,iBACA;YAEJ;YAEA,oDAAoD;YACpD,IAAItP,YAAY0F,KAAK,EAAE;gBACrBjE,IAAI6N,SAAS,CAAC,iBAAiB;YACjC;YAEA,IAAI,CAACH,YAAY;gBACf,IAAI3I,aAAa;oBACf,gEAAgE;oBAChE,oEAAoE;oBACpE,kEAAkE;oBAClE,mEAAmE;oBACnE,yBAAyB;oBACzB,MAAM,qBAA8D,CAA9D,IAAIiG,MAAM,sDAAV,qBAAA;+BAAA;oCAAA;sCAAA;oBAA6D;gBACrE;gBACA,OAAO;YACT;YAEA,IAAI0C,EAAAA,oBAAAA,WAAWpC,KAAK,qBAAhBoC,kBAAkB3O,IAAI,MAAKwM,8BAAe,CAACtM,QAAQ,EAAE;oBAEMyO;gBAD7D,MAAM,qBAEL,CAFK,IAAI1C,MACR,CAAC,wDAAwD,GAAE0C,qBAAAA,WAAWpC,KAAK,qBAAhBoC,mBAAkB3O,IAAI,EAAE,GAD/E,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YAEA,MAAM+O,cAAc,OAAOJ,WAAWpC,KAAK,CAAC/D,SAAS,KAAK;YAE1D,IACE9E,SACA,yEAAyE;YACzE,kEAAkE;YAClE,gDAAgD;YAChD,CAAC+B,uBACA,CAAA,CAACsJ,eAAe7K,oBAAmB,GACpC;gBACA,IAAI,CAACvC,aAAa;oBAChB,gDAAgD;oBAChD,iCAAiC;oBACjCV,IAAI6N,SAAS,CACX,kBACAzL,uBACI,gBACAsL,WAAWK,MAAM,GACf,SACAL,WAAWd,OAAO,GAChB,UACA;gBAEZ;gBACA,0EAA0E;gBAC1E,yDAAyD;gBACzD5M,IAAI6N,SAAS,CAACG,0CAAwB,EAAE;YAC1C;YACA,MAAM,EAAE1C,OAAOqC,UAAU,EAAE,GAAGD;YAE9B,sDAAsD;YACtD,IAAIjD;YAEJ,0EAA0E;YAC1E,oCAAoC;YACpC,IAAInG,kBAAkB;gBACpBmG,eAAe;oBAAEK,YAAY;oBAAGuC,QAAQ9I;gBAAU;YACpD,OAKK,IACH7D,eACAyC,gBACA,CAACF,wBACDc,mBACA;gBACA0G,eAAe;oBAAEK,YAAY;oBAAGuC,QAAQ9I;gBAAU;YACpD,OAAO,IAAI,CAAChG,YAAY0F,KAAK,EAAE;gBAC7B,2DAA2D;gBAC3D,IAAIpC,aAAa;oBACf4I,eAAe;wBAAEK,YAAY;wBAAGuC,QAAQ9I;oBAAU;gBACpD,OAIK,IAAI,CAAC9B,OAAO;oBACf,IAAI,CAACzC,IAAIiO,SAAS,CAAC,kBAAkB;wBACnCxD,eAAe;4BAAEK,YAAY;4BAAGuC,QAAQ9I;wBAAU;oBACpD;gBACF,OAAO,IAAImJ,WAAWjD,YAAY,EAAE;oBAClC,wEAAwE;oBACxE,oBAAoB;oBACpB,IAAI,OAAOiD,WAAWjD,YAAY,CAACK,UAAU,KAAK,UAAU;4BAShD4C;wBARV,IAAIA,WAAWjD,YAAY,CAACK,UAAU,GAAG,GAAG;4BAC1C,MAAM,qBAEL,CAFK,IAAIE,MACR,CAAC,2CAA2C,EAAE0C,WAAWjD,YAAY,CAACK,UAAU,CAAC,IAAI,CAAC,GADlF,qBAAA;uCAAA;4CAAA;8CAAA;4BAEN;wBACF;wBAEAL,eAAe;4BACbK,YAAY4C,WAAWjD,YAAY,CAACK,UAAU;4BAC9CuC,QAAQK,EAAAA,2BAAAA,WAAWjD,YAAY,qBAAvBiD,yBAAyBL,MAAM,KAAIpL,WAAWqH,UAAU;wBAClE;oBACF,OAGK;wBACHmB,eAAe;4BAAEK,YAAYoD,0BAAc;4BAAEb,QAAQ9I;wBAAU;oBACjE;gBACF;YACF;YAEAmJ,WAAWjD,YAAY,GAAGA;YAE1B,IACE,OAAOhG,0BAA0B,YACjCkJ,CAAAA,8BAAAA,WAAY5O,IAAI,MAAKwM,8BAAe,CAACtM,QAAQ,IAC7C0O,WAAW/B,WAAW,EACtB;oBAea+B;gBAdb,uEAAuE;gBACvE,sEAAsE;gBACtE,sEAAsE;gBAEtE,oEAAoE;gBACpE,uEAAuE;gBACvE,wEAAwE;gBACxE,sEAAsE;gBACtE,sEAAsE;gBACtE,wDAAwD;gBACxD3N,IAAI6N,SAAS,CAACM,0CAAwB,EAAE;gBAExC,sEAAsE;gBACtE,8CAA8C;gBAC9C,MAAMC,QAAOT,uBAAAA,WAAW/K,OAAO,qBAAlB+K,oBAAoB,CAAC/C,kCAAsB,CAAC;gBACzD,IAAIlK,eAAe+B,SAAS2L,QAAQ,OAAOA,SAAS,UAAU;oBAC5DpO,IAAI6N,SAAS,CAACjD,kCAAsB,EAAEwD;gBACxC;gBAEA,MAAMC,iBAAiBV,WAAW/B,WAAW,CAAC/E,GAAG,CAACpC;gBAClD,IAAI4J,mBAAmB9J,WAAW;oBAChC,YAAY;oBACZ,OAAO+J,IAAAA,6BAAgB,EAAC;wBACtBvO;wBACAC;wBACAuO,MAAM;wBACNC,eAAevM,WAAWuM,aAAa;wBACvCC,iBAAiBxM,WAAWwM,eAAe;wBAC3CjE,QAAQ+C,qBAAY,CAACC,UAAU,CAACa;wBAChC5D,cAAciD,WAAWjD,YAAY;oBACvC;gBACF;gBAEA,yEAAyE;gBACzE,yEAAyE;gBACzE,sEAAsE;gBACtE,qEAAqE;gBACrE,oEAAoE;gBACpE,gCAAgC;gBAChCzK,IAAIa,UAAU,GAAG;gBACjB,OAAOyN,IAAAA,6BAAgB,EAAC;oBACtBvO;oBACAC;oBACAuO,MAAM;oBACNC,eAAevM,WAAWuM,aAAa;oBACvCC,iBAAiBxM,WAAWwM,eAAe;oBAC3CjE,QAAQ+C,qBAAY,CAACC,UAAU,CAAC;oBAChC/C,cAAciD,WAAWjD,YAAY;gBACvC;YACF;YAEA,yEAAyE;YACzE,8BAA8B;YAC9B,MAAMiE,eAAejO,IAAAA,2BAAc,EAACV,KAAK;YACzC,IAAI2O,cAAc;gBAChB,MAAMC,WAAW,MAAMD,aACrB;oBACE,GAAGhB,UAAU;oBACb,0CAA0C;oBAC1C,wCAAwC;oBACxCpC,OAAO;wBACL,GAAGoC,WAAWpC,KAAK;wBACnBvM,MAAM;oBACR;gBACF,GACA;oBACEsI,KAAK5G,IAAAA,2BAAc,EAACV,KAAK;gBAC3B;gBAEF,IAAI4O,UAAU;oBACZ,0CAA0C;oBAC1C,OAAO;gBACT;YACF;YAEA,oEAAoE;YACpE,gBAAgB;YAChB,IAAIb,eAAexJ,kBAAkB;gBACnC,MAAM,qBAEL,CAFK,IAAI0G,MACR,yEADI,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YAEA,IAAI2C,WAAW/K,OAAO,EAAE;gBACtB,MAAMA,UAAU;oBAAE,GAAG+K,WAAW/K,OAAO;gBAAC;gBAExC,IAAI,CAAClC,eAAe,CAAC+B,OAAO;oBAC1B,OAAOG,OAAO,CAACgI,kCAAsB,CAAC;gBACxC;gBAEA,KAAK,IAAI,CAACgE,KAAKtD,MAAM,IAAIuD,OAAOC,OAAO,CAAClM,SAAU;oBAChD,IAAI,OAAO0I,UAAU,aAAa;oBAElC,IAAIyD,MAAMC,OAAO,CAAC1D,QAAQ;wBACxB,KAAK,MAAM2D,KAAK3D,MAAO;4BACrBtL,IAAIkP,YAAY,CAACN,KAAKK;wBACxB;oBACF,OAAO,IAAI,OAAO3D,UAAU,UAAU;wBACpCA,QAAQA,MAAM6D,QAAQ;wBACtBnP,IAAIkP,YAAY,CAACN,KAAKtD;oBACxB,OAAO;wBACLtL,IAAIkP,YAAY,CAACN,KAAKtD;oBACxB;gBACF;YACF;YAEA,sEAAsE;YACtE,8CAA8C;YAC9C,MAAM8C,QAAOT,sBAAAA,WAAW/K,OAAO,qBAAlB+K,mBAAoB,CAAC/C,kCAAsB,CAAC;YACzD,IAAIlK,eAAe+B,SAAS2L,QAAQ,OAAOA,SAAS,UAAU;gBAC5DpO,IAAI6N,SAAS,CAACjD,kCAAsB,EAAEwD;YACxC;YAEA,0EAA0E;YAC1E,0EAA0E;YAC1E,oCAAoC;YACpC,IAAIT,WAAWhC,MAAM,IAAK,CAAA,CAACxI,gBAAgB,CAACY,iBAAgB,GAAI;gBAC9D/D,IAAIa,UAAU,GAAG8M,WAAWhC,MAAM;YACpC;YAEA,gGAAgG;YAChG,IACE,CAACjL,eACDiN,WAAWhC,MAAM,IACjByD,sCAAkB,CAACzB,WAAWhC,MAAM,CAAC,IACrCxI,cACA;gBACAnD,IAAIa,UAAU,GAAG;YACnB;YAEA,sCAAsC;YACtC,IAAIiN,aAAa;gBACf9N,IAAI6N,SAAS,CAACM,0CAAwB,EAAE;YAC1C;YAEA,2DAA2D;YAC3D,oEAAoE;YACpE,0EAA0E;YAC1E,+BAA+B;YAC/B,IAAIhL,gBAAgB,CAACtB,aAAa;gBAChC,8DAA8D;gBAC9D,IAAI,OAAO8L,WAAWlC,OAAO,KAAK,aAAa;oBAC7C,IAAIkC,WAAWpG,SAAS,EAAE;wBACxB,MAAM,qBAA0D,CAA1D,IAAIyD,MAAM,kDAAV,qBAAA;mCAAA;wCAAA;0CAAA;wBAAyD;oBACjE;oBAEA,OAAOsD,IAAAA,6BAAgB,EAAC;wBACtBvO;wBACAC;wBACAuO,MAAM;wBACNC,eAAevM,WAAWuM,aAAa;wBACvCC,iBAAiBxM,WAAWwM,eAAe;wBAC3CjE,QAAQmD,WAAWnC,IAAI;wBACvB,0DAA0D;wBAC1D,2DAA2D;wBAC3D,+DAA+D;wBAC/D,mBAAmB;wBACnB,+EAA+E;wBAC/Ef,cAAcjG,sBACV;4BAAEsG,YAAY;4BAAGuC,QAAQ9I;wBAAU,IACnCmJ,WAAWjD,YAAY;oBAC7B;gBACF;gBAEA,sEAAsE;gBACtE,QAAQ;gBACR,OAAO6D,IAAAA,6BAAgB,EAAC;oBACtBvO;oBACAC;oBACAuO,MAAM;oBACNC,eAAevM,WAAWuM,aAAa;oBACvCC,iBAAiBxM,WAAWwM,eAAe;oBAC3CjE,QAAQ+C,qBAAY,CAACC,UAAU,CAACG,WAAWlC,OAAO;oBAClDhB,cAAciD,WAAWjD,YAAY;gBACvC;YACF;YAEA,mCAAmC;YACnC,IAAI4E,OAAO1B,WAAWnC,IAAI;YAE1B,qEAAqE;YACrE,sEAAsE;YACtE,oDAAoD;YACpD,IAAI,CAACsC,eAAepN,aAAa;gBAC/B,OAAO4N,IAAAA,6BAAgB,EAAC;oBACtBvO;oBACAC;oBACAuO,MAAM;oBACNC,eAAevM,WAAWuM,aAAa;oBACvCC,iBAAiBxM,WAAWwM,eAAe;oBAC3CjE,QAAQ6E;oBACR5E,cAAciD,WAAWjD,YAAY;gBACvC;YACF;YAEA,sEAAsE;YACtE,uEAAuE;YACvE,sEAAsE;YACtE,4BAA4B;YAC5B,IAAItG,sBAAsBC,wBAAwB;gBAChD,mEAAmE;gBACnE,mDAAmD;gBACnDiL,KAAKC,KAAK,CACR,IAAIC,eAAe;oBACjBC,OAAMC,UAAU;wBACdA,WAAWC,OAAO,CAACC,yBAAY,CAACC,MAAM,CAACC,aAAa;wBACpDJ,WAAWK,KAAK;oBAClB;gBACF;gBAGF,OAAOxB,IAAAA,6BAAgB,EAAC;oBACtBvO;oBACAC;oBACAuO,MAAM;oBACNC,eAAevM,WAAWuM,aAAa;oBACvCC,iBAAiBxM,WAAWwM,eAAe;oBAC3CjE,QAAQ6E;oBACR5E,cAAc;wBAAEK,YAAY;wBAAGuC,QAAQ9I;oBAAU;gBACnD;YACF;YAEA,yEAAyE;YACzE,wEAAwE;YACxE,mBAAmB;YACnB,MAAMwL,cAAc,IAAIC;YACxBX,KAAKC,KAAK,CAACS,YAAYE,QAAQ;YAE/B,wEAAwE;YACxE,wEAAwE;YACxE,yEAAyE;YACzE3I,SAAS;gBACP1B;gBACA2B,WAAWoG,WAAWpG,SAAS;gBAC/B,sEAAsE;gBACtE,YAAY;gBACZC,qBAAqB;YACvB,GACG0I,IAAI,CAAC,OAAO1F;oBAKPA;gBAJJ,IAAI,CAACA,QAAQ;oBACX,MAAM,qBAAwD,CAAxD,IAAIQ,MAAM,gDAAV,qBAAA;+BAAA;oCAAA;sCAAA;oBAAuD;gBAC/D;gBAEA,IAAIR,EAAAA,gBAAAA,OAAOc,KAAK,qBAAZd,cAAczL,IAAI,MAAKwM,8BAAe,CAACtM,QAAQ,EAAE;wBAELuL;oBAD9C,MAAM,qBAEL,CAFK,IAAIQ,MACR,CAAC,yCAAyC,GAAER,iBAAAA,OAAOc,KAAK,qBAAZd,eAAczL,IAAI,EAAE,GAD5D,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF;gBAEA,6CAA6C;gBAC7C,MAAMyL,OAAOc,KAAK,CAACE,IAAI,CAAC2E,MAAM,CAACJ,YAAYK,QAAQ;YACrD,GACCC,KAAK,CAAC,CAAC/F;gBACN,iEAAiE;gBACjE,0DAA0D;gBAC1DyF,YAAYK,QAAQ,CAACE,KAAK,CAAChG,KAAK+F,KAAK,CAAC,CAACE;oBACrCvJ,QAAQkD,KAAK,CAAC,8BAA8BqG;gBAC9C;YACF;YAEF,OAAOjC,IAAAA,6BAAgB,EAAC;gBACtBvO;gBACAC;gBACAuO,MAAM;gBACNC,eAAevM,WAAWuM,aAAa;gBACvCC,iBAAiBxM,WAAWwM,eAAe;gBAC3CjE,QAAQ6E;gBACR,uEAAuE;gBACvE,wEAAwE;gBACxE,qCAAqC;gBACrC5E,cAAc;oBAAEK,YAAY;oBAAGuC,QAAQ9I;gBAAU;YACnD;QACF;QAEA,oDAAoD;QACpD,yDAAyD;QACzD,IAAIkB,YAAY;YACd,MAAMuH,eAAevH;QACvB,OAAO;YACL,OAAO,MAAMF,OAAOiL,qBAAqB,CAACzQ,IAAI6C,OAAO,EAAE,IACrD2C,OAAOkL,KAAK,CACV3J,yBAAc,CAACC,aAAa,EAC5B;oBACE2J,UAAU,GAAGpL,OAAO,CAAC,EAAEvF,IAAIsH,GAAG,EAAE;oBAChCtI,MAAM4R,gBAAQ,CAACC,MAAM;oBACrBC,YAAY;wBACV,eAAevL;wBACf,eAAevF,IAAIsH,GAAG;oBACxB;gBACF,GACA2F;QAGN;IACF,EAAE,OAAO1C,KAAK;QACZ,kDAAkD;QAClD,IAAI,CAAC7E,YAAY;YACf,MAAMlH,YAAY8L,cAAc,CAC9BtK,KACAuK,KACA;gBACEwG,YAAY;gBACZC,WAAW5Q;gBACX6Q,WAAW;gBACXC,kBAAkBC,IAAAA,0BAAmB,EAAC;oBACpChJ,cAAczF;oBACdL;gBACF;YACF,GACAJ;QAEJ;QAEA,mDAAmD;QACnD,MAAMsI;IACR;AACF", "ignoreList": [0]}