{"version": 3, "sources": ["../../../src/build/templates/edge-ssr.ts"], "sourcesContent": ["import '../../server/web/globals'\nimport { adapter, type NextRequestHint } from '../../server/web/adapter'\nimport { IncrementalCache } from '../../server/lib/incremental-cache'\nimport { initializeCacheHandlers } from '../../server/use-cache/handlers'\n\nimport Document from 'VAR_MODULE_DOCUMENT'\nimport * as appMod from 'VAR_MODULE_APP'\nimport * as userlandPage from 'VAR_USERLAND'\nimport * as userlandErrorPage from 'VAR_MODULE_GLOBAL_ERROR'\n\ndeclare const userland500Page: any\ndeclare const incrementalCacheHandler: any\n// OPTIONAL_IMPORT:* as userland500Page\n// OPTIONAL_IMPORT:incrementalCacheHandler\n\n// TODO: re-enable this once we've refactored to use implicit matches\n// const renderToHTML = undefined\n\nimport RouteModule, {\n  type PagesRouteHandlerContext,\n} from '../../server/route-modules/pages/module'\nimport { WebNextRequest, WebNextResponse } from '../../server/base-http/web'\n\nimport type { RequestData } from '../../server/web/types'\nimport type { NextConfigComplete } from '../../server/config-shared'\nimport type { NextFetchEvent } from '../../server/web/spec-extension/fetch-event'\nimport type RenderResult from '../../server/render-result'\nimport type { RenderResultMetadata } from '../../server/render-result'\nimport { getTracer, SpanKind, type Span } from '../../server/lib/trace/tracer'\nimport { BaseServerSpan } from '../../server/lib/trace/constants'\n\n// injected by the loader afterwards.\ndeclare const nextConfig: NextConfigComplete\ndeclare const pageRouteModuleOptions: any\ndeclare const errorRouteModuleOptions: any\ndeclare const user500RouteModuleOptions: any\n// INJECT:nextConfig\n// INJECT:pageRouteModuleOptions\n// INJECT:errorRouteModuleOptions\n// INJECT:user500RouteModuleOptions\n\n// Initialize the cache handlers interface.\ninitializeCacheHandlers()\n\n// expose this for the route-module\n;(globalThis as any).nextConfig = nextConfig\n\nconst pageMod = {\n  ...userlandPage,\n  routeModule: new RouteModule({\n    ...pageRouteModuleOptions,\n    components: {\n      App: appMod.default,\n      Document,\n    },\n    userland: userlandPage,\n  }),\n}\n\nconst errorMod = {\n  ...userlandErrorPage,\n  routeModule: new RouteModule({\n    ...errorRouteModuleOptions,\n    components: {\n      App: appMod.default,\n      Document,\n    },\n    userland: userlandErrorPage,\n  }),\n}\n\n// FIXME: this needs to be made compatible with the template\nconst error500Mod = userland500Page\n  ? {\n      ...userland500Page,\n      routeModule: new RouteModule({\n        ...user500RouteModuleOptions,\n        components: {\n          App: appMod.default,\n          Document,\n        },\n        userland: userland500Page,\n      }),\n    }\n  : null\n\nexport const ComponentMod = pageMod\n\nasync function requestHandler(\n  req: NextRequestHint,\n  _event: NextFetchEvent\n): Promise<Response> {\n  let srcPage = 'VAR_PAGE'\n\n  const relativeUrl = `${req.nextUrl.pathname}${req.nextUrl.search}`\n  const baseReq = new WebNextRequest(req)\n  const pageRouteModule = pageMod.routeModule as RouteModule\n  const prepareResult = await pageRouteModule.prepare(baseReq, null, {\n    srcPage,\n    multiZoneDraftMode: false,\n  })\n\n  if (!prepareResult) {\n    return new Response('Bad Request', {\n      status: 400,\n    })\n  }\n  const {\n    query,\n    params,\n    buildId,\n    isNextDataRequest,\n    buildManifest,\n    prerenderManifest,\n    reactLoadableManifest,\n    clientReferenceManifest,\n    subresourceIntegrityManifest,\n    dynamicCssManifest,\n  } = prepareResult\n\n  const renderContext: PagesRouteHandlerContext = {\n    page: srcPage,\n    query,\n    params,\n\n    sharedContext: {\n      buildId,\n      deploymentId: process.env.NEXT_DEPLOYMENT_ID,\n      customServer: undefined,\n    },\n\n    renderContext: {\n      isFallback: false,\n      isDraftMode: false,\n      developmentNotFoundSourcePage: undefined,\n    },\n\n    renderOpts: {\n      params,\n      page: srcPage,\n      supportsDynamicResponse: true,\n      Component: pageMod.Component,\n      ComponentMod: pageMod,\n      pageConfig: pageMod.pageConfig,\n      routeModule: pageMod.routeModule,\n      strictNextHead: nextConfig.experimental.strictNextHead ?? true,\n      canonicalBase: nextConfig.amp.canonicalBase || '',\n      previewProps: prerenderManifest.preview,\n      ampOptimizerConfig: nextConfig.experimental.amp?.optimizer,\n      basePath: nextConfig.basePath,\n      assetPrefix: nextConfig.assetPrefix,\n      images: nextConfig.images,\n      optimizeCss: nextConfig.experimental.optimizeCss,\n      nextConfigOutput: nextConfig.output,\n      nextScriptWorkers: nextConfig.experimental.nextScriptWorkers,\n      disableOptimizedLoading: nextConfig.experimental.disableOptimizedLoading,\n      domainLocales: nextConfig.i18n?.domains,\n      distDir: '',\n      crossOrigin: nextConfig.crossOrigin ? nextConfig.crossOrigin : undefined,\n      largePageDataBytes: nextConfig.experimental.largePageDataBytes,\n      // Only the `publicRuntimeConfig` key is exposed to the client side\n      // It'll be rendered as part of __NEXT_DATA__ on the client side\n      runtimeConfig:\n        Object.keys(nextConfig.publicRuntimeConfig).length > 0\n          ? nextConfig.publicRuntimeConfig\n          : undefined,\n\n      isExperimentalCompile: nextConfig.experimental.isExperimentalCompile,\n      // `htmlLimitedBots` is passed to server as serialized config in string format\n      experimental: {\n        clientTraceMetadata: nextConfig.experimental.clientTraceMetadata,\n      },\n\n      buildManifest,\n      subresourceIntegrityManifest,\n      reactLoadableManifest,\n      clientReferenceManifest,\n      dynamicCssManifest,\n    },\n  }\n  let finalStatus = 200\n\n  const renderResultToResponse = (\n    result: RenderResult<RenderResultMetadata>\n  ): Response => {\n    // Handle null responses\n    if (result.isNull) {\n      finalStatus = 500\n      return new Response(null, { status: 500 })\n    }\n\n    // Extract metadata\n    const { metadata } = result\n    finalStatus = metadata.statusCode || 200\n    const headers = new Headers()\n\n    // Set content type\n    const contentType = result.contentType || 'text/html; charset=utf-8'\n    headers.set('Content-Type', contentType)\n\n    // Add metadata headers\n    if (metadata.headers) {\n      for (const [key, value] of Object.entries(metadata.headers)) {\n        if (value !== undefined) {\n          if (Array.isArray(value)) {\n            // Handle multiple header values\n            for (const v of value) {\n              headers.append(key, String(v))\n            }\n          } else {\n            headers.set(key, String(value))\n          }\n        }\n      }\n    }\n\n    // Handle static response\n    if (!result.isDynamic) {\n      const body = result.toUnchunkedString()\n      headers.set(\n        'Content-Length',\n        String(new TextEncoder().encode(body).length)\n      )\n      return new Response(body, {\n        status: finalStatus,\n        headers,\n      })\n    }\n\n    // Handle dynamic/streaming response\n    // For edge runtime, we need to create a readable stream that pipes from the result\n    const { readable, writable } = new TransformStream()\n\n    // Start piping the result to the writable stream\n    // This is done asynchronously to avoid blocking the response creation\n    result.pipeTo(writable).catch((err) => {\n      console.error('Error piping RenderResult to response:', err)\n    })\n\n    return new Response(readable, {\n      status: finalStatus,\n      headers,\n    })\n  }\n\n  const invokeRender = async (span?: Span): Promise<Response> => {\n    try {\n      const result = await pageRouteModule\n        .render(\n          // @ts-expect-error we don't type this for edge\n          baseReq,\n          new WebNextResponse(undefined),\n          {\n            ...renderContext,\n            renderOpts: {\n              ...renderContext.renderOpts,\n              getServerSideProps: pageMod.getServerSideProps,\n              Component: pageMod.default || pageMod,\n              ComponentMod: pageMod,\n              pageConfig: pageMod.config,\n              isNextDataRequest,\n            },\n          }\n        )\n        .finally(() => {\n          if (!span) return\n\n          span.setAttributes({\n            'http.status_code': finalStatus,\n            'next.rsc': false,\n          })\n\n          const rootSpanAttributes = tracer.getRootSpanAttributes()\n          // We were unable to get attributes, probably OTEL is not enabled\n          if (!rootSpanAttributes) {\n            return\n          }\n\n          if (\n            rootSpanAttributes.get('next.span_type') !==\n            BaseServerSpan.handleRequest\n          ) {\n            console.warn(\n              `Unexpected root span type '${rootSpanAttributes.get(\n                'next.span_type'\n              )}'. Please report this Next.js issue https://github.com/vercel/next.js`\n            )\n            return\n          }\n\n          const route = rootSpanAttributes.get('next.route')\n          if (route) {\n            const name = `${req.method} ${route}`\n\n            span.setAttributes({\n              'next.route': route,\n              'http.route': route,\n              'next.span_name': name,\n            })\n            span.updateName(name)\n          } else {\n            span.updateName(`${req.method} ${relativeUrl}`)\n          }\n        })\n\n      return renderResultToResponse(result)\n    } catch (err) {\n      const errModule = error500Mod || errorMod\n      const errRouteModule = errModule.routeModule as RouteModule\n\n      if (errRouteModule.isDev) {\n        throw err\n      }\n\n      await errRouteModule.onRequestError(baseReq, err, {\n        routerKind: 'Pages Router',\n        routePath: srcPage,\n        routeType: 'render',\n        revalidateReason: undefined,\n      })\n\n      const errResult = await errRouteModule.render(\n        // @ts-expect-error we don't type this for edge\n        baseReq,\n        new WebNextResponse(undefined),\n        {\n          ...renderContext,\n          page: error500Mod ? '/500' : '/_error',\n          renderOpts: {\n            ...renderContext.renderOpts,\n            getServerSideProps: errModule.getServerSideProps,\n            Component: errModule.default || errModule,\n            ComponentMod: errModule,\n            pageConfig: errModule.config,\n          },\n        }\n      )\n\n      return renderResultToResponse(errResult)\n    }\n  }\n\n  const tracer = getTracer()\n\n  // TODO: activeSpan code path is for when wrapped by\n  // next-server can be removed when this is no longer used\n  return tracer.withPropagatedContext(req.headers, () =>\n    tracer.trace(\n      BaseServerSpan.handleRequest,\n      {\n        spanName: `${req.method} ${relativeUrl}`,\n        kind: SpanKind.SERVER,\n        attributes: {\n          'http.method': req.method,\n          'http.target': relativeUrl,\n          'http.route': srcPage,\n        },\n      },\n      invokeRender\n    )\n  )\n}\n\nexport default function nHandler(opts: { page: string; request: RequestData }) {\n  return adapter({\n    ...opts,\n    IncrementalCache,\n    handler: requestHandler,\n    incrementalCacheHandler,\n    bypassNextUrl: true,\n  })\n}\n"], "names": ["ComponentMod", "nH<PERSON><PERSON>", "initializeCacheHandlers", "globalThis", "nextConfig", "pageMod", "userlandPage", "routeModule", "RouteModule", "pageRouteModuleOptions", "components", "App", "appMod", "default", "Document", "userland", "errorMod", "userlandErrorPage", "errorRouteModuleOptions", "error500Mod", "userland500Page", "user500RouteModuleOptions", "requestHandler", "req", "_event", "srcPage", "relativeUrl", "nextUrl", "pathname", "search", "baseReq", "WebNextRequest", "pageRouteModule", "prepareResult", "prepare", "multiZoneDraftMode", "Response", "status", "query", "params", "buildId", "isNextDataRequest", "buildManifest", "prerenderManifest", "reactLoadableManifest", "clientReferenceManifest", "subresourceIntegrityManifest", "dynamicCssManifest", "renderContext", "page", "sharedContext", "deploymentId", "process", "env", "NEXT_DEPLOYMENT_ID", "customServer", "undefined", "<PERSON><PERSON><PERSON><PERSON>", "isDraftMode", "developmentNotFoundSourcePage", "renderOpts", "supportsDynamicResponse", "Component", "pageConfig", "strictNextHead", "experimental", "canonicalBase", "amp", "previewProps", "preview", "ampOptimizerConfig", "optimizer", "basePath", "assetPrefix", "images", "optimizeCss", "nextConfigOutput", "output", "nextScriptWorkers", "disableOptimizedLoading", "domainLocales", "i18n", "domains", "distDir", "crossOrigin", "largePageDataBytes", "runtimeConfig", "Object", "keys", "publicRuntimeConfig", "length", "isExperimentalCompile", "clientTraceMetadata", "finalStatus", "renderResultToResponse", "result", "isNull", "metadata", "statusCode", "headers", "Headers", "contentType", "set", "key", "value", "entries", "Array", "isArray", "v", "append", "String", "isDynamic", "body", "toUnchunkedString", "TextEncoder", "encode", "readable", "writable", "TransformStream", "pipeTo", "catch", "err", "console", "error", "invokeRender", "span", "render", "WebNextResponse", "getServerSideProps", "config", "finally", "setAttributes", "rootSpanAttributes", "tracer", "getRootSpanAttributes", "get", "BaseServerSpan", "handleRequest", "warn", "route", "name", "method", "updateName", "errModule", "errRouteModule", "isDev", "onRequestError", "routerKind", "routePath", "routeType", "revalidateReason", "err<PERSON><PERSON><PERSON>", "getTracer", "withPropagatedContext", "trace", "spanName", "kind", "SpanKind", "SERVER", "attributes", "opts", "adapter", "IncrementalCache", "handler", "incremental<PERSON>ache<PERSON><PERSON><PERSON>", "bypassNextUrl"], "mappings": ";;;;;;;;;;;;;;;IAsFaA,YAAY;eAAZA;;IAqRb,OAQC;eARuBC;;;QA3WjB;yBACuC;kCACb;0BACO;4EAEnB;wEACG;sEACM;iFACK;+DAY5B;qBACyC;wBAOD;2BAChB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO/B,oBAAoB;AACpB,gCAAgC;AAChC,iCAAiC;AACjC,mCAAmC;AAEnC,2CAA2C;AAC3CC,IAAAA,iCAAuB;AAGrBC,WAAmBC,UAAU,GAAGA;AAElC,MAAMC,UAAU;IACd,GAAGC,aAAY;IACfC,aAAa,IAAIC,eAAW,CAAC;QAC3B,GAAGC,sBAAsB;QACzBC,YAAY;YACVC,KAAKC,gBAAOC,OAAO;YACnBC,UAAAA,4BAAQ;QACV;QACAC,UAAUT;IACZ;AACF;AAEA,MAAMU,WAAW;IACf,GAAGC,wBAAiB;IACpBV,aAAa,IAAIC,eAAW,CAAC;QAC3B,GAAGU,uBAAuB;QAC1BR,YAAY;YACVC,KAAKC,gBAAOC,OAAO;YACnBC,UAAAA,4BAAQ;QACV;QACAC,UAAUE;IACZ;AACF;AAEA,4DAA4D;AAC5D,MAAME,cAAcC,kBAChB;IACE,GAAGA,eAAe;IAClBb,aAAa,IAAIC,eAAW,CAAC;QAC3B,GAAGa,yBAAyB;QAC5BX,YAAY;YACVC,KAAKC,gBAAOC,OAAO;YACnBC,UAAAA,4BAAQ;QACV;QACAC,UAAUK;IACZ;AACF,IACA;AAEG,MAAMpB,eAAeK;AAE5B,eAAeiB,eACbC,GAAoB,EACpBC,MAAsB;QA0DEpB,8BAQLA;IAhEnB,IAAIqB,UAAU;IAEd,MAAMC,cAAc,GAAGH,IAAII,OAAO,CAACC,QAAQ,GAAGL,IAAII,OAAO,CAACE,MAAM,EAAE;IAClE,MAAMC,UAAU,IAAIC,mBAAc,CAACR;IACnC,MAAMS,kBAAkB3B,QAAQE,WAAW;IAC3C,MAAM0B,gBAAgB,MAAMD,gBAAgBE,OAAO,CAACJ,SAAS,MAAM;QACjEL;QACAU,oBAAoB;IACtB;IAEA,IAAI,CAACF,eAAe;QAClB,OAAO,IAAIG,SAAS,eAAe;YACjCC,QAAQ;QACV;IACF;IACA,MAAM,EACJC,KAAK,EACLC,MAAM,EACNC,OAAO,EACPC,iBAAiB,EACjBC,aAAa,EACbC,iBAAiB,EACjBC,qBAAqB,EACrBC,uBAAuB,EACvBC,4BAA4B,EAC5BC,kBAAkB,EACnB,GAAGd;IAEJ,MAAMe,gBAA0C;QAC9CC,MAAMxB;QACNa;QACAC;QAEAW,eAAe;YACbV;YACAW,cAAcC,QAAQC,GAAG,CAACC,kBAAkB;YAC5CC,cAAcC;QAChB;QAEAR,eAAe;YACbS,YAAY;YACZC,aAAa;YACbC,+BAA+BH;QACjC;QAEAI,YAAY;YACVrB;YACAU,MAAMxB;YACNoC,yBAAyB;YACzBC,WAAWzD,QAAQyD,SAAS;YAC5B9D,cAAcK;YACd0D,YAAY1D,QAAQ0D,UAAU;YAC9BxD,aAAaF,QAAQE,WAAW;YAChCyD,gBAAgB5D,WAAW6D,YAAY,CAACD,cAAc,IAAI;YAC1DE,eAAe9D,WAAW+D,GAAG,CAACD,aAAa,IAAI;YAC/CE,cAAczB,kBAAkB0B,OAAO;YACvCC,kBAAkB,GAAElE,+BAAAA,WAAW6D,YAAY,CAACE,GAAG,qBAA3B/D,6BAA6BmE,SAAS;YAC1DC,UAAUpE,WAAWoE,QAAQ;YAC7BC,aAAarE,WAAWqE,WAAW;YACnCC,QAAQtE,WAAWsE,MAAM;YACzBC,aAAavE,WAAW6D,YAAY,CAACU,WAAW;YAChDC,kBAAkBxE,WAAWyE,MAAM;YACnCC,mBAAmB1E,WAAW6D,YAAY,CAACa,iBAAiB;YAC5DC,yBAAyB3E,WAAW6D,YAAY,CAACc,uBAAuB;YACxEC,aAAa,GAAE5E,mBAAAA,WAAW6E,IAAI,qBAAf7E,iBAAiB8E,OAAO;YACvCC,SAAS;YACTC,aAAahF,WAAWgF,WAAW,GAAGhF,WAAWgF,WAAW,GAAG5B;YAC/D6B,oBAAoBjF,WAAW6D,YAAY,CAACoB,kBAAkB;YAC9D,mEAAmE;YACnE,gEAAgE;YAChEC,eACEC,OAAOC,IAAI,CAACpF,WAAWqF,mBAAmB,EAAEC,MAAM,GAAG,IACjDtF,WAAWqF,mBAAmB,GAC9BjC;YAENmC,uBAAuBvF,WAAW6D,YAAY,CAAC0B,qBAAqB;YACpE,8EAA8E;YAC9E1B,cAAc;gBACZ2B,qBAAqBxF,WAAW6D,YAAY,CAAC2B,mBAAmB;YAClE;YAEAlD;YACAI;YACAF;YACAC;YACAE;QACF;IACF;IACA,IAAI8C,cAAc;IAElB,MAAMC,yBAAyB,CAC7BC;QAEA,wBAAwB;QACxB,IAAIA,OAAOC,MAAM,EAAE;YACjBH,cAAc;YACd,OAAO,IAAIzD,SAAS,MAAM;gBAAEC,QAAQ;YAAI;QAC1C;QAEA,mBAAmB;QACnB,MAAM,EAAE4D,QAAQ,EAAE,GAAGF;QACrBF,cAAcI,SAASC,UAAU,IAAI;QACrC,MAAMC,UAAU,IAAIC;QAEpB,mBAAmB;QACnB,MAAMC,cAAcN,OAAOM,WAAW,IAAI;QAC1CF,QAAQG,GAAG,CAAC,gBAAgBD;QAE5B,uBAAuB;QACvB,IAAIJ,SAASE,OAAO,EAAE;YACpB,KAAK,MAAM,CAACI,KAAKC,MAAM,IAAIjB,OAAOkB,OAAO,CAACR,SAASE,OAAO,EAAG;gBAC3D,IAAIK,UAAUhD,WAAW;oBACvB,IAAIkD,MAAMC,OAAO,CAACH,QAAQ;wBACxB,gCAAgC;wBAChC,KAAK,MAAMI,KAAKJ,MAAO;4BACrBL,QAAQU,MAAM,CAACN,KAAKO,OAAOF;wBAC7B;oBACF,OAAO;wBACLT,QAAQG,GAAG,CAACC,KAAKO,OAAON;oBAC1B;gBACF;YACF;QACF;QAEA,yBAAyB;QACzB,IAAI,CAACT,OAAOgB,SAAS,EAAE;YACrB,MAAMC,OAAOjB,OAAOkB,iBAAiB;YACrCd,QAAQG,GAAG,CACT,kBACAQ,OAAO,IAAII,cAAcC,MAAM,CAACH,MAAMtB,MAAM;YAE9C,OAAO,IAAItD,SAAS4E,MAAM;gBACxB3E,QAAQwD;gBACRM;YACF;QACF;QAEA,oCAAoC;QACpC,mFAAmF;QACnF,MAAM,EAAEiB,QAAQ,EAAEC,QAAQ,EAAE,GAAG,IAAIC;QAEnC,iDAAiD;QACjD,sEAAsE;QACtEvB,OAAOwB,MAAM,CAACF,UAAUG,KAAK,CAAC,CAACC;YAC7BC,QAAQC,KAAK,CAAC,0CAA0CF;QAC1D;QAEA,OAAO,IAAIrF,SAASgF,UAAU;YAC5B/E,QAAQwD;YACRM;QACF;IACF;IAEA,MAAMyB,eAAe,OAAOC;QAC1B,IAAI;YACF,MAAM9B,SAAS,MAAM/D,gBAClB8F,MAAM,CACL,+CAA+C;YAC/ChG,SACA,IAAIiG,oBAAe,CAACvE,YACpB;gBACE,GAAGR,aAAa;gBAChBY,YAAY;oBACV,GAAGZ,cAAcY,UAAU;oBAC3BoE,oBAAoB3H,QAAQ2H,kBAAkB;oBAC9ClE,WAAWzD,QAAQQ,OAAO,IAAIR;oBAC9BL,cAAcK;oBACd0D,YAAY1D,QAAQ4H,MAAM;oBAC1BxF;gBACF;YACF,GAEDyF,OAAO,CAAC;gBACP,IAAI,CAACL,MAAM;gBAEXA,KAAKM,aAAa,CAAC;oBACjB,oBAAoBtC;oBACpB,YAAY;gBACd;gBAEA,MAAMuC,qBAAqBC,OAAOC,qBAAqB;gBACvD,iEAAiE;gBACjE,IAAI,CAACF,oBAAoB;oBACvB;gBACF;gBAEA,IACEA,mBAAmBG,GAAG,CAAC,sBACvBC,yBAAc,CAACC,aAAa,EAC5B;oBACAf,QAAQgB,IAAI,CACV,CAAC,2BAA2B,EAAEN,mBAAmBG,GAAG,CAClD,kBACA,qEAAqE,CAAC;oBAE1E;gBACF;gBAEA,MAAMI,QAAQP,mBAAmBG,GAAG,CAAC;gBACrC,IAAII,OAAO;oBACT,MAAMC,OAAO,GAAGrH,IAAIsH,MAAM,CAAC,CAAC,EAAEF,OAAO;oBAErCd,KAAKM,aAAa,CAAC;wBACjB,cAAcQ;wBACd,cAAcA;wBACd,kBAAkBC;oBACpB;oBACAf,KAAKiB,UAAU,CAACF;gBAClB,OAAO;oBACLf,KAAKiB,UAAU,CAAC,GAAGvH,IAAIsH,MAAM,CAAC,CAAC,EAAEnH,aAAa;gBAChD;YACF;YAEF,OAAOoE,uBAAuBC;QAChC,EAAE,OAAO0B,KAAK;YACZ,MAAMsB,YAAY5H,eAAeH;YACjC,MAAMgI,iBAAiBD,UAAUxI,WAAW;YAE5C,IAAIyI,eAAeC,KAAK,EAAE;gBACxB,MAAMxB;YACR;YAEA,MAAMuB,eAAeE,cAAc,CAACpH,SAAS2F,KAAK;gBAChD0B,YAAY;gBACZC,WAAW3H;gBACX4H,WAAW;gBACXC,kBAAkB9F;YACpB;YAEA,MAAM+F,YAAY,MAAMP,eAAelB,MAAM,CAC3C,+CAA+C;YAC/ChG,SACA,IAAIiG,oBAAe,CAACvE,YACpB;gBACE,GAAGR,aAAa;gBAChBC,MAAM9B,cAAc,SAAS;gBAC7ByC,YAAY;oBACV,GAAGZ,cAAcY,UAAU;oBAC3BoE,oBAAoBe,UAAUf,kBAAkB;oBAChDlE,WAAWiF,UAAUlI,OAAO,IAAIkI;oBAChC/I,cAAc+I;oBACdhF,YAAYgF,UAAUd,MAAM;gBAC9B;YACF;YAGF,OAAOnC,uBAAuByD;QAChC;IACF;IAEA,MAAMlB,SAASmB,IAAAA,iBAAS;IAExB,oDAAoD;IACpD,yDAAyD;IACzD,OAAOnB,OAAOoB,qBAAqB,CAAClI,IAAI4E,OAAO,EAAE,IAC/CkC,OAAOqB,KAAK,CACVlB,yBAAc,CAACC,aAAa,EAC5B;YACEkB,UAAU,GAAGpI,IAAIsH,MAAM,CAAC,CAAC,EAAEnH,aAAa;YACxCkI,MAAMC,gBAAQ,CAACC,MAAM;YACrBC,YAAY;gBACV,eAAexI,IAAIsH,MAAM;gBACzB,eAAenH;gBACf,cAAcD;YAChB;QACF,GACAmG;AAGN;AAEe,SAAS3H,SAAS+J,IAA4C;IAC3E,OAAOC,IAAAA,gBAAO,EAAC;QACb,GAAGD,IAAI;QACPE,kBAAAA,kCAAgB;QAChBC,SAAS7I;QACT8I;QACAC,eAAe;IACjB;AACF", "ignoreList": [0]}