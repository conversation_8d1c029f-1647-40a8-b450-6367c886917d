/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Fuser%2FDocuments%2Faugment-projects%2Fnextjs-superNode%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fuser%2FDocuments%2Faugment-projects%2Fnextjs-superNode&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Fuser%2FDocuments%2Faugment-projects%2Fnextjs-superNode%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fuser%2FDocuments%2Faugment-projects%2Fnextjs-superNode&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_builtin_global_error_js__WEBPACK_IMPORTED_MODULE_24___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   handler: () => (/* binding */ handler),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/route-modules/app-page/module.compiled.js?44ed\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/instrumentation/utils */ \"(rsc)/./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/instrumentation/utils.js\");\n/* harmony import */ var next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/lib/trace/tracer */ \"(rsc)/./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/trace/tracer.js\");\n/* harmony import */ var next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/dist/server/request-meta */ \"(rsc)/./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/request-meta.js\");\n/* harmony import */ var next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/dist/server/lib/trace/constants */ \"(rsc)/./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/trace/constants.js\");\n/* harmony import */ var next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var next_dist_server_app_render_interop_default__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/dist/server/app-render/interop-default */ \"(rsc)/./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/app-render/interop-default.js\");\n/* harmony import */ var next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/dist/server/base-http/node */ \"(rsc)/./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/base-http/node.js\");\n/* harmony import */ var next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var next_dist_server_lib_experimental_ppr__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/dist/server/lib/experimental/ppr */ \"(rsc)/./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/experimental/ppr.js\");\n/* harmony import */ var next_dist_server_lib_experimental_ppr__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_experimental_ppr__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var next_dist_server_request_fallback_params__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next/dist/server/request/fallback-params */ \"(rsc)/./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/request/fallback-params.js\");\n/* harmony import */ var next_dist_server_app_render_encryption_utils__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next/dist/server/app-render/encryption-utils */ \"(rsc)/./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/app-render/encryption-utils.js\");\n/* harmony import */ var next_dist_server_app_render_encryption_utils__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_encryption_utils__WEBPACK_IMPORTED_MODULE_10__);\n/* harmony import */ var next_dist_server_lib_streaming_metadata__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! next/dist/server/lib/streaming-metadata */ \"(rsc)/./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/streaming-metadata.js\");\n/* harmony import */ var next_dist_server_lib_streaming_metadata__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_streaming_metadata__WEBPACK_IMPORTED_MODULE_11__);\n/* harmony import */ var next_dist_server_app_render_action_utils__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! next/dist/server/app-render/action-utils */ \"(rsc)/./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/app-render/action-utils.js\");\n/* harmony import */ var next_dist_server_app_render_action_utils__WEBPACK_IMPORTED_MODULE_12___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_action_utils__WEBPACK_IMPORTED_MODULE_12__);\n/* harmony import */ var next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! next/dist/shared/lib/router/utils/app-paths */ \"next/dist/shared/lib/router/utils/app-paths\");\n/* harmony import */ var next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_13___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_13__);\n/* harmony import */ var next_dist_server_lib_server_action_request_meta__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! next/dist/server/lib/server-action-request-meta */ \"(rsc)/./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/server-action-request-meta.js\");\n/* harmony import */ var next_dist_server_lib_server_action_request_meta__WEBPACK_IMPORTED_MODULE_14___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_server_action_request_meta__WEBPACK_IMPORTED_MODULE_14__);\n/* harmony import */ var next_dist_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! next/dist/client/components/app-router-headers */ \"(rsc)/./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/app-router-headers.js\");\n/* harmony import */ var next_dist_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_15___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_15__);\n/* harmony import */ var next_dist_shared_lib_router_utils_is_bot__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! next/dist/shared/lib/router/utils/is-bot */ \"next/dist/shared/lib/router/utils/is-bot\");\n/* harmony import */ var next_dist_shared_lib_router_utils_is_bot__WEBPACK_IMPORTED_MODULE_16___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_router_utils_is_bot__WEBPACK_IMPORTED_MODULE_16__);\n/* harmony import */ var next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! next/dist/server/response-cache */ \"(rsc)/./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/response-cache/index.js\");\n/* harmony import */ var next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_17___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_17__);\n/* harmony import */ var next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! next/dist/lib/fallback */ \"(rsc)/./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/lib/fallback.js\");\n/* harmony import */ var next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18__);\n/* harmony import */ var next_dist_server_render_result__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! next/dist/server/render-result */ \"(rsc)/./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/render-result.js\");\n/* harmony import */ var next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! next/dist/lib/constants */ \"(rsc)/./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/lib/constants.js\");\n/* harmony import */ var next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20__);\n/* harmony import */ var next_dist_server_stream_utils_encoded_tags__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! next/dist/server/stream-utils/encoded-tags */ \"(rsc)/./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/stream-utils/encoded-tags.js\");\n/* harmony import */ var next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! next/dist/server/send-payload */ \"(rsc)/./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/send-payload.js\");\n/* harmony import */ var next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22__);\n/* harmony import */ var next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! next/dist/shared/lib/no-fallback-error.external */ \"next/dist/shared/lib/no-fallback-error.external\");\n/* harmony import */ var next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_23___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_23__);\n/* harmony import */ var next_dist_client_components_builtin_global_error_js__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! next/dist/client/components/builtin/global-error.js */ \"(rsc)/./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/builtin/global-error.js\");\n/* harmony import */ var next_dist_client_components_builtin_global_error_js__WEBPACK_IMPORTED_MODULE_24___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_builtin_global_error_js__WEBPACK_IMPORTED_MODULE_24__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_25___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_25__);\n/* harmony import */ var next_dist_client_components_redirect_status_code__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! next/dist/client/components/redirect-status-code */ \"(rsc)/./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/redirect-status-code.js\");\n/* harmony import */ var next_dist_client_components_redirect_status_code__WEBPACK_IMPORTED_MODULE_26___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_redirect_status_code__WEBPACK_IMPORTED_MODULE_26__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_25__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\",\"handler\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_25__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/builtin/global-error.js */ \"(rsc)/./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/builtin/global-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/builtin/not-found.js */ \"(rsc)/./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/builtin/not-found.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/builtin/forbidden.js */ \"(rsc)/./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/builtin/forbidden.js\", 23));\nconst module4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/builtin/unauthorized.js */ \"(rsc)/./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/builtin/unauthorized.js\", 23));\nconst page5 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\"));\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page5, \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/app/page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [module0, \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/app/layout.tsx\"],\n'global-error': [module1, \"next/dist/client/components/builtin/global-error.js\"],\n'not-found': [module2, \"next/dist/client/components/builtin/not-found.js\"],\n'forbidden': [module3, \"next/dist/client/components/builtin/forbidden.js\"],\n'unauthorized': [module4, \"next/dist/client/components/builtin/unauthorized.js\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/app/page.tsx\"];\n\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    },\n    distDir: \".next\" || 0,\n    projectDir:  false || ''\n});\nasync function handler(req, res, ctx) {\n    var _this;\n    let srcPage = \"/page\";\n    // turbopack doesn't normalize `/index` in the page name\n    // so we need to to process dynamic routes properly\n    // TODO: fix turbopack providing differing value from webpack\n    if (false) {} else if (srcPage === '/index') {\n        // we always normalize /index specifically\n        srcPage = '/';\n    }\n    const multiZoneDraftMode = \"false\";\n    const initialPostponed = (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'postponed');\n    // TODO: replace with more specific flags\n    const minimalMode = (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'minimalMode');\n    const prepareResult = await routeModule.prepare(req, res, {\n        srcPage,\n        multiZoneDraftMode\n    });\n    if (!prepareResult) {\n        res.statusCode = 400;\n        res.end('Bad Request');\n        ctx.waitUntil == null ? void 0 : ctx.waitUntil.call(ctx, Promise.resolve());\n        return null;\n    }\n    const { buildId, query, params, parsedUrl, pageIsDynamic, buildManifest, nextFontManifest, reactLoadableManifest, serverActionsManifest, clientReferenceManifest, subresourceIntegrityManifest, prerenderManifest, isDraftMode, resolvedPathname, revalidateOnlyGenerated, routerServerContext, nextConfig } = prepareResult;\n    const pathname = parsedUrl.pathname || '/';\n    const normalizedSrcPage = (0,next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_13__.normalizeAppPath)(srcPage);\n    let { isOnDemandRevalidate } = prepareResult;\n    const prerenderInfo = prerenderManifest.dynamicRoutes[normalizedSrcPage];\n    const isPrerendered = prerenderManifest.routes[resolvedPathname];\n    let isSSG = Boolean(prerenderInfo || isPrerendered || prerenderManifest.routes[normalizedSrcPage]);\n    const userAgent = req.headers['user-agent'] || '';\n    const botType = (0,next_dist_shared_lib_router_utils_is_bot__WEBPACK_IMPORTED_MODULE_16__.getBotType)(userAgent);\n    const isHtmlBot = (0,next_dist_server_lib_streaming_metadata__WEBPACK_IMPORTED_MODULE_11__.isHtmlBotRequest)(req);\n    /**\n   * If true, this indicates that the request being made is for an app\n   * prefetch request.\n   */ const isPrefetchRSCRequest = (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'isPrefetchRSCRequest') ?? Boolean(req.headers[next_dist_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_15__.NEXT_ROUTER_PREFETCH_HEADER]);\n    // NOTE: Don't delete headers[RSC] yet, it still needs to be used in renderToHTML later\n    const isRSCRequest = (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'isRSCRequest') ?? Boolean(req.headers[next_dist_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_15__.RSC_HEADER]);\n    const isPossibleServerAction = (0,next_dist_server_lib_server_action_request_meta__WEBPACK_IMPORTED_MODULE_14__.getIsPossibleServerAction)(req);\n    /**\n   * If the route being rendered is an app page, and the ppr feature has been\n   * enabled, then the given route _could_ support PPR.\n   */ const couldSupportPPR = (0,next_dist_server_lib_experimental_ppr__WEBPACK_IMPORTED_MODULE_8__.checkIsAppPPREnabled)(nextConfig.experimental.ppr);\n    // When enabled, this will allow the use of the `?__nextppronly` query to\n    // enable debugging of the static shell.\n    const hasDebugStaticShellQuery =  false && 0;\n    // When enabled, this will allow the use of the `?__nextppronly` query\n    // to enable debugging of the fallback shell.\n    const hasDebugFallbackShellQuery = hasDebugStaticShellQuery && query.__nextppronly === 'fallback';\n    // This page supports PPR if it is marked as being `PARTIALLY_STATIC` in the\n    // prerender manifest and this is an app page.\n    const isRoutePPREnabled = couldSupportPPR && (((_this = prerenderManifest.routes[normalizedSrcPage] ?? prerenderManifest.dynamicRoutes[normalizedSrcPage]) == null ? void 0 : _this.renderingMode) === 'PARTIALLY_STATIC' || // Ideally we'd want to check the appConfig to see if this page has PPR\n    // enabled or not, but that would require plumbing the appConfig through\n    // to the server during development. We assume that the page supports it\n    // but only during development.\n    hasDebugStaticShellQuery && (routeModule.isDev === true || (routerServerContext == null ? void 0 : routerServerContext.experimentalTestProxy) === true));\n    const isDebugStaticShell = hasDebugStaticShellQuery && isRoutePPREnabled;\n    // We should enable debugging dynamic accesses when the static shell\n    // debugging has been enabled and we're also in development mode.\n    const isDebugDynamicAccesses = isDebugStaticShell && routeModule.isDev === true;\n    const isDebugFallbackShell = hasDebugFallbackShellQuery && isRoutePPREnabled;\n    // If we're in minimal mode, then try to get the postponed information from\n    // the request metadata. If available, use it for resuming the postponed\n    // render.\n    const minimalPostponed = isRoutePPREnabled ? initialPostponed : undefined;\n    // If PPR is enabled, and this is a RSC request (but not a prefetch), then\n    // we can use this fact to only generate the flight data for the request\n    // because we can't cache the HTML (as it's also dynamic).\n    const isDynamicRSCRequest = isRoutePPREnabled && isRSCRequest && !isPrefetchRSCRequest;\n    // Need to read this before it's stripped by stripFlightHeaders. We don't\n    // need to transfer it to the request meta because it's only read\n    // within this function; the static segment data should have already been\n    // generated, so we will always either return a static response or a 404.\n    const segmentPrefetchHeader = (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'segmentPrefetchRSCRequest');\n    // TODO: investigate existing bug with shouldServeStreamingMetadata always\n    // being true for a revalidate due to modifying the base-server this.renderOpts\n    // when fixing this to correct logic it causes hydration issue since we set\n    // serveStreamingMetadata to true during export\n    let serveStreamingMetadata = !userAgent ? true : (0,next_dist_server_lib_streaming_metadata__WEBPACK_IMPORTED_MODULE_11__.shouldServeStreamingMetadata)(userAgent, nextConfig.htmlLimitedBots);\n    if (isHtmlBot && isRoutePPREnabled) {\n        isSSG = false;\n        serveStreamingMetadata = false;\n    }\n    // In development, we always want to generate dynamic HTML.\n    let supportsDynamicResponse = // If we're in development, we always support dynamic HTML, unless it's\n    // a data request, in which case we only produce static HTML.\n    routeModule.isDev === true || // If this is not SSG or does not have static paths, then it supports\n    // dynamic HTML.\n    !isSSG || // If this request has provided postponed data, it supports dynamic\n    // HTML.\n    typeof initialPostponed === 'string' || // If this is a dynamic RSC request, then this render supports dynamic\n    // HTML (it's dynamic).\n    isDynamicRSCRequest;\n    // When html bots request PPR page, perform the full dynamic rendering.\n    const shouldWaitOnAllReady = isHtmlBot && isRoutePPREnabled;\n    let ssgCacheKey = null;\n    if (!isDraftMode && isSSG && !supportsDynamicResponse && !isPossibleServerAction && !minimalPostponed && !isDynamicRSCRequest) {\n        ssgCacheKey = resolvedPathname;\n    }\n    // the staticPathKey differs from ssgCacheKey since\n    // ssgCacheKey is null in dev since we're always in \"dynamic\"\n    // mode in dev to bypass the cache, but we still need to honor\n    // dynamicParams = false in dev mode\n    let staticPathKey = ssgCacheKey;\n    if (!staticPathKey && routeModule.isDev) {\n        staticPathKey = resolvedPathname;\n    }\n    const ComponentMod = {\n        ...next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_25__,\n        tree,\n        pages,\n        GlobalError: (next_dist_client_components_builtin_global_error_js__WEBPACK_IMPORTED_MODULE_24___default()),\n        handler,\n        routeModule,\n        __next_app__\n    };\n    // Before rendering (which initializes component tree modules), we have to\n    // set the reference manifests to our global store so Server Action's\n    // encryption util can access to them at the top level of the page module.\n    if (serverActionsManifest && clientReferenceManifest) {\n        (0,next_dist_server_app_render_encryption_utils__WEBPACK_IMPORTED_MODULE_10__.setReferenceManifestsSingleton)({\n            page: srcPage,\n            clientReferenceManifest,\n            serverActionsManifest,\n            serverModuleMap: (0,next_dist_server_app_render_action_utils__WEBPACK_IMPORTED_MODULE_12__.createServerModuleMap)({\n                serverActionsManifest\n            })\n        });\n    }\n    const method = req.method || 'GET';\n    const tracer = (0,next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_3__.getTracer)();\n    const activeSpan = tracer.getActiveScopeSpan();\n    try {\n        const invokeRouteModule = async (span, context)=>{\n            const nextReq = new next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_7__.NodeNextRequest(req);\n            const nextRes = new next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_7__.NodeNextResponse(res);\n            // TODO: adapt for putting the RDC inside the postponed data\n            // If we're in dev, and this isn't a prefetch or a server action,\n            // we should seed the resume data cache.\n            if (true) {\n                if (nextConfig.experimental.dynamicIO && !isPrefetchRSCRequest && !context.renderOpts.isPossibleServerAction) {\n                    const warmup = await routeModule.warmup(nextReq, nextRes, context);\n                    // If the warmup is successful, we should use the resume data\n                    // cache from the warmup.\n                    if (warmup.metadata.renderResumeDataCache) {\n                        context.renderOpts.renderResumeDataCache = warmup.metadata.renderResumeDataCache;\n                    }\n                }\n            }\n            return routeModule.render(nextReq, nextRes, context).finally(()=>{\n                if (!span) return;\n                span.setAttributes({\n                    'http.status_code': res.statusCode,\n                    'next.rsc': false\n                });\n                const rootSpanAttributes = tracer.getRootSpanAttributes();\n                // We were unable to get attributes, probably OTEL is not enabled\n                if (!rootSpanAttributes) {\n                    return;\n                }\n                if (rootSpanAttributes.get('next.span_type') !== next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_5__.BaseServerSpan.handleRequest) {\n                    console.warn(`Unexpected root span type '${rootSpanAttributes.get('next.span_type')}'. Please report this Next.js issue https://github.com/vercel/next.js`);\n                    return;\n                }\n                const route = rootSpanAttributes.get('next.route');\n                if (route) {\n                    const name = `${method} ${route}`;\n                    span.setAttributes({\n                        'next.route': route,\n                        'http.route': route,\n                        'next.span_name': name\n                    });\n                    span.updateName(name);\n                } else {\n                    span.updateName(`${method} ${req.url}`);\n                }\n            });\n        };\n        const doRender = async ({ span, postponed, fallbackRouteParams })=>{\n            const context = {\n                query,\n                params,\n                page: normalizedSrcPage,\n                sharedContext: {\n                    buildId\n                },\n                serverComponentsHmrCache: (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'serverComponentsHmrCache'),\n                fallbackRouteParams,\n                renderOpts: {\n                    App: ()=>null,\n                    Document: ()=>null,\n                    pageConfig: {},\n                    ComponentMod,\n                    Component: (0,next_dist_server_app_render_interop_default__WEBPACK_IMPORTED_MODULE_6__.interopDefault)(ComponentMod),\n                    params,\n                    routeModule,\n                    page: srcPage,\n                    postponed,\n                    shouldWaitOnAllReady,\n                    serveStreamingMetadata,\n                    supportsDynamicResponse: typeof postponed === 'string' || supportsDynamicResponse,\n                    buildManifest,\n                    nextFontManifest,\n                    reactLoadableManifest,\n                    subresourceIntegrityManifest,\n                    serverActionsManifest,\n                    clientReferenceManifest,\n                    setIsrStatus: routerServerContext == null ? void 0 : routerServerContext.setIsrStatus,\n                    dir: routeModule.projectDir,\n                    isDraftMode,\n                    isRevalidate: isSSG && !postponed && !isDynamicRSCRequest,\n                    botType,\n                    isOnDemandRevalidate,\n                    isPossibleServerAction,\n                    assetPrefix: nextConfig.assetPrefix,\n                    nextConfigOutput: nextConfig.output,\n                    crossOrigin: nextConfig.crossOrigin,\n                    trailingSlash: nextConfig.trailingSlash,\n                    previewProps: prerenderManifest.preview,\n                    deploymentId: nextConfig.deploymentId,\n                    enableTainting: nextConfig.experimental.taint,\n                    htmlLimitedBots: nextConfig.htmlLimitedBots,\n                    devtoolSegmentExplorer: nextConfig.experimental.devtoolSegmentExplorer,\n                    reactMaxHeadersLength: nextConfig.reactMaxHeadersLength,\n                    multiZoneDraftMode,\n                    incrementalCache: (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'incrementalCache'),\n                    cacheLifeProfiles: nextConfig.experimental.cacheLife,\n                    basePath: nextConfig.basePath,\n                    serverActions: nextConfig.experimental.serverActions,\n                    ...isDebugStaticShell || isDebugDynamicAccesses ? {\n                        nextExport: true,\n                        supportsDynamicResponse: false,\n                        isStaticGeneration: true,\n                        isRevalidate: true,\n                        isDebugDynamicAccesses: isDebugDynamicAccesses\n                    } : {},\n                    experimental: {\n                        isRoutePPREnabled,\n                        expireTime: nextConfig.expireTime,\n                        staleTimes: nextConfig.experimental.staleTimes,\n                        dynamicIO: Boolean(nextConfig.experimental.dynamicIO),\n                        clientSegmentCache: Boolean(nextConfig.experimental.clientSegmentCache),\n                        dynamicOnHover: Boolean(nextConfig.experimental.dynamicOnHover),\n                        inlineCss: Boolean(nextConfig.experimental.inlineCss),\n                        authInterrupts: Boolean(nextConfig.experimental.authInterrupts),\n                        clientTraceMetadata: nextConfig.experimental.clientTraceMetadata || []\n                    },\n                    waitUntil: ctx.waitUntil,\n                    onClose: (cb)=>{\n                        res.on('close', cb);\n                    },\n                    onAfterTaskError: ()=>{},\n                    onInstrumentationRequestError: (error, _request, errorContext)=>routeModule.onRequestError(req, error, errorContext, routerServerContext),\n                    err: (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'invokeError'),\n                    dev: routeModule.isDev\n                }\n            };\n            const result = await invokeRouteModule(span, context);\n            const { metadata } = result;\n            const { cacheControl, headers = {}, // Add any fetch tags that were on the page to the response headers.\n            fetchTags: cacheTags } = metadata;\n            if (cacheTags) {\n                headers[next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20__.NEXT_CACHE_TAGS_HEADER] = cacheTags;\n            }\n            // Pull any fetch metrics from the render onto the request.\n            ;\n            req.fetchMetrics = metadata.fetchMetrics;\n            // we don't throw static to dynamic errors in dev as isSSG\n            // is a best guess in dev since we don't have the prerender pass\n            // to know whether the path is actually static or not\n            if (isSSG && (cacheControl == null ? void 0 : cacheControl.revalidate) === 0 && !routeModule.isDev && !isRoutePPREnabled) {\n                const staticBailoutInfo = metadata.staticBailoutInfo;\n                const err = Object.defineProperty(new Error(`Page changed from static to dynamic at runtime ${resolvedPathname}${(staticBailoutInfo == null ? void 0 : staticBailoutInfo.description) ? `, reason: ${staticBailoutInfo.description}` : ``}` + `\\nsee more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E132\",\n                    enumerable: false,\n                    configurable: true\n                });\n                if (staticBailoutInfo == null ? void 0 : staticBailoutInfo.stack) {\n                    const stack = staticBailoutInfo.stack;\n                    err.stack = err.message + stack.substring(stack.indexOf('\\n'));\n                }\n                throw err;\n            }\n            return {\n                value: {\n                    kind: next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_17__.CachedRouteKind.APP_PAGE,\n                    html: result,\n                    headers,\n                    rscData: metadata.flightData,\n                    postponed: metadata.postponed,\n                    status: metadata.statusCode,\n                    segmentData: metadata.segmentData\n                },\n                cacheControl\n            };\n        };\n        const responseGenerator = async ({ hasResolved, previousCacheEntry, isRevalidating, span })=>{\n            const isProduction = routeModule.isDev === false;\n            const didRespond = hasResolved || res.writableEnded;\n            // skip on-demand revalidate if cache is not present and\n            // revalidate-if-generated is set\n            if (isOnDemandRevalidate && revalidateOnlyGenerated && !previousCacheEntry && !minimalMode) {\n                if (routerServerContext == null ? void 0 : routerServerContext.render404) {\n                    await routerServerContext.render404(req, res);\n                } else {\n                    res.statusCode = 404;\n                    res.end('This page could not be found');\n                }\n                return null;\n            }\n            let fallbackMode;\n            if (prerenderInfo) {\n                fallbackMode = (0,next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18__.parseFallbackField)(prerenderInfo.fallback);\n            }\n            // When serving a bot request, we want to serve a blocking render and not\n            // the prerendered page. This ensures that the correct content is served\n            // to the bot in the head.\n            if (fallbackMode === next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18__.FallbackMode.PRERENDER && (0,next_dist_shared_lib_router_utils_is_bot__WEBPACK_IMPORTED_MODULE_16__.isBot)(userAgent)) {\n                fallbackMode = next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18__.FallbackMode.BLOCKING_STATIC_RENDER;\n            }\n            if ((previousCacheEntry == null ? void 0 : previousCacheEntry.isStale) === -1) {\n                isOnDemandRevalidate = true;\n            }\n            // TODO: adapt for PPR\n            // only allow on-demand revalidate for fallback: true/blocking\n            // or for prerendered fallback: false paths\n            if (isOnDemandRevalidate && (fallbackMode !== next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18__.FallbackMode.NOT_FOUND || previousCacheEntry)) {\n                fallbackMode = next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18__.FallbackMode.BLOCKING_STATIC_RENDER;\n            }\n            if (!minimalMode && fallbackMode !== next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18__.FallbackMode.BLOCKING_STATIC_RENDER && staticPathKey && !didRespond && !isDraftMode && pageIsDynamic && (isProduction || !isPrerendered)) {\n                // if the page has dynamicParams: false and this pathname wasn't\n                // prerendered trigger the no fallback handling\n                if (// In development, fall through to render to handle missing\n                // getStaticPaths.\n                (isProduction || prerenderInfo) && // When fallback isn't present, abort this render so we 404\n                fallbackMode === next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18__.FallbackMode.NOT_FOUND) {\n                    throw new next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_23__.NoFallbackError();\n                }\n                let fallbackResponse;\n                if (isRoutePPREnabled && !isRSCRequest) {\n                    // We use the response cache here to handle the revalidation and\n                    // management of the fallback shell.\n                    fallbackResponse = await routeModule.handleResponse({\n                        cacheKey: isProduction ? normalizedSrcPage : null,\n                        req,\n                        nextConfig,\n                        routeKind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n                        isFallback: true,\n                        prerenderManifest,\n                        isRoutePPREnabled,\n                        responseGenerator: async ()=>doRender({\n                                span,\n                                // We pass `undefined` as rendering a fallback isn't resumed\n                                // here.\n                                postponed: undefined,\n                                fallbackRouteParams: // If we're in production or we're debugging the fallback\n                                // shell then we should postpone when dynamic params are\n                                // accessed.\n                                isProduction || isDebugFallbackShell ? (0,next_dist_server_request_fallback_params__WEBPACK_IMPORTED_MODULE_9__.getFallbackRouteParams)(normalizedSrcPage) : null\n                            }),\n                        waitUntil: ctx.waitUntil\n                    });\n                    // If the fallback response was set to null, then we should return null.\n                    if (fallbackResponse === null) return null;\n                    // Otherwise, if we did get a fallback response, we should return it.\n                    if (fallbackResponse) {\n                        // Remove the cache control from the response to prevent it from being\n                        // used in the surrounding cache.\n                        delete fallbackResponse.cacheControl;\n                        return fallbackResponse;\n                    }\n                }\n            }\n            // Only requests that aren't revalidating can be resumed. If we have the\n            // minimal postponed data, then we should resume the render with it.\n            const postponed = !isOnDemandRevalidate && !isRevalidating && minimalPostponed ? minimalPostponed : undefined;\n            // When we're in minimal mode, if we're trying to debug the static shell,\n            // we should just return nothing instead of resuming the dynamic render.\n            if ((isDebugStaticShell || isDebugDynamicAccesses) && typeof postponed !== 'undefined') {\n                return {\n                    cacheControl: {\n                        revalidate: 1,\n                        expire: undefined\n                    },\n                    value: {\n                        kind: next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_17__.CachedRouteKind.PAGES,\n                        html: next_dist_server_render_result__WEBPACK_IMPORTED_MODULE_19__[\"default\"].fromStatic(''),\n                        pageData: {},\n                        headers: undefined,\n                        status: undefined\n                    }\n                };\n            }\n            // If this is a dynamic route with PPR enabled and the default route\n            // matches were set, then we should pass the fallback route params to\n            // the renderer as this is a fallback revalidation request.\n            const fallbackRouteParams = pageIsDynamic && isRoutePPREnabled && ((0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'renderFallbackShell') || isDebugFallbackShell) ? (0,next_dist_server_request_fallback_params__WEBPACK_IMPORTED_MODULE_9__.getFallbackRouteParams)(pathname) : null;\n            // Perform the render.\n            return doRender({\n                span,\n                postponed,\n                fallbackRouteParams\n            });\n        };\n        const handleResponse = async (span)=>{\n            var _cacheEntry_value, _cachedData_headers;\n            const cacheEntry = await routeModule.handleResponse({\n                cacheKey: ssgCacheKey,\n                responseGenerator: (c)=>responseGenerator({\n                        span,\n                        ...c\n                    }),\n                routeKind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n                isOnDemandRevalidate,\n                isRoutePPREnabled,\n                req,\n                nextConfig,\n                prerenderManifest,\n                waitUntil: ctx.waitUntil\n            });\n            if (isDraftMode) {\n                res.setHeader('Cache-Control', 'private, no-cache, no-store, max-age=0, must-revalidate');\n            }\n            // In dev, we should not cache pages for any reason.\n            if (routeModule.isDev) {\n                res.setHeader('Cache-Control', 'no-store, must-revalidate');\n            }\n            if (!cacheEntry) {\n                if (ssgCacheKey) {\n                    // A cache entry might not be generated if a response is written\n                    // in `getInitialProps` or `getServerSideProps`, but those shouldn't\n                    // have a cache key. If we do have a cache key but we don't end up\n                    // with a cache entry, then either Next.js or the application has a\n                    // bug that needs fixing.\n                    throw Object.defineProperty(new Error('invariant: cache entry required but not generated'), \"__NEXT_ERROR_CODE\", {\n                        value: \"E62\",\n                        enumerable: false,\n                        configurable: true\n                    });\n                }\n                return null;\n            }\n            if (((_cacheEntry_value = cacheEntry.value) == null ? void 0 : _cacheEntry_value.kind) !== next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_17__.CachedRouteKind.APP_PAGE) {\n                var _cacheEntry_value1;\n                throw Object.defineProperty(new Error(`Invariant app-page handler received invalid cache entry ${(_cacheEntry_value1 = cacheEntry.value) == null ? void 0 : _cacheEntry_value1.kind}`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E707\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n            const didPostpone = typeof cacheEntry.value.postponed === 'string';\n            if (isSSG && // We don't want to send a cache header for requests that contain dynamic\n            // data. If this is a Dynamic RSC request or wasn't a Prefetch RSC\n            // request, then we should set the cache header.\n            !isDynamicRSCRequest && (!didPostpone || isPrefetchRSCRequest)) {\n                if (!minimalMode) {\n                    // set x-nextjs-cache header to match the header\n                    // we set for the image-optimizer\n                    res.setHeader('x-nextjs-cache', isOnDemandRevalidate ? 'REVALIDATED' : cacheEntry.isMiss ? 'MISS' : cacheEntry.isStale ? 'STALE' : 'HIT');\n                }\n                // Set a header used by the client router to signal the response is static\n                // and should respect the `static` cache staleTime value.\n                res.setHeader(next_dist_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_15__.NEXT_IS_PRERENDER_HEADER, '1');\n            }\n            const { value: cachedData } = cacheEntry;\n            // Coerce the cache control parameter from the render.\n            let cacheControl;\n            // If this is a resume request in minimal mode it is streamed with dynamic\n            // content and should not be cached.\n            if (minimalPostponed) {\n                cacheControl = {\n                    revalidate: 0,\n                    expire: undefined\n                };\n            } else if (minimalMode && isRSCRequest && !isPrefetchRSCRequest && isRoutePPREnabled) {\n                cacheControl = {\n                    revalidate: 0,\n                    expire: undefined\n                };\n            } else if (!routeModule.isDev) {\n                // If this is a preview mode request, we shouldn't cache it\n                if (isDraftMode) {\n                    cacheControl = {\n                        revalidate: 0,\n                        expire: undefined\n                    };\n                } else if (!isSSG) {\n                    if (!res.getHeader('Cache-Control')) {\n                        cacheControl = {\n                            revalidate: 0,\n                            expire: undefined\n                        };\n                    }\n                } else if (cacheEntry.cacheControl) {\n                    // If the cache entry has a cache control with a revalidate value that's\n                    // a number, use it.\n                    if (typeof cacheEntry.cacheControl.revalidate === 'number') {\n                        var _cacheEntry_cacheControl;\n                        if (cacheEntry.cacheControl.revalidate < 1) {\n                            throw Object.defineProperty(new Error(`Invalid revalidate configuration provided: ${cacheEntry.cacheControl.revalidate} < 1`), \"__NEXT_ERROR_CODE\", {\n                                value: \"E22\",\n                                enumerable: false,\n                                configurable: true\n                            });\n                        }\n                        cacheControl = {\n                            revalidate: cacheEntry.cacheControl.revalidate,\n                            expire: ((_cacheEntry_cacheControl = cacheEntry.cacheControl) == null ? void 0 : _cacheEntry_cacheControl.expire) ?? nextConfig.expireTime\n                        };\n                    } else {\n                        cacheControl = {\n                            revalidate: next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20__.CACHE_ONE_YEAR,\n                            expire: undefined\n                        };\n                    }\n                }\n            }\n            cacheEntry.cacheControl = cacheControl;\n            if (typeof segmentPrefetchHeader === 'string' && (cachedData == null ? void 0 : cachedData.kind) === next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_17__.CachedRouteKind.APP_PAGE && cachedData.segmentData) {\n                var _cachedData_headers1;\n                // This is a prefetch request issued by the client Segment Cache. These\n                // should never reach the application layer (lambda). We should either\n                // respond from the cache (HIT) or respond with 204 No Content (MISS).\n                // Set a header to indicate that PPR is enabled for this route. This\n                // lets the client distinguish between a regular cache miss and a cache\n                // miss due to PPR being disabled. In other contexts this header is used\n                // to indicate that the response contains dynamic data, but here we're\n                // only using it to indicate that the feature is enabled — the segment\n                // response itself contains whether the data is dynamic.\n                res.setHeader(next_dist_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_15__.NEXT_DID_POSTPONE_HEADER, '2');\n                // Add the cache tags header to the response if it exists and we're in\n                // minimal mode while rendering a static page.\n                const tags = (_cachedData_headers1 = cachedData.headers) == null ? void 0 : _cachedData_headers1[next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20__.NEXT_CACHE_TAGS_HEADER];\n                if (minimalMode && isSSG && tags && typeof tags === 'string') {\n                    res.setHeader(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20__.NEXT_CACHE_TAGS_HEADER, tags);\n                }\n                const matchedSegment = cachedData.segmentData.get(segmentPrefetchHeader);\n                if (matchedSegment !== undefined) {\n                    // Cache hit\n                    return (0,next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22__.sendRenderResult)({\n                        req,\n                        res,\n                        type: 'rsc',\n                        generateEtags: nextConfig.generateEtags,\n                        poweredByHeader: nextConfig.poweredByHeader,\n                        result: next_dist_server_render_result__WEBPACK_IMPORTED_MODULE_19__[\"default\"].fromStatic(matchedSegment),\n                        cacheControl: cacheEntry.cacheControl\n                    });\n                }\n                // Cache miss. Either a cache entry for this route has not been generated\n                // (which technically should not be possible when PPR is enabled, because\n                // at a minimum there should always be a fallback entry) or there's no\n                // match for the requested segment. Respond with a 204 No Content. We\n                // don't bother to respond with 404, because these requests are only\n                // issued as part of a prefetch.\n                res.statusCode = 204;\n                return (0,next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22__.sendRenderResult)({\n                    req,\n                    res,\n                    type: 'rsc',\n                    generateEtags: nextConfig.generateEtags,\n                    poweredByHeader: nextConfig.poweredByHeader,\n                    result: next_dist_server_render_result__WEBPACK_IMPORTED_MODULE_19__[\"default\"].fromStatic(''),\n                    cacheControl: cacheEntry.cacheControl\n                });\n            }\n            // If there's a callback for `onCacheEntry`, call it with the cache entry\n            // and the revalidate options.\n            const onCacheEntry = (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'onCacheEntry');\n            if (onCacheEntry) {\n                const finished = await onCacheEntry({\n                    ...cacheEntry,\n                    // TODO: remove this when upstream doesn't\n                    // always expect this value to be \"PAGE\"\n                    value: {\n                        ...cacheEntry.value,\n                        kind: 'PAGE'\n                    }\n                }, {\n                    url: (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'initURL')\n                });\n                if (finished) {\n                    // TODO: maybe we have to end the request?\n                    return null;\n                }\n            }\n            // If the request has a postponed state and it's a resume request we\n            // should error.\n            if (didPostpone && minimalPostponed) {\n                throw Object.defineProperty(new Error('Invariant: postponed state should not be present on a resume request'), \"__NEXT_ERROR_CODE\", {\n                    value: \"E396\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n            if (cachedData.headers) {\n                const headers = {\n                    ...cachedData.headers\n                };\n                if (!minimalMode || !isSSG) {\n                    delete headers[next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20__.NEXT_CACHE_TAGS_HEADER];\n                }\n                for (let [key, value] of Object.entries(headers)){\n                    if (typeof value === 'undefined') continue;\n                    if (Array.isArray(value)) {\n                        for (const v of value){\n                            res.appendHeader(key, v);\n                        }\n                    } else if (typeof value === 'number') {\n                        value = value.toString();\n                        res.appendHeader(key, value);\n                    } else {\n                        res.appendHeader(key, value);\n                    }\n                }\n            }\n            // Add the cache tags header to the response if it exists and we're in\n            // minimal mode while rendering a static page.\n            const tags = (_cachedData_headers = cachedData.headers) == null ? void 0 : _cachedData_headers[next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20__.NEXT_CACHE_TAGS_HEADER];\n            if (minimalMode && isSSG && tags && typeof tags === 'string') {\n                res.setHeader(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20__.NEXT_CACHE_TAGS_HEADER, tags);\n            }\n            // If the request is a data request, then we shouldn't set the status code\n            // from the response because it should always be 200. This should be gated\n            // behind the experimental PPR flag.\n            if (cachedData.status && (!isRSCRequest || !isRoutePPREnabled)) {\n                res.statusCode = cachedData.status;\n            }\n            // Redirect information is encoded in RSC payload, so we don't need to use redirect status codes\n            if (!minimalMode && cachedData.status && next_dist_client_components_redirect_status_code__WEBPACK_IMPORTED_MODULE_26__.RedirectStatusCode[cachedData.status] && isRSCRequest) {\n                res.statusCode = 200;\n            }\n            // Mark that the request did postpone.\n            if (didPostpone) {\n                res.setHeader(next_dist_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_15__.NEXT_DID_POSTPONE_HEADER, '1');\n            }\n            // we don't go through this block when preview mode is true\n            // as preview mode is a dynamic request (bypasses cache) and doesn't\n            // generate both HTML and payloads in the same request so continue to just\n            // return the generated payload\n            if (isRSCRequest && !isDraftMode) {\n                // If this is a dynamic RSC request, then stream the response.\n                if (typeof cachedData.rscData === 'undefined') {\n                    if (cachedData.postponed) {\n                        throw Object.defineProperty(new Error('Invariant: Expected postponed to be undefined'), \"__NEXT_ERROR_CODE\", {\n                            value: \"E372\",\n                            enumerable: false,\n                            configurable: true\n                        });\n                    }\n                    return (0,next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22__.sendRenderResult)({\n                        req,\n                        res,\n                        type: 'rsc',\n                        generateEtags: nextConfig.generateEtags,\n                        poweredByHeader: nextConfig.poweredByHeader,\n                        result: cachedData.html,\n                        // Dynamic RSC responses cannot be cached, even if they're\n                        // configured with `force-static` because we have no way of\n                        // distinguishing between `force-static` and pages that have no\n                        // postponed state.\n                        // TODO: distinguish `force-static` from pages with no postponed state (static)\n                        cacheControl: isDynamicRSCRequest ? {\n                            revalidate: 0,\n                            expire: undefined\n                        } : cacheEntry.cacheControl\n                    });\n                }\n                // As this isn't a prefetch request, we should serve the static flight\n                // data.\n                return (0,next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22__.sendRenderResult)({\n                    req,\n                    res,\n                    type: 'rsc',\n                    generateEtags: nextConfig.generateEtags,\n                    poweredByHeader: nextConfig.poweredByHeader,\n                    result: next_dist_server_render_result__WEBPACK_IMPORTED_MODULE_19__[\"default\"].fromStatic(cachedData.rscData),\n                    cacheControl: cacheEntry.cacheControl\n                });\n            }\n            // This is a request for HTML data.\n            let body = cachedData.html;\n            // If there's no postponed state, we should just serve the HTML. This\n            // should also be the case for a resume request because it's completed\n            // as a server render (rather than a static render).\n            if (!didPostpone || minimalMode) {\n                return (0,next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22__.sendRenderResult)({\n                    req,\n                    res,\n                    type: 'html',\n                    generateEtags: nextConfig.generateEtags,\n                    poweredByHeader: nextConfig.poweredByHeader,\n                    result: body,\n                    cacheControl: cacheEntry.cacheControl\n                });\n            }\n            // If we're debugging the static shell or the dynamic API accesses, we\n            // should just serve the HTML without resuming the render. The returned\n            // HTML will be the static shell so all the Dynamic API's will be used\n            // during static generation.\n            if (isDebugStaticShell || isDebugDynamicAccesses) {\n                // Since we're not resuming the render, we need to at least add the\n                // closing body and html tags to create valid HTML.\n                body.chain(new ReadableStream({\n                    start (controller) {\n                        controller.enqueue(next_dist_server_stream_utils_encoded_tags__WEBPACK_IMPORTED_MODULE_21__.ENCODED_TAGS.CLOSED.BODY_AND_HTML);\n                        controller.close();\n                    }\n                }));\n                return (0,next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22__.sendRenderResult)({\n                    req,\n                    res,\n                    type: 'html',\n                    generateEtags: nextConfig.generateEtags,\n                    poweredByHeader: nextConfig.poweredByHeader,\n                    result: body,\n                    cacheControl: {\n                        revalidate: 0,\n                        expire: undefined\n                    }\n                });\n            }\n            // This request has postponed, so let's create a new transformer that the\n            // dynamic data can pipe to that will attach the dynamic data to the end\n            // of the response.\n            const transformer = new TransformStream();\n            body.chain(transformer.readable);\n            // Perform the render again, but this time, provide the postponed state.\n            // We don't await because we want the result to start streaming now, and\n            // we've already chained the transformer's readable to the render result.\n            doRender({\n                span,\n                postponed: cachedData.postponed,\n                // This is a resume render, not a fallback render, so we don't need to\n                // set this.\n                fallbackRouteParams: null\n            }).then(async (result)=>{\n                var _result_value;\n                if (!result) {\n                    throw Object.defineProperty(new Error('Invariant: expected a result to be returned'), \"__NEXT_ERROR_CODE\", {\n                        value: \"E463\",\n                        enumerable: false,\n                        configurable: true\n                    });\n                }\n                if (((_result_value = result.value) == null ? void 0 : _result_value.kind) !== next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_17__.CachedRouteKind.APP_PAGE) {\n                    var _result_value1;\n                    throw Object.defineProperty(new Error(`Invariant: expected a page response, got ${(_result_value1 = result.value) == null ? void 0 : _result_value1.kind}`), \"__NEXT_ERROR_CODE\", {\n                        value: \"E305\",\n                        enumerable: false,\n                        configurable: true\n                    });\n                }\n                // Pipe the resume result to the transformer.\n                await result.value.html.pipeTo(transformer.writable);\n            }).catch((err)=>{\n                // An error occurred during piping or preparing the render, abort\n                // the transformers writer so we can terminate the stream.\n                transformer.writable.abort(err).catch((e)=>{\n                    console.error(\"couldn't abort transformer\", e);\n                });\n            });\n            return (0,next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22__.sendRenderResult)({\n                req,\n                res,\n                type: 'html',\n                generateEtags: nextConfig.generateEtags,\n                poweredByHeader: nextConfig.poweredByHeader,\n                result: body,\n                // We don't want to cache the response if it has postponed data because\n                // the response being sent to the client it's dynamic parts are streamed\n                // to the client on the same request.\n                cacheControl: {\n                    revalidate: 0,\n                    expire: undefined\n                }\n            });\n        };\n        // TODO: activeSpan code path is for when wrapped by\n        // next-server can be removed when this is no longer used\n        if (activeSpan) {\n            await handleResponse(activeSpan);\n        } else {\n            return await tracer.withPropagatedContext(req.headers, ()=>tracer.trace(next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_5__.BaseServerSpan.handleRequest, {\n                    spanName: `${method} ${req.url}`,\n                    kind: next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_3__.SpanKind.SERVER,\n                    attributes: {\n                        'http.method': method,\n                        'http.target': req.url\n                    }\n                }, handleResponse));\n        }\n    } catch (err) {\n        // if we aren't wrapped by base-server handle here\n        if (!activeSpan) {\n            await routeModule.onRequestError(req, err, {\n                routerKind: 'App Router',\n                routePath: srcPage,\n                routeType: 'render',\n                revalidateReason: (0,next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_2__.getRevalidateReason)({\n                    isRevalidate: isSSG,\n                    isOnDemandRevalidate\n                })\n            }, routerServerContext);\n        }\n        // rethrow so that we can handle serving error page\n        throw err;\n    }\n}\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Fuser%2FDocuments%2Faugment-projects%2Fnextjs-superNode%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fuser%2FDocuments%2Faugment-projects%2Fnextjs-superNode&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fuser%2FDocuments%2Faugment-projects%2Fnextjs-superNode%2Fnode_modules%2F.pnpm%2Fnext%4015.4.2_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fbuiltin%2Fglobal-error.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fuser%2FDocuments%2Faugment-projects%2Fnextjs-superNode%2Fnode_modules%2F.pnpm%2Fnext%4015.4.2_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fuser%2FDocuments%2Faugment-projects%2Fnextjs-superNode%2Fnode_modules%2F.pnpm%2Fnext%4015.4.2_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fuser%2FDocuments%2Faugment-projects%2Fnextjs-superNode%2Fnode_modules%2F.pnpm%2Fnext%4015.4.2_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fuser%2FDocuments%2Faugment-projects%2Fnextjs-superNode%2Fnode_modules%2F.pnpm%2Fnext%4015.4.2_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fuser%2FDocuments%2Faugment-projects%2Fnextjs-superNode%2Fnode_modules%2F.pnpm%2Fnext%4015.4.2_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fuser%2FDocuments%2Faugment-projects%2Fnextjs-superNode%2Fnode_modules%2F.pnpm%2Fnext%4015.4.2_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fuser%2FDocuments%2Faugment-projects%2Fnextjs-superNode%2Fnode_modules%2F.pnpm%2Fnext%4015.4.2_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fuser%2FDocuments%2Faugment-projects%2Fnextjs-superNode%2Fnode_modules%2F.pnpm%2Fnext%4015.4.2_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%2Fnode_modules%2Fnext%2Fdist%2Flib%2Fmetadata%2Fgenerate%2Ficon-mark.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fuser%2FDocuments%2Faugment-projects%2Fnextjs-superNode%2Fnode_modules%2F.pnpm%2Fnext%4015.4.2_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%2Fnode_modules%2Fnext%2Fdist%2Fnext-devtools%2Fuserspace%2Fapp%2Fsegment-explorer-node.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fuser%2FDocuments%2Faugment-projects%2Fnextjs-superNode%2Fnode_modules%2F.pnpm%2Fnext%4015.4.2_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fbuiltin%2Fglobal-error.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fuser%2FDocuments%2Faugment-projects%2Fnextjs-superNode%2Fnode_modules%2F.pnpm%2Fnext%4015.4.2_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fuser%2FDocuments%2Faugment-projects%2Fnextjs-superNode%2Fnode_modules%2F.pnpm%2Fnext%4015.4.2_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fuser%2FDocuments%2Faugment-projects%2Fnextjs-superNode%2Fnode_modules%2F.pnpm%2Fnext%4015.4.2_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fuser%2FDocuments%2Faugment-projects%2Fnextjs-superNode%2Fnode_modules%2F.pnpm%2Fnext%4015.4.2_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fuser%2FDocuments%2Faugment-projects%2Fnextjs-superNode%2Fnode_modules%2F.pnpm%2Fnext%4015.4.2_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fuser%2FDocuments%2Faugment-projects%2Fnextjs-superNode%2Fnode_modules%2F.pnpm%2Fnext%4015.4.2_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fuser%2FDocuments%2Faugment-projects%2Fnextjs-superNode%2Fnode_modules%2F.pnpm%2Fnext%4015.4.2_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fuser%2FDocuments%2Faugment-projects%2Fnextjs-superNode%2Fnode_modules%2F.pnpm%2Fnext%4015.4.2_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%2Fnode_modules%2Fnext%2Fdist%2Flib%2Fmetadata%2Fgenerate%2Ficon-mark.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fuser%2FDocuments%2Faugment-projects%2Fnextjs-superNode%2Fnode_modules%2F.pnpm%2Fnext%4015.4.2_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%2Fnode_modules%2Fnext%2Fdist%2Fnext-devtools%2Fuserspace%2Fapp%2Fsegment-explorer-node.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/builtin/global-error.js */ \"(rsc)/./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/builtin/global-error.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/lib/metadata/generate/icon-mark.js */ \"(rsc)/./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/lib/metadata/generate/icon-mark.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/next-devtools/userspace/app/segment-explorer-node.js */ \"(rsc)/./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/next-devtools/userspace/app/segment-explorer-node.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fuser%2FDocuments%2Faugment-projects%2Fnextjs-superNode%2Fnode_modules%2F.pnpm%2Fnext%4015.4.2_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fbuiltin%2Fglobal-error.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fuser%2FDocuments%2Faugment-projects%2Fnextjs-superNode%2Fnode_modules%2F.pnpm%2Fnext%4015.4.2_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fuser%2FDocuments%2Faugment-projects%2Fnextjs-superNode%2Fnode_modules%2F.pnpm%2Fnext%4015.4.2_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fuser%2FDocuments%2Faugment-projects%2Fnextjs-superNode%2Fnode_modules%2F.pnpm%2Fnext%4015.4.2_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fuser%2FDocuments%2Faugment-projects%2Fnextjs-superNode%2Fnode_modules%2F.pnpm%2Fnext%4015.4.2_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fuser%2FDocuments%2Faugment-projects%2Fnextjs-superNode%2Fnode_modules%2F.pnpm%2Fnext%4015.4.2_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fuser%2FDocuments%2Faugment-projects%2Fnextjs-superNode%2Fnode_modules%2F.pnpm%2Fnext%4015.4.2_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fuser%2FDocuments%2Faugment-projects%2Fnextjs-superNode%2Fnode_modules%2F.pnpm%2Fnext%4015.4.2_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fuser%2FDocuments%2Faugment-projects%2Fnextjs-superNode%2Fnode_modules%2F.pnpm%2Fnext%4015.4.2_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%2Fnode_modules%2Fnext%2Fdist%2Flib%2Fmetadata%2Fgenerate%2Ficon-mark.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fuser%2FDocuments%2Faugment-projects%2Fnextjs-superNode%2Fnode_modules%2F.pnpm%2Fnext%4015.4.2_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%2Fnode_modules%2Fnext%2Fdist%2Fnext-devtools%2Fuserspace%2Fapp%2Fsegment-explorer-node.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fuser%2FDocuments%2Faugment-projects%2Fnextjs-superNode%2Fnode_modules%2F.pnpm%2Fnext%4015.4.2_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fuser%2FDocuments%2Faugment-projects%2Fnextjs-superNode%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fuser%2FDocuments%2Faugment-projects%2Fnextjs-superNode%2Fnode_modules%2F.pnpm%2Fnext%4015.4.2_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fuser%2FDocuments%2Faugment-projects%2Fnextjs-superNode%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fuser%2FDocuments%2Faugment-projects%2Fnextjs-superNode%2Fsrc%2Fcomponents%2FWorkflowCanvas.tsx%22%2C%22ids%22%3A%5B%22WorkflowCanvas%22%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fuser%2FDocuments%2Faugment-projects%2Fnextjs-superNode%2Fsrc%2Fcomponents%2FWorkflowCanvas.tsx%22%2C%22ids%22%3A%5B%22WorkflowCanvas%22%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/WorkflowCanvas.tsx */ \"(rsc)/./src/components/WorkflowCanvas.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vbmV4dEAxNS40LjJfcmVhY3QtZG9tQDE4LjMuMV9yZWFjdEAxOC4zLjFfX3JlYWN0QDE4LjMuMS9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGdXNlciUyRkRvY3VtZW50cyUyRmF1Z21lbnQtcHJvamVjdHMlMkZuZXh0anMtc3VwZXJOb2RlJTJGc3JjJTJGY29tcG9uZW50cyUyRldvcmtmbG93Q2FudmFzLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMldvcmtmbG93Q2FudmFzJTIyJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxrTEFBa0siLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcIldvcmtmbG93Q2FudmFzXCJdICovIFwiL1VzZXJzL3VzZXIvRG9jdW1lbnRzL2F1Z21lbnQtcHJvamVjdHMvbmV4dGpzLXN1cGVyTm9kZS9zcmMvY29tcG9uZW50cy9Xb3JrZmxvd0NhbnZhcy50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fuser%2FDocuments%2Faugment-projects%2Fnextjs-superNode%2Fsrc%2Fcomponents%2FWorkflowCanvas.tsx%22%2C%22ids%22%3A%5B%22WorkflowCanvas%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"955ec95b3e6f\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyIvVXNlcnMvdXNlci9Eb2N1bWVudHMvYXVnbWVudC1wcm9qZWN0cy9uZXh0anMtc3VwZXJOb2RlL3NyYy9hcHAvZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI5NTVlYzk1YjNlNmZcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src/app/layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n\n\n\nconst metadata = {\n    title: 'SuperNode - 工作流程序',\n    description: '基于Next.js的可视化工作流程序'\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"zh-CN\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default().className),\n            children: children\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/app/layout.tsx\",\n            lineNumber: 19,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/app/layout.tsx\",\n        lineNumber: 18,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFJTUE7QUFGaUI7QUFJaEIsTUFBTUMsV0FBcUI7SUFDaENDLE9BQU87SUFDUEMsYUFBYTtBQUNmLEVBQUU7QUFFYSxTQUFTQyxXQUFXLEVBQ2pDQyxRQUFRLEVBR1Q7SUFDQyxxQkFDRSw4REFBQ0M7UUFBS0MsTUFBSztrQkFDVCw0RUFBQ0M7WUFBS0MsV0FBV1QsK0pBQWU7c0JBQUdLOzs7Ozs7Ozs7OztBQUd6QyIsInNvdXJjZXMiOlsiL1VzZXJzL3VzZXIvRG9jdW1lbnRzL2F1Z21lbnQtcHJvamVjdHMvbmV4dGpzLXN1cGVyTm9kZS9zcmMvYXBwL2xheW91dC50c3giXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHR5cGUgeyBNZXRhZGF0YSB9IGZyb20gJ25leHQnO1xuaW1wb3J0IHsgSW50ZXIgfSBmcm9tICduZXh0L2ZvbnQvZ29vZ2xlJztcbmltcG9ydCAnLi9nbG9iYWxzLmNzcyc7XG5cbmNvbnN0IGludGVyID0gSW50ZXIoeyBzdWJzZXRzOiBbJ2xhdGluJ10gfSk7XG5cbmV4cG9ydCBjb25zdCBtZXRhZGF0YTogTWV0YWRhdGEgPSB7XG4gIHRpdGxlOiAnU3VwZXJOb2RlIC0g5bel5L2c5rWB56iL5bqPJyxcbiAgZGVzY3JpcHRpb246ICfln7rkuo5OZXh0Lmpz55qE5Y+v6KeG5YyW5bel5L2c5rWB56iL5bqPJyxcbn07XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFJvb3RMYXlvdXQoe1xuICBjaGlsZHJlbixcbn06IHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZTtcbn0pIHtcbiAgcmV0dXJuIChcbiAgICA8aHRtbCBsYW5nPVwiemgtQ05cIj5cbiAgICAgIDxib2R5IGNsYXNzTmFtZT17aW50ZXIuY2xhc3NOYW1lfT57Y2hpbGRyZW59PC9ib2R5PlxuICAgIDwvaHRtbD5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJpbnRlciIsIm1ldGFkYXRhIiwidGl0bGUiLCJkZXNjcmlwdGlvbiIsIlJvb3RMYXlvdXQiLCJjaGlsZHJlbiIsImh0bWwiLCJsYW5nIiwiYm9keSIsImNsYXNzTmFtZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_WorkflowCanvas__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/WorkflowCanvas */ \"(rsc)/./src/components/WorkflowCanvas.tsx\");\n\n\nfunction Home() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n        className: \"w-full h-screen\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_WorkflowCanvas__WEBPACK_IMPORTED_MODULE_1__.WorkflowCanvas, {}, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/app/page.tsx\",\n            lineNumber: 6,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/app/page.tsx\",\n        lineNumber: 5,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL3BhZ2UudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQTZEO0FBRTlDLFNBQVNDO0lBQ3RCLHFCQUNFLDhEQUFDQztRQUFLQyxXQUFVO2tCQUNkLDRFQUFDSCxzRUFBY0E7Ozs7Ozs7Ozs7QUFHckIiLCJzb3VyY2VzIjpbIi9Vc2Vycy91c2VyL0RvY3VtZW50cy9hdWdtZW50LXByb2plY3RzL25leHRqcy1zdXBlck5vZGUvc3JjL2FwcC9wYWdlLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBXb3JrZmxvd0NhbnZhcyB9IGZyb20gJ0AvY29tcG9uZW50cy9Xb3JrZmxvd0NhbnZhcyc7XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIEhvbWUoKSB7XG4gIHJldHVybiAoXG4gICAgPG1haW4gY2xhc3NOYW1lPVwidy1mdWxsIGgtc2NyZWVuXCI+XG4gICAgICA8V29ya2Zsb3dDYW52YXMgLz5cbiAgICA8L21haW4+XG4gICk7XG59XG4iXSwibmFtZXMiOlsiV29ya2Zsb3dDYW52YXMiLCJIb21lIiwibWFpbiIsImNsYXNzTmFtZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/page.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/WorkflowCanvas.tsx":
/*!*******************************************!*\
  !*** ./src/components/WorkflowCanvas.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   WorkflowCanvas: () => (/* binding */ WorkflowCanvas)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server */ "(rsc)/./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server.js");
/* harmony import */ var react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__);

const WorkflowCanvas = (0,react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call WorkflowCanvas() from the server but WorkflowCanvas is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/WorkflowCanvas.tsx",
"WorkflowCanvas",
);

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fuser%2FDocuments%2Faugment-projects%2Fnextjs-superNode%2Fnode_modules%2F.pnpm%2Fnext%4015.4.2_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fbuiltin%2Fglobal-error.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fuser%2FDocuments%2Faugment-projects%2Fnextjs-superNode%2Fnode_modules%2F.pnpm%2Fnext%4015.4.2_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fuser%2FDocuments%2Faugment-projects%2Fnextjs-superNode%2Fnode_modules%2F.pnpm%2Fnext%4015.4.2_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fuser%2FDocuments%2Faugment-projects%2Fnextjs-superNode%2Fnode_modules%2F.pnpm%2Fnext%4015.4.2_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fuser%2FDocuments%2Faugment-projects%2Fnextjs-superNode%2Fnode_modules%2F.pnpm%2Fnext%4015.4.2_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fuser%2FDocuments%2Faugment-projects%2Fnextjs-superNode%2Fnode_modules%2F.pnpm%2Fnext%4015.4.2_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fuser%2FDocuments%2Faugment-projects%2Fnextjs-superNode%2Fnode_modules%2F.pnpm%2Fnext%4015.4.2_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fuser%2FDocuments%2Faugment-projects%2Fnextjs-superNode%2Fnode_modules%2F.pnpm%2Fnext%4015.4.2_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fuser%2FDocuments%2Faugment-projects%2Fnextjs-superNode%2Fnode_modules%2F.pnpm%2Fnext%4015.4.2_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%2Fnode_modules%2Fnext%2Fdist%2Flib%2Fmetadata%2Fgenerate%2Ficon-mark.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fuser%2FDocuments%2Faugment-projects%2Fnextjs-superNode%2Fnode_modules%2F.pnpm%2Fnext%4015.4.2_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%2Fnode_modules%2Fnext%2Fdist%2Fnext-devtools%2Fuserspace%2Fapp%2Fsegment-explorer-node.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fuser%2FDocuments%2Faugment-projects%2Fnextjs-superNode%2Fnode_modules%2F.pnpm%2Fnext%4015.4.2_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fbuiltin%2Fglobal-error.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fuser%2FDocuments%2Faugment-projects%2Fnextjs-superNode%2Fnode_modules%2F.pnpm%2Fnext%4015.4.2_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fuser%2FDocuments%2Faugment-projects%2Fnextjs-superNode%2Fnode_modules%2F.pnpm%2Fnext%4015.4.2_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fuser%2FDocuments%2Faugment-projects%2Fnextjs-superNode%2Fnode_modules%2F.pnpm%2Fnext%4015.4.2_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fuser%2FDocuments%2Faugment-projects%2Fnextjs-superNode%2Fnode_modules%2F.pnpm%2Fnext%4015.4.2_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fuser%2FDocuments%2Faugment-projects%2Fnextjs-superNode%2Fnode_modules%2F.pnpm%2Fnext%4015.4.2_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fuser%2FDocuments%2Faugment-projects%2Fnextjs-superNode%2Fnode_modules%2F.pnpm%2Fnext%4015.4.2_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fuser%2FDocuments%2Faugment-projects%2Fnextjs-superNode%2Fnode_modules%2F.pnpm%2Fnext%4015.4.2_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fuser%2FDocuments%2Faugment-projects%2Fnextjs-superNode%2Fnode_modules%2F.pnpm%2Fnext%4015.4.2_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%2Fnode_modules%2Fnext%2Fdist%2Flib%2Fmetadata%2Fgenerate%2Ficon-mark.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fuser%2FDocuments%2Faugment-projects%2Fnextjs-superNode%2Fnode_modules%2F.pnpm%2Fnext%4015.4.2_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%2Fnode_modules%2Fnext%2Fdist%2Fnext-devtools%2Fuserspace%2Fapp%2Fsegment-explorer-node.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/builtin/global-error.js */ \"(ssr)/./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/builtin/global-error.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/lib/metadata/generate/icon-mark.js */ \"(ssr)/./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/lib/metadata/generate/icon-mark.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/next-devtools/userspace/app/segment-explorer-node.js */ \"(ssr)/./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/next-devtools/userspace/app/segment-explorer-node.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vbmV4dEAxNS40LjJfcmVhY3QtZG9tQDE4LjMuMV9yZWFjdEAxOC4zLjFfX3JlYWN0QDE4LjMuMS9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGdXNlciUyRkRvY3VtZW50cyUyRmF1Z21lbnQtcHJvamVjdHMlMkZuZXh0anMtc3VwZXJOb2RlJTJGbm9kZV9tb2R1bGVzJTJGLnBucG0lMkZuZXh0JTQwMTUuNC4yX3JlYWN0LWRvbSU0MDE4LjMuMV9yZWFjdCU0MDE4LjMuMV9fcmVhY3QlNDAxOC4zLjElMkZub2RlX21vZHVsZXMlMkZuZXh0JTJGZGlzdCUyRmNsaWVudCUyRmNvbXBvbmVudHMlMkZidWlsdGluJTJGZ2xvYmFsLWVycm9yLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGdXNlciUyRkRvY3VtZW50cyUyRmF1Z21lbnQtcHJvamVjdHMlMkZuZXh0anMtc3VwZXJOb2RlJTJGbm9kZV9tb2R1bGVzJTJGLnBucG0lMkZuZXh0JTQwMTUuNC4yX3JlYWN0LWRvbSU0MDE4LjMuMV9yZWFjdCU0MDE4LjMuMV9fcmVhY3QlNDAxOC4zLjElMkZub2RlX21vZHVsZXMlMkZuZXh0JTJGZGlzdCUyRmNsaWVudCUyRmNvbXBvbmVudHMlMkZjbGllbnQtcGFnZS5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjIlMkZVc2VycyUyRnVzZXIlMkZEb2N1bWVudHMlMkZhdWdtZW50LXByb2plY3RzJTJGbmV4dGpzLXN1cGVyTm9kZSUyRm5vZGVfbW9kdWxlcyUyRi5wbnBtJTJGbmV4dCU0MDE1LjQuMl9yZWFjdC1kb20lNDAxOC4zLjFfcmVhY3QlNDAxOC4zLjFfX3JlYWN0JTQwMTguMy4xJTJGbm9kZV9tb2R1bGVzJTJGbmV4dCUyRmRpc3QlMkZjbGllbnQlMkZjb21wb25lbnRzJTJGY2xpZW50LXNlZ21lbnQuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGVXNlcnMlMkZ1c2VyJTJGRG9jdW1lbnRzJTJGYXVnbWVudC1wcm9qZWN0cyUyRm5leHRqcy1zdXBlck5vZGUlMkZub2RlX21vZHVsZXMlMkYucG5wbSUyRm5leHQlNDAxNS40LjJfcmVhY3QtZG9tJTQwMTguMy4xX3JlYWN0JTQwMTguMy4xX19yZWFjdCU0MDE4LjMuMSUyRm5vZGVfbW9kdWxlcyUyRm5leHQlMkZkaXN0JTJGY2xpZW50JTJGY29tcG9uZW50cyUyRmh0dHAtYWNjZXNzLWZhbGxiYWNrJTJGZXJyb3ItYm91bmRhcnkuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGVXNlcnMlMkZ1c2VyJTJGRG9jdW1lbnRzJTJGYXVnbWVudC1wcm9qZWN0cyUyRm5leHRqcy1zdXBlck5vZGUlMkZub2RlX21vZHVsZXMlMkYucG5wbSUyRm5leHQlNDAxNS40LjJfcmVhY3QtZG9tJTQwMTguMy4xX3JlYWN0JTQwMTguMy4xX19yZWFjdCU0MDE4LjMuMSUyRm5vZGVfbW9kdWxlcyUyRm5leHQlMkZkaXN0JTJGY2xpZW50JTJGY29tcG9uZW50cyUyRmxheW91dC1yb3V0ZXIuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGVXNlcnMlMkZ1c2VyJTJGRG9jdW1lbnRzJTJGYXVnbWVudC1wcm9qZWN0cyUyRm5leHRqcy1zdXBlck5vZGUlMkZub2RlX21vZHVsZXMlMkYucG5wbSUyRm5leHQlNDAxNS40LjJfcmVhY3QtZG9tJTQwMTguMy4xX3JlYWN0JTQwMTguMy4xX19yZWFjdCU0MDE4LjMuMSUyRm5vZGVfbW9kdWxlcyUyRm5leHQlMkZkaXN0JTJGY2xpZW50JTJGY29tcG9uZW50cyUyRm1ldGFkYXRhJTJGYXN5bmMtbWV0YWRhdGEuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGVXNlcnMlMkZ1c2VyJTJGRG9jdW1lbnRzJTJGYXVnbWVudC1wcm9qZWN0cyUyRm5leHRqcy1zdXBlck5vZGUlMkZub2RlX21vZHVsZXMlMkYucG5wbSUyRm5leHQlNDAxNS40LjJfcmVhY3QtZG9tJTQwMTguMy4xX3JlYWN0JTQwMTguMy4xX19yZWFjdCU0MDE4LjMuMSUyRm5vZGVfbW9kdWxlcyUyRm5leHQlMkZkaXN0JTJGY2xpZW50JTJGY29tcG9uZW50cyUyRm1ldGFkYXRhJTJGbWV0YWRhdGEtYm91bmRhcnkuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGVXNlcnMlMkZ1c2VyJTJGRG9jdW1lbnRzJTJGYXVnbWVudC1wcm9qZWN0cyUyRm5leHRqcy1zdXBlck5vZGUlMkZub2RlX21vZHVsZXMlMkYucG5wbSUyRm5leHQlNDAxNS40LjJfcmVhY3QtZG9tJTQwMTguMy4xX3JlYWN0JTQwMTguMy4xX19yZWFjdCU0MDE4LjMuMSUyRm5vZGVfbW9kdWxlcyUyRm5leHQlMkZkaXN0JTJGY2xpZW50JTJGY29tcG9uZW50cyUyRnJlbmRlci1mcm9tLXRlbXBsYXRlLWNvbnRleHQuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGVXNlcnMlMkZ1c2VyJTJGRG9jdW1lbnRzJTJGYXVnbWVudC1wcm9qZWN0cyUyRm5leHRqcy1zdXBlck5vZGUlMkZub2RlX21vZHVsZXMlMkYucG5wbSUyRm5leHQlNDAxNS40LjJfcmVhY3QtZG9tJTQwMTguMy4xX3JlYWN0JTQwMTguMy4xX19yZWFjdCU0MDE4LjMuMSUyRm5vZGVfbW9kdWxlcyUyRm5leHQlMkZkaXN0JTJGbGliJTJGbWV0YWRhdGElMkZnZW5lcmF0ZSUyRmljb24tbWFyay5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjIlMkZVc2VycyUyRnVzZXIlMkZEb2N1bWVudHMlMkZhdWdtZW50LXByb2plY3RzJTJGbmV4dGpzLXN1cGVyTm9kZSUyRm5vZGVfbW9kdWxlcyUyRi5wbnBtJTJGbmV4dCU0MDE1LjQuMl9yZWFjdC1kb20lNDAxOC4zLjFfcmVhY3QlNDAxOC4zLjFfX3JlYWN0JTQwMTguMy4xJTJGbm9kZV9tb2R1bGVzJTJGbmV4dCUyRmRpc3QlMkZuZXh0LWRldnRvb2xzJTJGdXNlcnNwYWNlJTJGYXBwJTJGc2VnbWVudC1leHBsb3Jlci1ub2RlLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSw0WUFBd087QUFDeE87QUFDQSwwWEFBK047QUFDL047QUFDQSxnWUFBa087QUFDbE87QUFDQSwwYUFBdVA7QUFDdlA7QUFDQSw4WEFBaU87QUFDak87QUFDQSxrWkFBMk87QUFDM087QUFDQSx3WkFBOE87QUFDOU87QUFDQSw0WkFBZ1A7QUFDaFA7QUFDQSx3WEFBaU87QUFDak87QUFDQSxrYUFBbVAiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Vc2Vycy91c2VyL0RvY3VtZW50cy9hdWdtZW50LXByb2plY3RzL25leHRqcy1zdXBlck5vZGUvbm9kZV9tb2R1bGVzLy5wbnBtL25leHRAMTUuNC4yX3JlYWN0LWRvbUAxOC4zLjFfcmVhY3RAMTguMy4xX19yZWFjdEAxOC4zLjEvbm9kZV9tb2R1bGVzL25leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9idWlsdGluL2dsb2JhbC1lcnJvci5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL3VzZXIvRG9jdW1lbnRzL2F1Z21lbnQtcHJvamVjdHMvbmV4dGpzLXN1cGVyTm9kZS9ub2RlX21vZHVsZXMvLnBucG0vbmV4dEAxNS40LjJfcmVhY3QtZG9tQDE4LjMuMV9yZWFjdEAxOC4zLjFfX3JlYWN0QDE4LjMuMS9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL2NsaWVudC1wYWdlLmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvdXNlci9Eb2N1bWVudHMvYXVnbWVudC1wcm9qZWN0cy9uZXh0anMtc3VwZXJOb2RlL25vZGVfbW9kdWxlcy8ucG5wbS9uZXh0QDE1LjQuMl9yZWFjdC1kb21AMTguMy4xX3JlYWN0QDE4LjMuMV9fcmVhY3RAMTguMy4xL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvY2xpZW50LXNlZ21lbnQuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Vc2Vycy91c2VyL0RvY3VtZW50cy9hdWdtZW50LXByb2plY3RzL25leHRqcy1zdXBlck5vZGUvbm9kZV9tb2R1bGVzLy5wbnBtL25leHRAMTUuNC4yX3JlYWN0LWRvbUAxOC4zLjFfcmVhY3RAMTguMy4xX19yZWFjdEAxOC4zLjEvbm9kZV9tb2R1bGVzL25leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9odHRwLWFjY2Vzcy1mYWxsYmFjay9lcnJvci1ib3VuZGFyeS5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL3VzZXIvRG9jdW1lbnRzL2F1Z21lbnQtcHJvamVjdHMvbmV4dGpzLXN1cGVyTm9kZS9ub2RlX21vZHVsZXMvLnBucG0vbmV4dEAxNS40LjJfcmVhY3QtZG9tQDE4LjMuMV9yZWFjdEAxOC4zLjFfX3JlYWN0QDE4LjMuMS9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL2xheW91dC1yb3V0ZXIuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Vc2Vycy91c2VyL0RvY3VtZW50cy9hdWdtZW50LXByb2plY3RzL25leHRqcy1zdXBlck5vZGUvbm9kZV9tb2R1bGVzLy5wbnBtL25leHRAMTUuNC4yX3JlYWN0LWRvbUAxOC4zLjFfcmVhY3RAMTguMy4xX19yZWFjdEAxOC4zLjEvbm9kZV9tb2R1bGVzL25leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9tZXRhZGF0YS9hc3luYy1tZXRhZGF0YS5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL3VzZXIvRG9jdW1lbnRzL2F1Z21lbnQtcHJvamVjdHMvbmV4dGpzLXN1cGVyTm9kZS9ub2RlX21vZHVsZXMvLnBucG0vbmV4dEAxNS40LjJfcmVhY3QtZG9tQDE4LjMuMV9yZWFjdEAxOC4zLjFfX3JlYWN0QDE4LjMuMS9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL21ldGFkYXRhL21ldGFkYXRhLWJvdW5kYXJ5LmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvdXNlci9Eb2N1bWVudHMvYXVnbWVudC1wcm9qZWN0cy9uZXh0anMtc3VwZXJOb2RlL25vZGVfbW9kdWxlcy8ucG5wbS9uZXh0QDE1LjQuMl9yZWFjdC1kb21AMTguMy4xX3JlYWN0QDE4LjMuMV9fcmVhY3RAMTguMy4xL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvcmVuZGVyLWZyb20tdGVtcGxhdGUtY29udGV4dC5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL3VzZXIvRG9jdW1lbnRzL2F1Z21lbnQtcHJvamVjdHMvbmV4dGpzLXN1cGVyTm9kZS9ub2RlX21vZHVsZXMvLnBucG0vbmV4dEAxNS40LjJfcmVhY3QtZG9tQDE4LjMuMV9yZWFjdEAxOC4zLjFfX3JlYWN0QDE4LjMuMS9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2xpYi9tZXRhZGF0YS9nZW5lcmF0ZS9pY29uLW1hcmsuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Vc2Vycy91c2VyL0RvY3VtZW50cy9hdWdtZW50LXByb2plY3RzL25leHRqcy1zdXBlck5vZGUvbm9kZV9tb2R1bGVzLy5wbnBtL25leHRAMTUuNC4yX3JlYWN0LWRvbUAxOC4zLjFfcmVhY3RAMTguMy4xX19yZWFjdEAxOC4zLjEvbm9kZV9tb2R1bGVzL25leHQvZGlzdC9uZXh0LWRldnRvb2xzL3VzZXJzcGFjZS9hcHAvc2VnbWVudC1leHBsb3Jlci1ub2RlLmpzXCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fuser%2FDocuments%2Faugment-projects%2Fnextjs-superNode%2Fnode_modules%2F.pnpm%2Fnext%4015.4.2_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fbuiltin%2Fglobal-error.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fuser%2FDocuments%2Faugment-projects%2Fnextjs-superNode%2Fnode_modules%2F.pnpm%2Fnext%4015.4.2_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fuser%2FDocuments%2Faugment-projects%2Fnextjs-superNode%2Fnode_modules%2F.pnpm%2Fnext%4015.4.2_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fuser%2FDocuments%2Faugment-projects%2Fnextjs-superNode%2Fnode_modules%2F.pnpm%2Fnext%4015.4.2_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fuser%2FDocuments%2Faugment-projects%2Fnextjs-superNode%2Fnode_modules%2F.pnpm%2Fnext%4015.4.2_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fuser%2FDocuments%2Faugment-projects%2Fnextjs-superNode%2Fnode_modules%2F.pnpm%2Fnext%4015.4.2_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fuser%2FDocuments%2Faugment-projects%2Fnextjs-superNode%2Fnode_modules%2F.pnpm%2Fnext%4015.4.2_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fuser%2FDocuments%2Faugment-projects%2Fnextjs-superNode%2Fnode_modules%2F.pnpm%2Fnext%4015.4.2_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fuser%2FDocuments%2Faugment-projects%2Fnextjs-superNode%2Fnode_modules%2F.pnpm%2Fnext%4015.4.2_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%2Fnode_modules%2Fnext%2Fdist%2Flib%2Fmetadata%2Fgenerate%2Ficon-mark.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fuser%2FDocuments%2Faugment-projects%2Fnextjs-superNode%2Fnode_modules%2F.pnpm%2Fnext%4015.4.2_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%2Fnode_modules%2Fnext%2Fdist%2Fnext-devtools%2Fuserspace%2Fapp%2Fsegment-explorer-node.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fuser%2FDocuments%2Faugment-projects%2Fnextjs-superNode%2Fnode_modules%2F.pnpm%2Fnext%4015.4.2_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fuser%2FDocuments%2Faugment-projects%2Fnextjs-superNode%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fuser%2FDocuments%2Faugment-projects%2Fnextjs-superNode%2Fnode_modules%2F.pnpm%2Fnext%4015.4.2_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fuser%2FDocuments%2Faugment-projects%2Fnextjs-superNode%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fuser%2FDocuments%2Faugment-projects%2Fnextjs-superNode%2Fsrc%2Fcomponents%2FWorkflowCanvas.tsx%22%2C%22ids%22%3A%5B%22WorkflowCanvas%22%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fuser%2FDocuments%2Faugment-projects%2Fnextjs-superNode%2Fsrc%2Fcomponents%2FWorkflowCanvas.tsx%22%2C%22ids%22%3A%5B%22WorkflowCanvas%22%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/WorkflowCanvas.tsx */ \"(ssr)/./src/components/WorkflowCanvas.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vbmV4dEAxNS40LjJfcmVhY3QtZG9tQDE4LjMuMV9yZWFjdEAxOC4zLjFfX3JlYWN0QDE4LjMuMS9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGdXNlciUyRkRvY3VtZW50cyUyRmF1Z21lbnQtcHJvamVjdHMlMkZuZXh0anMtc3VwZXJOb2RlJTJGc3JjJTJGY29tcG9uZW50cyUyRldvcmtmbG93Q2FudmFzLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMldvcmtmbG93Q2FudmFzJTIyJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxrTEFBa0siLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcIldvcmtmbG93Q2FudmFzXCJdICovIFwiL1VzZXJzL3VzZXIvRG9jdW1lbnRzL2F1Z21lbnQtcHJvamVjdHMvbmV4dGpzLXN1cGVyTm9kZS9zcmMvY29tcG9uZW50cy9Xb3JrZmxvd0NhbnZhcy50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fuser%2FDocuments%2Faugment-projects%2Fnextjs-superNode%2Fsrc%2Fcomponents%2FWorkflowCanvas.tsx%22%2C%22ids%22%3A%5B%22WorkflowCanvas%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/components/PropertyPanel.tsx":
/*!******************************************!*\
  !*** ./src/components/PropertyPanel.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PropertyPanel: () => (/* binding */ PropertyPanel)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ PropertyPanel auto */ \n\nconst PropertyPanel = ({ node, onUpdateNode, onClose })=>{\n    if (!node) return null;\n    const handlePropertyChange = (key, value)=>{\n        onUpdateNode(node.id, {\n            properties: {\n                ...node.properties,\n                [key]: value\n            }\n        });\n    };\n    const handleNameChange = (name)=>{\n        onUpdateNode(node.id, {\n            name\n        });\n    };\n    const handleDescriptionChange = (description)=>{\n        onUpdateNode(node.id, {\n            description\n        });\n    };\n    const renderLedProperties = ()=>{\n        const properties = node.properties;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                            children: \"颜色模式\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/PropertyPanel.tsx\",\n                            lineNumber: 43,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                            value: properties.colorMode || '3color',\n                            onChange: (e)=>handlePropertyChange('colorMode', e.target.value),\n                            className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"1color\",\n                                    children: \"单色 (红色)\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/PropertyPanel.tsx\",\n                                    lineNumber: 51,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"2color\",\n                                    children: \"双色 (红绿)\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/PropertyPanel.tsx\",\n                                    lineNumber: 52,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"3color\",\n                                    children: \"三色 (RGB)\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/PropertyPanel.tsx\",\n                                    lineNumber: 53,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/PropertyPanel.tsx\",\n                            lineNumber: 46,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/PropertyPanel.tsx\",\n                    lineNumber: 42,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                            children: \"输入范围\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/PropertyPanel.tsx\",\n                            lineNumber: 58,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                            value: properties.inputRange || 'percentage',\n                            onChange: (e)=>handlePropertyChange('inputRange', e.target.value),\n                            className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"percentage\",\n                                    children: \"百分比 (0-100)\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/PropertyPanel.tsx\",\n                                    lineNumber: 66,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"0-255\",\n                                    children: \"0-255\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/PropertyPanel.tsx\",\n                                    lineNumber: 67,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"0-1\",\n                                    children: \"0-1\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/PropertyPanel.tsx\",\n                                    lineNumber: 68,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"boolean\",\n                                    children: \"布尔值\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/PropertyPanel.tsx\",\n                                    lineNumber: 69,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/PropertyPanel.tsx\",\n                            lineNumber: 61,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/PropertyPanel.tsx\",\n                    lineNumber: 57,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                            children: \"亮度 (%)\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/PropertyPanel.tsx\",\n                            lineNumber: 74,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"range\",\n                            min: \"0\",\n                            max: \"100\",\n                            value: properties.brightness || 100,\n                            onChange: (e)=>handlePropertyChange('brightness', parseInt(e.target.value)),\n                            className: \"w-full\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/PropertyPanel.tsx\",\n                            lineNumber: 77,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm text-gray-500 text-center\",\n                            children: [\n                                properties.brightness || 100,\n                                \"%\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/PropertyPanel.tsx\",\n                            lineNumber: 85,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/PropertyPanel.tsx\",\n                    lineNumber: 73,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"border-t pt-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                            className: \"text-sm font-medium text-gray-700 mb-2\",\n                            children: \"当前颜色值\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/PropertyPanel.tsx\",\n                            lineNumber: 91,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-3 gap-2 text-sm\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-red-600 font-medium\",\n                                        children: [\n                                            \"红: \",\n                                            properties.redValue || 0\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/PropertyPanel.tsx\",\n                                        lineNumber: 94,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/PropertyPanel.tsx\",\n                                    lineNumber: 93,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-green-600 font-medium\",\n                                        children: [\n                                            \"绿: \",\n                                            properties.greenValue || 0\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/PropertyPanel.tsx\",\n                                        lineNumber: 97,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/PropertyPanel.tsx\",\n                                    lineNumber: 96,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-blue-600 font-medium\",\n                                        children: [\n                                            \"蓝: \",\n                                            properties.blueValue || 0\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/PropertyPanel.tsx\",\n                                        lineNumber: 100,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/PropertyPanel.tsx\",\n                                    lineNumber: 99,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/PropertyPanel.tsx\",\n                            lineNumber: 92,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-2 text-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full h-8 rounded border\",\n                                style: {\n                                    backgroundColor: `rgb(${properties.redValue || 0}, ${properties.greenValue || 0}, ${properties.blueValue || 0})`\n                                }\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/PropertyPanel.tsx\",\n                                lineNumber: 104,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/PropertyPanel.tsx\",\n                            lineNumber: 103,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/PropertyPanel.tsx\",\n                    lineNumber: 90,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/PropertyPanel.tsx\",\n            lineNumber: 41,\n            columnNumber: 7\n        }, undefined);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed right-0 top-0 h-full w-80 bg-white shadow-lg border-l border-gray-200 z-50 overflow-y-auto\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"p-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between mb-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold text-gray-800\",\n                            children: \"节点属性\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/PropertyPanel.tsx\",\n                            lineNumber: 121,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onClose,\n                            className: \"text-gray-400 hover:text-gray-600 transition-colors\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"w-6 h-6\",\n                                fill: \"none\",\n                                stroke: \"currentColor\",\n                                viewBox: \"0 0 24 24\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 2,\n                                    d: \"M6 18L18 6M6 6l12 12\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/PropertyPanel.tsx\",\n                                    lineNumber: 127,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/PropertyPanel.tsx\",\n                                lineNumber: 126,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/PropertyPanel.tsx\",\n                            lineNumber: 122,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/PropertyPanel.tsx\",\n                    lineNumber: 120,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4 mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                    children: \"节点名称\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/PropertyPanel.tsx\",\n                                    lineNumber: 135,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    value: node.name,\n                                    onChange: (e)=>handleNameChange(e.target.value),\n                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/PropertyPanel.tsx\",\n                                    lineNumber: 138,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/PropertyPanel.tsx\",\n                            lineNumber: 134,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                    children: \"描述\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/PropertyPanel.tsx\",\n                                    lineNumber: 147,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                    value: node.description || '',\n                                    onChange: (e)=>handleDescriptionChange(e.target.value),\n                                    rows: 3,\n                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/PropertyPanel.tsx\",\n                                    lineNumber: 150,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/PropertyPanel.tsx\",\n                            lineNumber: 146,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                    children: \"节点类型\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/PropertyPanel.tsx\",\n                                    lineNumber: 159,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    value: node.type,\n                                    disabled: true,\n                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-gray-500\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/PropertyPanel.tsx\",\n                                    lineNumber: 162,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/PropertyPanel.tsx\",\n                            lineNumber: 158,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/PropertyPanel.tsx\",\n                    lineNumber: 133,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"border-t pt-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                            className: \"text-md font-medium text-gray-800 mb-4\",\n                            children: \"节点配置\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/PropertyPanel.tsx\",\n                            lineNumber: 173,\n                            columnNumber: 11\n                        }, undefined),\n                        node.type === 'led' && renderLedProperties()\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/PropertyPanel.tsx\",\n                    lineNumber: 172,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/PropertyPanel.tsx\",\n            lineNumber: 118,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/PropertyPanel.tsx\",\n        lineNumber: 117,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/PropertyPanel.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/WorkflowCanvas.tsx":
/*!*******************************************!*\
  !*** ./src/components/WorkflowCanvas.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   WorkflowCanvas: () => (/* binding */ WorkflowCanvas)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _nodes_LedNode__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/nodes/LedNode */ \"(ssr)/./src/nodes/LedNode.ts\");\n/* harmony import */ var _nodes_LedNodeComponent__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./nodes/LedNodeComponent */ \"(ssr)/./src/components/nodes/LedNodeComponent.tsx\");\n/* harmony import */ var _PropertyPanel__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./PropertyPanel */ \"(ssr)/./src/components/PropertyPanel.tsx\");\n/* __next_internal_client_entry_do_not_use__ WorkflowCanvas auto */ \n\n\n\n\nconst WorkflowCanvas = ()=>{\n    const [nodes, setNodes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedNode, setSelectedNode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showPropertyPanel, setShowPropertyPanel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const canvasRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const nodeInstancesRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(new Map());\n    // 创建新的LED节点\n    const createLedNode = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"WorkflowCanvas.useCallback[createLedNode]\": (position)=>{\n            const ledNode = new _nodes_LedNode__WEBPACK_IMPORTED_MODULE_2__.LedNode({\n                position\n            });\n            const nodeData = ledNode.getData();\n            setNodes({\n                \"WorkflowCanvas.useCallback[createLedNode]\": (prev)=>[\n                        ...prev,\n                        nodeData\n                    ]\n            }[\"WorkflowCanvas.useCallback[createLedNode]\"]);\n            nodeInstancesRef.current.set(nodeData.id, ledNode);\n            return nodeData;\n        }\n    }[\"WorkflowCanvas.useCallback[createLedNode]\"], []);\n    // 更新节点数据\n    const updateNode = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"WorkflowCanvas.useCallback[updateNode]\": (nodeId, updates)=>{\n            // 使用函数式更新减少重新渲染\n            setNodes({\n                \"WorkflowCanvas.useCallback[updateNode]\": (prev)=>{\n                    const nodeIndex = prev.findIndex({\n                        \"WorkflowCanvas.useCallback[updateNode].nodeIndex\": (node)=>node.id === nodeId\n                    }[\"WorkflowCanvas.useCallback[updateNode].nodeIndex\"]);\n                    if (nodeIndex === -1) return prev;\n                    const currentNode = prev[nodeIndex];\n                    const updatedNode = {\n                        ...currentNode,\n                        ...updates\n                    };\n                    // 检查是否真的有变化，避免不必要的更新\n                    if (JSON.stringify(currentNode) === JSON.stringify(updatedNode)) {\n                        return prev;\n                    }\n                    // 更新节点实例\n                    const nodeInstance = nodeInstancesRef.current.get(nodeId);\n                    if (nodeInstance) {\n                        nodeInstance.updateData(updatedNode);\n                        // 如果属性发生变化，触发处理\n                        if (updates.properties) {\n                            Object.entries(updates.properties).forEach({\n                                \"WorkflowCanvas.useCallback[updateNode]\": ([key, value])=>{\n                                    nodeInstance.setProperty(key, value);\n                                }\n                            }[\"WorkflowCanvas.useCallback[updateNode]\"]);\n                        }\n                    }\n                    // 创建新数组，只更新变化的节点\n                    const newNodes = [\n                        ...prev\n                    ];\n                    newNodes[nodeIndex] = updatedNode;\n                    return newNodes;\n                }\n            }[\"WorkflowCanvas.useCallback[updateNode]\"]);\n            // 如果更新的是当前选中的节点，也更新选中状态\n            if (selectedNode?.id === nodeId) {\n                setSelectedNode({\n                    \"WorkflowCanvas.useCallback[updateNode]\": (prev)=>prev ? {\n                            ...prev,\n                            ...updates\n                        } : null\n                }[\"WorkflowCanvas.useCallback[updateNode]\"]);\n            }\n        }\n    }[\"WorkflowCanvas.useCallback[updateNode]\"], [\n        selectedNode\n    ]);\n    // 处理节点事件\n    const handleNodeEvent = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"WorkflowCanvas.useCallback[handleNodeEvent]\": (event)=>{\n            const nodeInstance = nodeInstancesRef.current.get(event.nodeId);\n            if (nodeInstance) {\n                nodeInstance.handleEvent(event);\n            }\n            switch(event.type){\n                case 'click':\n                    const clickedNode = nodes.find({\n                        \"WorkflowCanvas.useCallback[handleNodeEvent].clickedNode\": (n)=>n.id === event.nodeId\n                    }[\"WorkflowCanvas.useCallback[handleNodeEvent].clickedNode\"]);\n                    if (clickedNode) {\n                        // 取消其他节点的选中状态\n                        setNodes({\n                            \"WorkflowCanvas.useCallback[handleNodeEvent]\": (prev)=>prev.map({\n                                    \"WorkflowCanvas.useCallback[handleNodeEvent]\": (node)=>({\n                                            ...node,\n                                            selected: node.id === event.nodeId\n                                        })\n                                }[\"WorkflowCanvas.useCallback[handleNodeEvent]\"])\n                        }[\"WorkflowCanvas.useCallback[handleNodeEvent]\"]);\n                        setSelectedNode(clickedNode);\n                    }\n                    break;\n                case 'doubleClick':\n                    const doubleClickedNode = nodes.find({\n                        \"WorkflowCanvas.useCallback[handleNodeEvent].doubleClickedNode\": (n)=>n.id === event.nodeId\n                    }[\"WorkflowCanvas.useCallback[handleNodeEvent].doubleClickedNode\"]);\n                    if (doubleClickedNode) {\n                        setSelectedNode(doubleClickedNode);\n                        setShowPropertyPanel(true);\n                    }\n                    break;\n            }\n        }\n    }[\"WorkflowCanvas.useCallback[handleNodeEvent]\"], [\n        nodes\n    ]);\n    // 处理画布点击\n    const handleCanvasClick = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"WorkflowCanvas.useCallback[handleCanvasClick]\": (e)=>{\n            if (e.target === canvasRef.current) {\n                // 取消所有节点的选中状态\n                setNodes({\n                    \"WorkflowCanvas.useCallback[handleCanvasClick]\": (prev)=>prev.map({\n                            \"WorkflowCanvas.useCallback[handleCanvasClick]\": (node)=>({\n                                    ...node,\n                                    selected: false\n                                })\n                        }[\"WorkflowCanvas.useCallback[handleCanvasClick]\"])\n                }[\"WorkflowCanvas.useCallback[handleCanvasClick]\"]);\n                setSelectedNode(null);\n            }\n        }\n    }[\"WorkflowCanvas.useCallback[handleCanvasClick]\"], []);\n    // 处理画布双击 - 创建新节点\n    const handleCanvasDoubleClick = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"WorkflowCanvas.useCallback[handleCanvasDoubleClick]\": (e)=>{\n            if (e.target === canvasRef.current) {\n                const rect = canvasRef.current.getBoundingClientRect();\n                const position = {\n                    x: e.clientX - rect.left - 60,\n                    y: e.clientY - rect.top - 40\n                };\n                createLedNode(position);\n            }\n        }\n    }[\"WorkflowCanvas.useCallback[handleCanvasDoubleClick]\"], [\n        createLedNode\n    ]);\n    // 渲染节点\n    const renderNode = (node)=>{\n        switch(node.type){\n            case 'led':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_nodes_LedNodeComponent__WEBPACK_IMPORTED_MODULE_3__.LedNodeComponent, {\n                    node: node,\n                    onEvent: handleNodeEvent,\n                    onUpdateNode: updateNode\n                }, node.id, false, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/WorkflowCanvas.tsx\",\n                    lineNumber: 125,\n                    columnNumber: 11\n                }, undefined);\n            default:\n                return null;\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative w-full h-screen bg-gray-50 overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-4 left-4 z-10 bg-white rounded-lg shadow-md p-2 flex space-x-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>createLedNode({\n                                x: 100,\n                                y: 100\n                            }),\n                        className: \"px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors\",\n                        children: \"添加LED节点\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/WorkflowCanvas.tsx\",\n                        lineNumber: 141,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>{\n                            setNodes([]);\n                            nodeInstancesRef.current.clear();\n                            setSelectedNode(null);\n                            setShowPropertyPanel(false);\n                        },\n                        className: \"px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600 transition-colors\",\n                        children: \"清空画布\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/WorkflowCanvas.tsx\",\n                        lineNumber: 147,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/WorkflowCanvas.tsx\",\n                lineNumber: 140,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-4 right-4 z-10 bg-white rounded-lg shadow-md p-2\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-sm text-gray-600\",\n                    children: [\n                        \"节点数量: \",\n                        nodes.length,\n                        selectedNode && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-1\",\n                            children: [\n                                \"选中: \",\n                                selectedNode.name\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/WorkflowCanvas.tsx\",\n                            lineNumber: 165,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/WorkflowCanvas.tsx\",\n                    lineNumber: 162,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/WorkflowCanvas.tsx\",\n                lineNumber: 161,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: canvasRef,\n                className: \"w-full h-full relative cursor-crosshair\",\n                onClick: handleCanvasClick,\n                onDoubleClick: handleCanvasDoubleClick,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 opacity-20\",\n                        style: {\n                            backgroundImage: `\n              linear-gradient(to right, #e5e7eb 1px, transparent 1px),\n              linear-gradient(to bottom, #e5e7eb 1px, transparent 1px)\n            `,\n                            backgroundSize: '20px 20px'\n                        }\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/WorkflowCanvas.tsx\",\n                        lineNumber: 180,\n                        columnNumber: 9\n                    }, undefined),\n                    nodes.map(renderNode),\n                    nodes.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 flex items-center justify-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-gray-400 text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-lg mb-2\",\n                                    children: \"工作流画布\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/WorkflowCanvas.tsx\",\n                                    lineNumber: 198,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm\",\n                                    children: \"双击画布创建LED节点，或点击工具栏按钮\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/WorkflowCanvas.tsx\",\n                                    lineNumber: 199,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/WorkflowCanvas.tsx\",\n                            lineNumber: 197,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/WorkflowCanvas.tsx\",\n                        lineNumber: 196,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/WorkflowCanvas.tsx\",\n                lineNumber: 173,\n                columnNumber: 7\n            }, undefined),\n            showPropertyPanel && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_PropertyPanel__WEBPACK_IMPORTED_MODULE_4__.PropertyPanel, {\n                node: selectedNode,\n                onUpdateNode: updateNode,\n                onClose: ()=>setShowPropertyPanel(false)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/WorkflowCanvas.tsx\",\n                lineNumber: 207,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/WorkflowCanvas.tsx\",\n        lineNumber: 138,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/WorkflowCanvas.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/nodes/BaseNodeComponent.tsx":
/*!****************************************************!*\
  !*** ./src/components/nodes/BaseNodeComponent.tsx ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BaseNodeComponent: () => (/* binding */ BaseNodeComponent)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ BaseNodeComponent auto */ \n\nconst BaseNodeComponent = ({ node, onEvent, onUpdateNode, children })=>{\n    const [isDragging, setIsDragging] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [dragOffset, setDragOffset] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        x: 0,\n        y: 0\n    });\n    const nodeRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const animationFrameRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const pendingUpdateRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // 计算引脚位置\n    const calculatePinPositions = ()=>{\n        const inputPins = node.pins.filter((pin)=>pin.type === 'input');\n        const outputPins = node.pins.filter((pin)=>pin.type === 'output');\n        const maxPins = Math.max(inputPins.length, outputPins.length);\n        // 标题栏高度：padding(16px) + 文字高度(20px) + border(1px) = 37px\n        const titleHeight = 37;\n        // 底部留白\n        const bottomMargin = 12;\n        // 引脚区域顶部留白\n        const topMargin = 12;\n        // 可用于引脚的高度\n        const availableHeight = node.size.height - titleHeight - bottomMargin - topMargin;\n        // 引脚间距，如果只有一个引脚则居中，多个引脚则均匀分布\n        let pinSpacing = 0;\n        if (maxPins > 1) {\n            pinSpacing = availableHeight / (maxPins - 1);\n        }\n        return {\n            inputPins,\n            outputPins,\n            titleHeight,\n            topMargin,\n            pinSpacing,\n            availableHeight,\n            maxPins\n        };\n    };\n    const { inputPins, outputPins, titleHeight, topMargin, pinSpacing, maxPins } = calculatePinPositions();\n    // 使用requestAnimationFrame节流更新\n    const flushPendingUpdate = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"BaseNodeComponent.useCallback[flushPendingUpdate]\": ()=>{\n            if (pendingUpdateRef.current) {\n                onUpdateNode(node.id, {\n                    position: pendingUpdateRef.current\n                });\n                pendingUpdateRef.current = null;\n            }\n            animationFrameRef.current = null;\n        }\n    }[\"BaseNodeComponent.useCallback[flushPendingUpdate]\"], [\n        onUpdateNode,\n        node.id\n    ]);\n    const handleMouseMove = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"BaseNodeComponent.useCallback[handleMouseMove]\": (e)=>{\n            // 确保拖拽偏移量已经设置\n            if (dragOffset.x === 0 && dragOffset.y === 0) return;\n            const newPosition = {\n                x: e.clientX - dragOffset.x,\n                y: e.clientY - dragOffset.y\n            };\n            // 立即更新DOM位置以获得流畅的视觉效果\n            if (nodeRef.current) {\n                nodeRef.current.style.left = `${newPosition.x}px`;\n                nodeRef.current.style.top = `${newPosition.y}px`;\n            }\n            // 节流状态更新\n            pendingUpdateRef.current = newPosition;\n            if (animationFrameRef.current === null) {\n                animationFrameRef.current = requestAnimationFrame(flushPendingUpdate);\n            }\n        }\n    }[\"BaseNodeComponent.useCallback[handleMouseMove]\"], [\n        dragOffset.x,\n        dragOffset.y,\n        flushPendingUpdate\n    ]);\n    const handleMouseUp = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"BaseNodeComponent.useCallback[handleMouseUp]\": ()=>{\n            setIsDragging(false);\n            // 清理动画帧\n            if (animationFrameRef.current) {\n                cancelAnimationFrame(animationFrameRef.current);\n                animationFrameRef.current = null;\n            }\n            // 确保最后的位置更新被应用\n            if (pendingUpdateRef.current) {\n                onUpdateNode(node.id, {\n                    position: pendingUpdateRef.current\n                });\n                pendingUpdateRef.current = null;\n            }\n            onEvent({\n                type: 'dragEnd',\n                nodeId: node.id\n            });\n            // 移除全局鼠标事件监听\n            document.removeEventListener('mousemove', handleMouseMove);\n            document.removeEventListener('mouseup', handleMouseUp);\n        }\n    }[\"BaseNodeComponent.useCallback[handleMouseUp]\"], [\n        handleMouseMove,\n        onEvent,\n        onUpdateNode,\n        node.id\n    ]);\n    const handleMouseDown = (e)=>{\n        if (e.button !== 0) return; // 只处理左键\n        // 防止在引脚上开始拖拽\n        const target = e.target;\n        if (target.closest('.pin-connector')) {\n            return;\n        }\n        e.preventDefault();\n        e.stopPropagation();\n        // 使用节点的当前位置而不是getBoundingClientRect来计算偏移量\n        // 这样可以避免因为CSS变换或其他因素导致的位置偏差\n        setDragOffset({\n            x: e.clientX - node.position.x,\n            y: e.clientY - node.position.y\n        });\n        setIsDragging(true);\n        onEvent({\n            type: 'dragStart',\n            nodeId: node.id,\n            data: {\n                position: node.position\n            }\n        });\n        // 添加全局鼠标事件监听\n        document.addEventListener('mousemove', handleMouseMove);\n        document.addEventListener('mouseup', handleMouseUp);\n    };\n    const handleClick = (e)=>{\n        e.stopPropagation();\n        onEvent({\n            type: 'click',\n            nodeId: node.id\n        });\n    };\n    const handleDoubleClick = (e)=>{\n        e.stopPropagation();\n        onEvent({\n            type: 'doubleClick',\n            nodeId: node.id\n        });\n    };\n    const getStatusColor = ()=>{\n        switch(node.status){\n            case 'running':\n                return 'border-yellow-400 bg-yellow-50';\n            case 'success':\n                return 'border-green-400 bg-green-50';\n            case 'error':\n                return 'border-red-400 bg-red-50';\n            case 'warning':\n                return 'border-orange-400 bg-orange-50';\n            default:\n                return 'border-gray-300 bg-white';\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: nodeRef,\n        className: `absolute border-2 rounded-lg shadow-md cursor-move select-none ${getStatusColor()} ${node.selected ? 'ring-2 ring-blue-400' : ''} ${isDragging ? 'shadow-lg' : ''}`,\n        style: {\n            left: node.position.x,\n            top: node.position.y,\n            width: node.size.width,\n            height: node.size.height\n        },\n        onMouseDown: handleMouseDown,\n        onClick: handleClick,\n        onDoubleClick: handleDoubleClick,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-2 py-2 bg-gray-100 border-b border-gray-200 rounded-t-md\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-sm font-medium text-gray-800 truncate\",\n                    children: node.name\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/nodes/BaseNodeComponent.tsx\",\n                    lineNumber: 196,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/nodes/BaseNodeComponent.tsx\",\n                lineNumber: 195,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-2 flex-1\",\n                children: children\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/nodes/BaseNodeComponent.tsx\",\n                lineNumber: 202,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute left-0\",\n                style: {\n                    top: titleHeight + topMargin\n                },\n                children: inputPins.map((pin, index)=>{\n                    // 如果只有一个引脚，居中显示；多个引脚则均匀分布\n                    const pinY = maxPins === 1 ? 0 : index * pinSpacing;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute flex items-center\",\n                        style: {\n                            top: pinY,\n                            left: -6,\n                            transform: 'translateY(-50%)' // 垂直居中对齐\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: `pin-connector w-3 h-3 rounded-full border-2 cursor-pointer transition-colors ${pin.connected ? 'bg-blue-500 border-blue-600' : 'bg-white border-gray-400 hover:border-blue-400'}`,\n                                title: pin.name\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/nodes/BaseNodeComponent.tsx\",\n                                lineNumber: 221,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"absolute left-4 top-1/2 transform -translate-y-1/2 text-xs text-gray-600 whitespace-nowrap\",\n                                children: pin.name\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/nodes/BaseNodeComponent.tsx\",\n                                lineNumber: 229,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, pin.id, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/nodes/BaseNodeComponent.tsx\",\n                        lineNumber: 212,\n                        columnNumber: 13\n                    }, undefined);\n                })\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/nodes/BaseNodeComponent.tsx\",\n                lineNumber: 207,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute right-0\",\n                style: {\n                    top: titleHeight + topMargin\n                },\n                children: outputPins.map((pin, index)=>{\n                    // 如果只有一个引脚，居中显示；多个引脚则均匀分布\n                    const pinY = maxPins === 1 ? 0 : index * pinSpacing;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute flex items-center justify-end\",\n                        style: {\n                            top: pinY,\n                            right: -6,\n                            transform: 'translateY(-50%)' // 垂直居中对齐\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"absolute right-4 top-1/2 transform -translate-y-1/2 text-xs text-gray-600 whitespace-nowrap\",\n                                children: pin.name\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/nodes/BaseNodeComponent.tsx\",\n                                lineNumber: 252,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: `pin-connector w-3 h-3 rounded-full border-2 cursor-pointer transition-colors ${pin.connected ? 'bg-green-500 border-green-600' : 'bg-white border-gray-400 hover:border-green-400'}`,\n                                title: pin.name\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/nodes/BaseNodeComponent.tsx\",\n                                lineNumber: 255,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, pin.id, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/nodes/BaseNodeComponent.tsx\",\n                        lineNumber: 243,\n                        columnNumber: 13\n                    }, undefined);\n                })\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/nodes/BaseNodeComponent.tsx\",\n                lineNumber: 238,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/nodes/BaseNodeComponent.tsx\",\n        lineNumber: 177,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/nodes/BaseNodeComponent.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/nodes/LedNodeComponent.tsx":
/*!***************************************************!*\
  !*** ./src/components/nodes/LedNodeComponent.tsx ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LedNodeComponent: () => (/* binding */ LedNodeComponent)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _BaseNodeComponent__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./BaseNodeComponent */ \"(ssr)/./src/components/nodes/BaseNodeComponent.tsx\");\n/* __next_internal_client_entry_do_not_use__ LedNodeComponent auto */ \n\n\nconst LedNodeComponent = ({ node, onEvent, onUpdateNode })=>{\n    const properties = node.properties;\n    const redValue = properties.redValue || 0;\n    const greenValue = properties.greenValue || 0;\n    const blueValue = properties.blueValue || 0;\n    // 生成LED显示颜色\n    const ledColor = `rgb(${redValue}, ${greenValue}, ${blueValue})`;\n    const isOn = redValue > 0 || greenValue > 0 || blueValue > 0;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_BaseNodeComponent__WEBPACK_IMPORTED_MODULE_2__.BaseNodeComponent, {\n        node: node,\n        onEvent: onEvent,\n        onUpdateNode: onUpdateNode,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center h-full\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: `w-8 h-8 rounded-full border-2 transition-all duration-300 ${isOn ? 'border-gray-400 shadow-lg' : 'border-gray-300'}`,\n                            style: {\n                                backgroundColor: isOn ? ledColor : '#f3f4f6',\n                                boxShadow: isOn ? `0 0 10px ${ledColor}, 0 0 20px ${ledColor}40` : 'none'\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-4 h-4 rounded-full absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2\",\n                                style: {\n                                    backgroundColor: isOn ? `rgba(${redValue}, ${greenValue}, ${blueValue}, 0.8)` : '#e5e7eb'\n                                }\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/nodes/LedNodeComponent.tsx\",\n                                lineNumber: 46,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/nodes/LedNodeComponent.tsx\",\n                            lineNumber: 32,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute -bottom-1 -right-1\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: `w-2 h-2 rounded-full ${isOn ? 'bg-green-400' : 'bg-gray-300'}`\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/nodes/LedNodeComponent.tsx\",\n                                lineNumber: 58,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/nodes/LedNodeComponent.tsx\",\n                            lineNumber: 57,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/nodes/LedNodeComponent.tsx\",\n                    lineNumber: 30,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/nodes/LedNodeComponent.tsx\",\n                lineNumber: 28,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute bottom-1 left-1 right-1\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-xs text-center text-gray-600 bg-white bg-opacity-75 rounded px-1\",\n                    children: [\n                        properties.colorMode === '1color' && `R:${redValue}`,\n                        properties.colorMode === '2color' && `R:${redValue} G:${greenValue}`,\n                        properties.colorMode === '3color' && `R:${redValue} G:${greenValue} B:${blueValue}`\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/nodes/LedNodeComponent.tsx\",\n                    lineNumber: 69,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/nodes/LedNodeComponent.tsx\",\n                lineNumber: 68,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/nodes/LedNodeComponent.tsx\",\n        lineNumber: 23,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/nodes/LedNodeComponent.tsx\n");

/***/ }),

/***/ "(ssr)/./src/nodes/BaseNode.ts":
/*!*******************************!*\
  !*** ./src/nodes/BaseNode.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BaseNode: () => (/* binding */ BaseNode)\n/* harmony export */ });\nclass BaseNode {\n    constructor(data){\n        this.data = {\n            id: data.id || this.generateId(),\n            type: data.type || 'base',\n            name: data.name || 'Base Node',\n            description: data.description || '',\n            position: data.position || {\n                x: 0,\n                y: 0\n            },\n            size: data.size || {\n                width: 200,\n                height: 100\n            },\n            status: data.status || 'idle',\n            pins: data.pins || [],\n            properties: data.properties || {},\n            selected: data.selected || false,\n            dragging: data.dragging || false\n        };\n    }\n    // 生成唯一ID\n    generateId() {\n        return `node_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n    }\n    // 获取节点数据\n    getData() {\n        return {\n            ...this.data\n        };\n    }\n    // 更新节点数据\n    updateData(updates) {\n        this.data = {\n            ...this.data,\n            ...updates\n        };\n    }\n    // 设置节点状态\n    setStatus(status) {\n        this.data.status = status;\n    }\n    // 添加引脚\n    addPin(pin) {\n        this.data.pins.push(pin);\n    }\n    // 移除引脚\n    removePin(pinId) {\n        this.data.pins = this.data.pins.filter((pin)=>pin.id !== pinId);\n    }\n    // 获取引脚\n    getPin(pinId) {\n        return this.data.pins.find((pin)=>pin.id === pinId);\n    }\n    // 设置引脚值\n    setPinValue(pinId, value) {\n        const pin = this.getPin(pinId);\n        if (pin) {\n            pin.value = value;\n            this.onPinValueChanged(pinId, value);\n        }\n    }\n    // 获取引脚值\n    getPinValue(pinId) {\n        const pin = this.getPin(pinId);\n        return pin?.value;\n    }\n    // 设置属性\n    setProperty(key, value) {\n        this.data.properties[key] = value;\n        this.onPropertyChanged(key, value);\n    }\n    // 获取属性\n    getProperty(key) {\n        return this.data.properties[key];\n    }\n    // 处理事件\n    handleEvent(event) {\n        switch(event.type){\n            case 'click':\n                this.onClick(event);\n                break;\n            case 'doubleClick':\n                this.onDoubleClick(event);\n                break;\n            case 'dragStart':\n                this.onDragStart(event);\n                break;\n            case 'drag':\n                this.onDrag(event);\n                break;\n            case 'dragEnd':\n                this.onDragEnd(event);\n                break;\n            case 'pinConnect':\n                this.onPinConnect(event);\n                break;\n            case 'pinDisconnect':\n                this.onPinDisconnect(event);\n                break;\n            case 'propertyChange':\n                this.onPropertyChange(event);\n                break;\n        }\n    }\n    // 事件处理方法 - 子类可以重写\n    onClick(event) {}\n    onDoubleClick(event) {}\n    onDragStart(event) {\n        this.data.dragging = true;\n    }\n    onDrag(event) {\n        if (event.data && this.data.dragging) {\n            this.data.position = event.data.position;\n        }\n    }\n    onDragEnd(event) {\n        this.data.dragging = false;\n    }\n    onPinConnect(event) {}\n    onPinDisconnect(event) {}\n    onPropertyChange(event) {}\n    onPinValueChanged(pinId, value) {\n        // 当引脚值改变时触发处理\n        this.process();\n    }\n    onPropertyChanged(key, value) {\n        // 当属性改变时可能需要重新配置引脚\n        this.updatePinsConfiguration();\n    }\n    // 更新引脚配置 - 子类可以重写\n    updatePinsConfiguration() {\n        // 更新引脚配置后，重新计算节点高度\n        this.updateNodeSize();\n    }\n    // 根据引脚数量计算并更新节点尺寸\n    updateNodeSize() {\n        const inputPins = this.data.pins.filter((pin)=>pin.type === 'input');\n        const outputPins = this.data.pins.filter((pin)=>pin.type === 'output');\n        const maxPins = Math.max(inputPins.length, outputPins.length);\n        // 标题栏高度：37px\n        const titleHeight = 37;\n        // 内容区域最小高度\n        const minContentHeight = 80;\n        // 引脚区域上下边距\n        const pinMargins = 24; // 上边距12px + 下边距12px\n        // 计算引脚所需的高度\n        let pinsHeight = minContentHeight;\n        if (maxPins > 0) {\n            // 每个引脚需要的最小间距\n            const minPinSpacing = 24;\n            // 确保有足够的空间容纳所有引脚，至少保持最小内容高度\n            const requiredPinsHeight = (maxPins - 1) * minPinSpacing + 30; // 额外30px用于首尾边距\n            pinsHeight = Math.max(minContentHeight, requiredPinsHeight);\n        }\n        const newHeight = titleHeight + pinMargins + pinsHeight;\n        // 只有当高度发生变化时才更新\n        if (this.data.size.height !== newHeight) {\n            this.data.size.height = newHeight;\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/nodes/BaseNode.ts\n");

/***/ }),

/***/ "(ssr)/./src/nodes/LedNode.ts":
/*!******************************!*\
  !*** ./src/nodes/LedNode.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LedNode: () => (/* binding */ LedNode)\n/* harmony export */ });\n/* harmony import */ var _BaseNode__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./BaseNode */ \"(ssr)/./src/nodes/BaseNode.ts\");\n\nclass LedNode extends _BaseNode__WEBPACK_IMPORTED_MODULE_0__.BaseNode {\n    constructor(data){\n        super({\n            type: 'led',\n            name: 'RGB LED',\n            description: '3色变色LED灯节点',\n            size: {\n                width: 140,\n                height: 100\n            },\n            properties: {\n                colorMode: '3color',\n                inputRange: 'percentage',\n                redValue: 0,\n                greenValue: 0,\n                blueValue: 0,\n                brightness: 100\n            },\n            ...data\n        });\n        this.initializePins();\n    }\n    initializePins() {\n        this.updatePinsConfiguration();\n    }\n    updatePinsConfiguration() {\n        // 清除现有引脚\n        this.data.pins = [];\n        const colorMode = this.getProperty('colorMode');\n        const inputRange = this.getProperty('inputRange');\n        // 根据颜色模式添加引脚\n        if (colorMode === '1color' || colorMode === '2color' || colorMode === '3color') {\n            this.addPin({\n                id: 'red_input',\n                name: 'R',\n                type: 'input',\n                dataType: inputRange === 'boolean' ? 'boolean' : 'number',\n                value: 0\n            });\n        }\n        if (colorMode === '2color' || colorMode === '3color') {\n            this.addPin({\n                id: 'green_input',\n                name: 'G',\n                type: 'input',\n                dataType: inputRange === 'boolean' ? 'boolean' : 'number',\n                value: 0\n            });\n        }\n        if (colorMode === '3color') {\n            this.addPin({\n                id: 'blue_input',\n                name: 'B',\n                type: 'input',\n                dataType: inputRange === 'boolean' ? 'boolean' : 'number',\n                value: 0\n            });\n        }\n        // 添加亮度控制引脚\n        this.addPin({\n            id: 'brightness_input',\n            name: 'Brightness',\n            type: 'input',\n            dataType: 'number',\n            value: 100\n        });\n        // 添加输出引脚\n        this.addPin({\n            id: 'color_output',\n            name: 'Color',\n            type: 'output',\n            dataType: 'color',\n            value: '#000000'\n        });\n    }\n    async process() {\n        try {\n            this.setStatus('running');\n            const colorMode = this.getProperty('colorMode');\n            const inputRange = this.getProperty('inputRange');\n            const brightness = this.getPinValue('brightness_input') || this.getProperty('brightness') || 100;\n            let red = 0, green = 0, blue = 0;\n            // 获取输入值\n            if (colorMode === '1color' || colorMode === '2color' || colorMode === '3color') {\n                red = this.normalizeInputValue(this.getPinValue('red_input') || 0, inputRange);\n            }\n            if (colorMode === '2color' || colorMode === '3color') {\n                green = this.normalizeInputValue(this.getPinValue('green_input') || 0, inputRange);\n            }\n            if (colorMode === '3color') {\n                blue = this.normalizeInputValue(this.getPinValue('blue_input') || 0, inputRange);\n            }\n            // 应用亮度\n            const brightnessMultiplier = Math.max(0, Math.min(100, brightness)) / 100;\n            red = Math.round(red * brightnessMultiplier);\n            green = Math.round(green * brightnessMultiplier);\n            blue = Math.round(blue * brightnessMultiplier);\n            // 更新属性值\n            this.setProperty('redValue', red);\n            this.setProperty('greenValue', green);\n            this.setProperty('blueValue', blue);\n            // 生成颜色输出\n            const colorHex = `#${red.toString(16).padStart(2, '0')}${green.toString(16).padStart(2, '0')}${blue.toString(16).padStart(2, '0')}`;\n            this.setPinValue('color_output', colorHex);\n            this.setStatus('success');\n        } catch (error) {\n            console.error('LED Node processing error:', error);\n            this.setStatus('error');\n        }\n    }\n    normalizeInputValue(value, inputRange) {\n        if (typeof value === 'boolean') {\n            return value ? 255 : 0;\n        }\n        const numValue = Number(value) || 0;\n        switch(inputRange){\n            case 'percentage':\n                return Math.round(Math.max(0, Math.min(100, numValue)) * 2.55);\n            case '0-255':\n                return Math.round(Math.max(0, Math.min(255, numValue)));\n            case '0-1':\n                return Math.round(Math.max(0, Math.min(1, numValue)) * 255);\n            case 'boolean':\n                return numValue > 0 ? 255 : 0;\n            default:\n                return Math.round(Math.max(0, Math.min(255, numValue)));\n        }\n    }\n    onPropertyChanged(key, value) {\n        if (key === 'colorMode' || key === 'inputRange') {\n            this.updatePinsConfiguration();\n        }\n        super.onPropertyChanged(key, value);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/nodes/LedNode.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/dynamic-access-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/server/app-render/dynamic-access-async-storage.external.js" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/dynamic-access-async-storage.external.js");

/***/ }),

/***/ "./work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "module":
/*!*************************!*\
  !*** external "module" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("module");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/shared/lib/no-fallback-error.external":
/*!******************************************************************!*\
  !*** external "next/dist/shared/lib/no-fallback-error.external" ***!
  \******************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/no-fallback-error.external");

/***/ }),

/***/ "next/dist/shared/lib/router/utils/app-paths":
/*!**************************************************************!*\
  !*** external "next/dist/shared/lib/router/utils/app-paths" ***!
  \**************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/router/utils/app-paths");

/***/ }),

/***/ "next/dist/shared/lib/router/utils/is-bot":
/*!***********************************************************!*\
  !*** external "next/dist/shared/lib/router/utils/is-bot" ***!
  \***********************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/router/utils/is-bot");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1","vendor-chunks/@swc+helpers@0.5.15"], () => (__webpack_exec__("(rsc)/./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Fuser%2FDocuments%2Faugment-projects%2Fnextjs-superNode%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fuser%2FDocuments%2Faugment-projects%2Fnextjs-superNode&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!")));
module.exports = __webpack_exports__;

})();