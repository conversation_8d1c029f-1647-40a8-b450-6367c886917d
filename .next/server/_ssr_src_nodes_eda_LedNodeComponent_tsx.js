"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_ssr_src_nodes_eda_LedNodeComponent_tsx";
exports.ids = ["_ssr_src_nodes_eda_LedNodeComponent_tsx"];
exports.modules = {

/***/ "(ssr)/./src/base/BaseNodeComponent.tsx":
/*!****************************************!*\
  !*** ./src/base/BaseNodeComponent.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BaseNodeComponent: () => (/* binding */ BaseNodeComponent)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ BaseNodeComponent auto */ \n\nconst BaseNodeComponent = ({ node, onEvent, onUpdateNode, children })=>{\n    const [isDragging, setIsDragging] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const nodeRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const animationFrameRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const pendingUpdateRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // 使用ref来存储拖拽起始信息，避免不必要的重渲染\n    const dragInfoRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // 计算引脚位置\n    const calculatePinPositions = ()=>{\n        const inputPins = node.pins.filter((pin)=>pin.type === 'input');\n        const outputPins = node.pins.filter((pin)=>pin.type === 'output');\n        const maxPins = Math.max(inputPins.length, outputPins.length);\n        // 标题栏高度\n        const titleHeight = 37;\n        // 引脚区域的起始Y坐标（相对于内容区）\n        const pinStartY = 14; // (pinSlotHeight / 2)\n        // 每个引脚的固定垂直间距\n        const pinSpacing = 28;\n        return {\n            inputPins,\n            outputPins,\n            maxPins,\n            titleHeight,\n            pinStartY,\n            pinSpacing\n        };\n    };\n    const { inputPins, outputPins, maxPins, titleHeight, pinStartY, pinSpacing } = calculatePinPositions();\n    // 使用requestAnimationFrame节流更新\n    const flushPendingUpdate = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"BaseNodeComponent.useCallback[flushPendingUpdate]\": ()=>{\n            if (pendingUpdateRef.current) {\n                onUpdateNode(node.id, {\n                    position: pendingUpdateRef.current\n                });\n                pendingUpdateRef.current = null;\n            }\n            animationFrameRef.current = null;\n        }\n    }[\"BaseNodeComponent.useCallback[flushPendingUpdate]\"], [\n        onUpdateNode,\n        node.id\n    ]);\n    const handleMouseMove = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"BaseNodeComponent.useCallback[handleMouseMove]\": (e)=>{\n            // 拖拽逻辑不应依赖isDragging state，而是依赖ref，以避免陈旧闭包问题\n            if (!dragInfoRef.current) return;\n            const deltaX = e.clientX - dragInfoRef.current.startX;\n            const deltaY = e.clientY - dragInfoRef.current.startY;\n            const newPosition = {\n                x: dragInfoRef.current.startNodeX + deltaX,\n                y: dragInfoRef.current.startNodeY + deltaY\n            };\n            // 恢复直接DOM操作以实现流畅视觉效果\n            if (nodeRef.current) {\n                nodeRef.current.style.left = `${newPosition.x}px`;\n                nodeRef.current.style.top = `${newPosition.y}px`;\n            }\n            // 节流状态更新\n            pendingUpdateRef.current = newPosition;\n            if (animationFrameRef.current === null) {\n                animationFrameRef.current = requestAnimationFrame(flushPendingUpdate);\n            }\n        }\n    }[\"BaseNodeComponent.useCallback[handleMouseMove]\"], [\n        flushPendingUpdate\n    ]);\n    const handleMouseUp = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"BaseNodeComponent.useCallback[handleMouseUp]\": ()=>{\n            setIsDragging(false);\n            dragInfoRef.current = null; // 清理拖拽信息\n            // 清理动画帧\n            if (animationFrameRef.current) {\n                cancelAnimationFrame(animationFrameRef.current);\n                animationFrameRef.current = null;\n            }\n            // 确保最后的位置更新被应用\n            if (pendingUpdateRef.current) {\n                onUpdateNode(node.id, {\n                    position: pendingUpdateRef.current\n                });\n                pendingUpdateRef.current = null;\n            }\n            onEvent({\n                type: 'dragEnd',\n                nodeId: node.id\n            });\n            // 移除全局鼠标事件监听\n            document.removeEventListener('mousemove', handleMouseMove);\n            document.removeEventListener('mouseup', handleMouseUp);\n        }\n    }[\"BaseNodeComponent.useCallback[handleMouseUp]\"], [\n        handleMouseMove,\n        onEvent,\n        onUpdateNode,\n        node.id\n    ]);\n    const handleMouseDown = (e)=>{\n        if (e.button !== 0) return; // 只处理左键\n        // 防止在引脚上开始拖拽\n        const target = e.target;\n        if (target.closest('.pin-connector')) {\n            return;\n        }\n        e.preventDefault();\n        e.stopPropagation();\n        // 记录拖拽起始信息\n        dragInfoRef.current = {\n            startX: e.clientX,\n            startY: e.clientY,\n            startNodeX: node.position.x,\n            startNodeY: node.position.y\n        };\n        setIsDragging(true);\n        onEvent({\n            type: 'dragStart',\n            nodeId: node.id,\n            data: {\n                position: node.position\n            }\n        });\n        // 添加全局鼠标事件监听\n        document.addEventListener('mousemove', handleMouseMove);\n        document.addEventListener('mouseup', handleMouseUp);\n    };\n    const handleClick = (e)=>{\n        e.stopPropagation();\n        onEvent({\n            type: 'click',\n            nodeId: node.id\n        });\n    };\n    const handleDoubleClick = (e)=>{\n        e.stopPropagation();\n        onEvent({\n            type: 'doubleClick',\n            nodeId: node.id\n        });\n    };\n    const getStatusColor = ()=>{\n        switch(node.status){\n            case 'running':\n                return 'border-yellow-400 bg-yellow-50';\n            case 'success':\n                return 'border-green-400 bg-green-50';\n            case 'error':\n                return 'border-red-400 bg-red-50';\n            case 'warning':\n                return 'border-orange-400 bg-orange-50';\n            default:\n                return 'border-gray-300 bg-white';\n        }\n    };\n    const getTitleBarColorClasses = (color = 'gray')=>{\n        const colorMap = {\n            gray: 'bg-gray-100 border-gray-200',\n            red: 'bg-red-100 border-red-200',\n            yellow: 'bg-yellow-100 border-yellow-200',\n            green: 'bg-green-100 border-green-200',\n            blue: 'bg-blue-100 border-blue-200',\n            purple: 'bg-purple-100 border-purple-200'\n        };\n        return colorMap[color] || colorMap.gray;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: nodeRef,\n        className: `absolute border-2 rounded-lg shadow-md cursor-move select-none ${getStatusColor()} ${node.selected ? 'ring-2 ring-blue-400' : ''} ${isDragging ? 'shadow-lg' : ''}`,\n        style: {\n            left: node.position.x,\n            top: node.position.y,\n            width: node.size.width,\n            height: node.size.height\n        },\n        onMouseDown: handleMouseDown,\n        onClick: handleClick,\n        onDoubleClick: handleDoubleClick,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `flex items-center justify-between px-2 py-2 border-b rounded-t-md ${getTitleBarColorClasses(node.color)}`,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2 flex-1 min-w-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-lg\",\n                                children: node.icon\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/base/BaseNodeComponent.tsx\",\n                                lineNumber: 209,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm font-medium text-gray-800 truncate\",\n                                children: node.name\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/base/BaseNodeComponent.tsx\",\n                                lineNumber: 210,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/base/BaseNodeComponent.tsx\",\n                        lineNumber: 208,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: (e)=>{\n                            e.stopPropagation();\n                            onEvent({\n                                type: 'testRun',\n                                nodeId: node.id\n                            });\n                        },\n                        className: \"p-1 rounded-full text-gray-400 hover:bg-gray-200 hover:text-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-300\",\n                        title: \"测试运行节点\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            className: \"w-4 h-4\",\n                            fill: \"currentColor\",\n                            viewBox: \"0 0 20 20\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                fillRule: \"evenodd\",\n                                d: \"M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z\",\n                                clipRule: \"evenodd\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/base/BaseNodeComponent.tsx\",\n                                lineNumber: 223,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/base/BaseNodeComponent.tsx\",\n                            lineNumber: 222,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/base/BaseNodeComponent.tsx\",\n                        lineNumber: 214,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/base/BaseNodeComponent.tsx\",\n                lineNumber: 207,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-2 flex-1\",\n                children: children\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/base/BaseNodeComponent.tsx\",\n                lineNumber: 229,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute left-0\",\n                style: {\n                    top: titleHeight\n                },\n                children: inputPins.map((pin, index)=>{\n                    const pinY = pinStartY + index * pinSpacing;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute flex items-center\",\n                        style: {\n                            top: pinY,\n                            left: -6,\n                            transform: 'translateY(-50%)' // 垂直居中对齐\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: `pin-connector w-3 h-3 rounded-full border-2 cursor-pointer transition-colors ${pin.connected ? 'bg-blue-500 border-blue-600' : 'bg-white border-gray-400 hover:border-blue-400'}`,\n                                title: pin.name\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/base/BaseNodeComponent.tsx\",\n                                lineNumber: 247,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"absolute left-4 top-1/2 transform -translate-y-1/2 text-xs text-gray-600 whitespace-nowrap\",\n                                children: pin.name\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/base/BaseNodeComponent.tsx\",\n                                lineNumber: 255,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, pin.id, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/base/BaseNodeComponent.tsx\",\n                        lineNumber: 238,\n                        columnNumber: 13\n                    }, undefined);\n                })\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/base/BaseNodeComponent.tsx\",\n                lineNumber: 234,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute right-0\",\n                style: {\n                    top: titleHeight\n                },\n                children: outputPins.map((pin, index)=>{\n                    const pinY = pinStartY + index * pinSpacing;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute flex items-center justify-end\",\n                        style: {\n                            top: pinY,\n                            right: -6,\n                            transform: 'translateY(-50%)' // 垂直居中对齐\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"absolute right-4 top-1/2 transform -translate-y-1/2 text-xs text-gray-600 whitespace-nowrap\",\n                                children: pin.name\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/base/BaseNodeComponent.tsx\",\n                                lineNumber: 277,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: `pin-connector w-3 h-3 rounded-full border-2 cursor-pointer transition-colors ${pin.connected ? 'bg-green-500 border-green-600' : 'bg-white border-gray-400 hover:border-green-400'}`,\n                                title: pin.name\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/base/BaseNodeComponent.tsx\",\n                                lineNumber: 280,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, pin.id, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/base/BaseNodeComponent.tsx\",\n                        lineNumber: 268,\n                        columnNumber: 13\n                    }, undefined);\n                })\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/base/BaseNodeComponent.tsx\",\n                lineNumber: 264,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/base/BaseNodeComponent.tsx\",\n        lineNumber: 189,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/base/BaseNodeComponent.tsx\n");

/***/ }),

/***/ "(ssr)/./src/nodes/eda/LedNodeComponent.tsx":
/*!********************************************!*\
  !*** ./src/nodes/eda/LedNodeComponent.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LedNodeComponent: () => (/* binding */ LedNodeComponent)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _base_BaseNodeComponent__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/base/BaseNodeComponent */ \"(ssr)/./src/base/BaseNodeComponent.tsx\");\n/* __next_internal_client_entry_do_not_use__ LedNodeComponent auto */ \n\n\nconst LedNodeComponent = ({ node, onEvent, onUpdateNode })=>{\n    const properties = node.properties;\n    const redValue = properties.redValue || 0;\n    const greenValue = properties.greenValue || 0;\n    const blueValue = properties.blueValue || 0;\n    // 生成LED显示颜色\n    const ledColor = `rgb(${redValue}, ${greenValue}, ${blueValue})`;\n    const isOn = redValue > 0 || greenValue > 0 || blueValue > 0;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_base_BaseNodeComponent__WEBPACK_IMPORTED_MODULE_2__.BaseNodeComponent, {\n        node: node,\n        onEvent: onEvent,\n        onUpdateNode: onUpdateNode,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center h-full\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: `w-8 h-8 rounded-full border-2 transition-all duration-300 ${isOn ? 'border-gray-400 shadow-lg' : 'border-gray-300'}`,\n                            style: {\n                                backgroundColor: isOn ? ledColor : '#f3f4f6',\n                                boxShadow: isOn ? `0 0 10px ${ledColor}, 0 0 20px ${ledColor}40` : 'none'\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-4 h-4 rounded-full absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2\",\n                                style: {\n                                    backgroundColor: isOn ? `rgba(${redValue}, ${greenValue}, ${blueValue}, 0.8)` : '#e5e7eb'\n                                }\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/nodes/eda/LedNodeComponent.tsx\",\n                                lineNumber: 46,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/nodes/eda/LedNodeComponent.tsx\",\n                            lineNumber: 32,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute -bottom-1 -right-1\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: `w-2 h-2 rounded-full ${isOn ? 'bg-green-400' : 'bg-gray-300'}`\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/nodes/eda/LedNodeComponent.tsx\",\n                                lineNumber: 58,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/nodes/eda/LedNodeComponent.tsx\",\n                            lineNumber: 57,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/nodes/eda/LedNodeComponent.tsx\",\n                    lineNumber: 30,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/nodes/eda/LedNodeComponent.tsx\",\n                lineNumber: 28,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute bottom-1 left-1 right-1\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-xs text-center text-gray-600 bg-white bg-opacity-75 rounded px-1\",\n                    children: [\n                        properties.colorMode === '1color' && `R:${redValue}`,\n                        properties.colorMode === '2color' && `R:${redValue} G:${greenValue}`,\n                        properties.colorMode === '3color' && `R:${redValue} G:${greenValue} B:${blueValue}`\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/nodes/eda/LedNodeComponent.tsx\",\n                    lineNumber: 69,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/nodes/eda/LedNodeComponent.tsx\",\n                lineNumber: 68,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/nodes/eda/LedNodeComponent.tsx\",\n        lineNumber: 23,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/nodes/eda/LedNodeComponent.tsx\n");

/***/ })

};
;