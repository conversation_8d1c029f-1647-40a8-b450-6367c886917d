"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_ssr_src_nodes_eda_LedNode_tsx";
exports.ids = ["_ssr_src_nodes_eda_LedNode_tsx"];
exports.modules = {

/***/ "(ssr)/./src/base/BaseNode.ts":
/*!******************************!*\
  !*** ./src/base/BaseNode.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BaseNode: () => (/* binding */ BaseNode)\n/* harmony export */ });\nclass BaseNode {\n    constructor(data){\n        this.data = {\n            id: data.id || this.generateId(),\n            type: data.type,\n            name: data.name || 'Base Node',\n            icon: data.icon || '❓',\n            color: data.color || 'gray',\n            description: data.description || '',\n            position: data.position || {\n                x: 0,\n                y: 0\n            },\n            size: data.size || {\n                width: 200,\n                height: 100\n            },\n            status: data.status || 'idle',\n            pins: data.pins || [],\n            properties: data.properties || {},\n            selected: data.selected || false,\n            dragging: data.dragging || false\n        };\n    }\n    // --- Static method for property panel rendering ---\n    static renderProperties(props) {\n        return null; // Default implementation\n    }\n    // --- Core Methods ---\n    generateId() {\n        return `node_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n    }\n    getData() {\n        return {\n            ...this.data\n        };\n    }\n    updateData(updates) {\n        this.data = {\n            ...this.data,\n            ...updates\n        };\n    }\n    setStatus(status) {\n        this.data.status = status;\n    }\n    addPin(pin) {\n        this.data.pins.push(pin);\n    }\n    removePin(pinId) {\n        this.data.pins = this.data.pins.filter((pin)=>pin.id !== pinId);\n    }\n    getPin(pinId) {\n        return this.data.pins.find((pin)=>pin.id === pinId);\n    }\n    setPinValue(pinId, value) {\n        const pin = this.getPin(pinId);\n        if (pin) {\n            pin.value = value;\n            this.onPinValueChanged(pinId, value);\n        }\n    }\n    getPinValue(pinId) {\n        const pin = this.getPin(pinId);\n        return pin?.value;\n    }\n    getInputValue(pinId) {\n        const pin = this.getPin(pinId);\n        if (!pin || pin.type !== 'input') {\n            return undefined;\n        }\n        if (pin.connected) {\n            return pin.value;\n        }\n        return pin.defaultValue ?? pin.value;\n    }\n    setProperty(key, value) {\n        this.data.properties[key] = value;\n        this.onPropertyChanged(key, value);\n    }\n    getProperty(key) {\n        return this.data.properties[key];\n    }\n    handleEvent(event) {\n        switch(event.type){\n            case 'click':\n                this.onClick(event);\n                break;\n            case 'doubleClick':\n                this.onDoubleClick(event);\n                break;\n            case 'dragStart':\n                this.onDragStart(event);\n                break;\n            case 'drag':\n                this.onDrag(event);\n                break;\n            case 'dragEnd':\n                this.onDragEnd(event);\n                break;\n            case 'pinConnect':\n                this.onPinConnect(event);\n                break;\n            case 'pinDisconnect':\n                this.onPinDisconnect(event);\n                break;\n            case 'propertyChange':\n                if (event.data) {\n                    this.onPropertyChanged(event.data.key, event.data.value);\n                }\n                break;\n            case 'testRun':\n                this.process();\n                break;\n        }\n    }\n    // --- Event Handlers (for overriding) ---\n    onClick(event) {}\n    onDoubleClick(event) {}\n    onDragStart(event) {\n        this.data.dragging = true;\n    }\n    onDrag(event) {\n        if (event.data && this.data.dragging) {\n            this.data.position = event.data.position;\n        }\n    }\n    onDragEnd(event) {\n        this.data.dragging = false;\n    }\n    onPinConnect(event) {}\n    onPinDisconnect(event) {}\n    onPropertyChanged(key, value) {\n        this.updatePinsConfiguration();\n    }\n    onPinValueChanged(pinId, value) {\n        const pin = this.getPin(pinId);\n        if (pin && pin.type === 'input') {\n            this.process();\n        }\n    }\n    updatePinsConfiguration() {\n        return this.updateNodeSize();\n    }\n    updateNodeSize() {\n        const inputPins = this.data.pins.filter((pin)=>pin.type === 'input');\n        const outputPins = this.data.pins.filter((pin)=>pin.type === 'output');\n        const maxPins = Math.max(inputPins.length, outputPins.length);\n        const titleHeight = 37;\n        const minContentHeight = 40;\n        const pinPadding = 20;\n        const pinSlotHeight = 28;\n        let contentHeight = minContentHeight;\n        if (maxPins > 0) {\n            const requiredPinsHeight = maxPins * pinSlotHeight + pinPadding;\n            contentHeight = Math.max(minContentHeight, requiredPinsHeight);\n        }\n        // If the node has fixed size, don't update it automatically\n        if (this.data.size.width && this.data.size.height && !this.data.properties.dynamicSize) {\n            // Special case for fixed-size nodes that might still need to adjust if pins overflow\n            const requiredHeight = titleHeight + contentHeight;\n            if (this.data.size.height < requiredHeight) {\n                this.data.size.height = requiredHeight;\n                return true;\n            }\n            return false;\n        }\n        const newHeight = titleHeight + contentHeight;\n        if (this.data.size.height !== newHeight) {\n            this.data.size.height = newHeight;\n            return true;\n        }\n        return false;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvYmFzZS9CYXNlTm9kZS50cyIsIm1hcHBpbmdzIjoiOzs7O0FBR08sTUFBZUE7SUFHcEIsWUFBWUMsSUFBd0QsQ0FBRTtRQUNwRSxJQUFJLENBQUNBLElBQUksR0FBRztZQUNWQyxJQUFJRCxLQUFLQyxFQUFFLElBQUksSUFBSSxDQUFDQyxVQUFVO1lBQzlCQyxNQUFNSCxLQUFLRyxJQUFJO1lBQ2ZDLE1BQU1KLEtBQUtJLElBQUksSUFBSTtZQUNuQkMsTUFBTUwsS0FBS0ssSUFBSSxJQUFJO1lBQ25CQyxPQUFPTixLQUFLTSxLQUFLLElBQUk7WUFDckJDLGFBQWFQLEtBQUtPLFdBQVcsSUFBSTtZQUNqQ0MsVUFBVVIsS0FBS1EsUUFBUSxJQUFJO2dCQUFFQyxHQUFHO2dCQUFHQyxHQUFHO1lBQUU7WUFDeENDLE1BQU1YLEtBQUtXLElBQUksSUFBSTtnQkFBRUMsT0FBTztnQkFBS0MsUUFBUTtZQUFJO1lBQzdDQyxRQUFRZCxLQUFLYyxNQUFNLElBQUk7WUFDdkJDLE1BQU1mLEtBQUtlLElBQUksSUFBSSxFQUFFO1lBQ3JCQyxZQUFZaEIsS0FBS2dCLFVBQVUsSUFBSSxDQUFDO1lBQ2hDQyxVQUFVakIsS0FBS2lCLFFBQVEsSUFBSTtZQUMzQkMsVUFBVWxCLEtBQUtrQixRQUFRLElBQUk7UUFDN0I7SUFDRjtJQUVBLHFEQUFxRDtJQUNyRCxPQUFjQyxpQkFBa0JDLEtBQXlCLEVBQW1CO1FBQzFFLE9BQU8sTUFBTSx5QkFBeUI7SUFDeEM7SUFFQSx1QkFBdUI7SUFDZmxCLGFBQXFCO1FBQzNCLE9BQU8sQ0FBQyxLQUFLLEVBQUVtQixLQUFLQyxHQUFHLEdBQUcsQ0FBQyxFQUFFQyxLQUFLQyxNQUFNLEdBQUdDLFFBQVEsQ0FBQyxJQUFJQyxNQUFNLENBQUMsR0FBRyxJQUFJO0lBQ3hFO0lBRU9DLFVBQXdCO1FBQzdCLE9BQU87WUFBRSxHQUFHLElBQUksQ0FBQzNCLElBQUk7UUFBQztJQUN4QjtJQUVPNEIsV0FBV0MsT0FBOEIsRUFBUTtRQUN0RCxJQUFJLENBQUM3QixJQUFJLEdBQUc7WUFBRSxHQUFHLElBQUksQ0FBQ0EsSUFBSTtZQUFFLEdBQUc2QixPQUFPO1FBQUM7SUFDekM7SUFFT0MsVUFBVWhCLE1BQWtCLEVBQVE7UUFDekMsSUFBSSxDQUFDZCxJQUFJLENBQUNjLE1BQU0sR0FBR0E7SUFDckI7SUFFT2lCLE9BQU9DLEdBQVksRUFBUTtRQUNoQyxJQUFJLENBQUNoQyxJQUFJLENBQUNlLElBQUksQ0FBQ2tCLElBQUksQ0FBQ0Q7SUFDdEI7SUFFT0UsVUFBVUMsS0FBYSxFQUFRO1FBQ3BDLElBQUksQ0FBQ25DLElBQUksQ0FBQ2UsSUFBSSxHQUFHLElBQUksQ0FBQ2YsSUFBSSxDQUFDZSxJQUFJLENBQUNxQixNQUFNLENBQUNKLENBQUFBLE1BQU9BLElBQUkvQixFQUFFLEtBQUtrQztJQUMzRDtJQUVPRSxPQUFPRixLQUFhLEVBQXVCO1FBQ2hELE9BQU8sSUFBSSxDQUFDbkMsSUFBSSxDQUFDZSxJQUFJLENBQUN1QixJQUFJLENBQUNOLENBQUFBLE1BQU9BLElBQUkvQixFQUFFLEtBQUtrQztJQUMvQztJQUVPSSxZQUFZSixLQUFhLEVBQUVLLEtBQVUsRUFBUTtRQUNsRCxNQUFNUixNQUFNLElBQUksQ0FBQ0ssTUFBTSxDQUFDRjtRQUN4QixJQUFJSCxLQUFLO1lBQ1BBLElBQUlRLEtBQUssR0FBR0E7WUFDWixJQUFJLENBQUNDLGlCQUFpQixDQUFDTixPQUFPSztRQUNoQztJQUNGO0lBRU9FLFlBQVlQLEtBQWEsRUFBTztRQUNyQyxNQUFNSCxNQUFNLElBQUksQ0FBQ0ssTUFBTSxDQUFDRjtRQUN4QixPQUFPSCxLQUFLUTtJQUNkO0lBRU9HLGNBQWNSLEtBQWEsRUFBTztRQUN2QyxNQUFNSCxNQUFNLElBQUksQ0FBQ0ssTUFBTSxDQUFDRjtRQUN4QixJQUFJLENBQUNILE9BQU9BLElBQUk3QixJQUFJLEtBQUssU0FBUztZQUNoQyxPQUFPeUM7UUFDVDtRQUNBLElBQUlaLElBQUlhLFNBQVMsRUFBRTtZQUNqQixPQUFPYixJQUFJUSxLQUFLO1FBQ2xCO1FBQ0EsT0FBT1IsSUFBSWMsWUFBWSxJQUFJZCxJQUFJUSxLQUFLO0lBQ3RDO0lBRU9PLFlBQVlDLEdBQVcsRUFBRVIsS0FBVSxFQUFRO1FBQ2hELElBQUksQ0FBQ3hDLElBQUksQ0FBQ2dCLFVBQVUsQ0FBQ2dDLElBQUksR0FBR1I7UUFDNUIsSUFBSSxDQUFDUyxpQkFBaUIsQ0FBQ0QsS0FBS1I7SUFDOUI7SUFFT1UsWUFBWUYsR0FBVyxFQUFPO1FBQ25DLE9BQU8sSUFBSSxDQUFDaEQsSUFBSSxDQUFDZ0IsVUFBVSxDQUFDZ0MsSUFBSTtJQUNsQztJQUVPRyxZQUFZQyxLQUFnQixFQUFRO1FBQ3pDLE9BQVFBLE1BQU1qRCxJQUFJO1lBQ2hCLEtBQUs7Z0JBQVMsSUFBSSxDQUFDa0QsT0FBTyxDQUFDRDtnQkFBUTtZQUNuQyxLQUFLO2dCQUFlLElBQUksQ0FBQ0UsYUFBYSxDQUFDRjtnQkFBUTtZQUMvQyxLQUFLO2dCQUFhLElBQUksQ0FBQ0csV0FBVyxDQUFDSDtnQkFBUTtZQUMzQyxLQUFLO2dCQUFRLElBQUksQ0FBQ0ksTUFBTSxDQUFDSjtnQkFBUTtZQUNqQyxLQUFLO2dCQUFXLElBQUksQ0FBQ0ssU0FBUyxDQUFDTDtnQkFBUTtZQUN2QyxLQUFLO2dCQUFjLElBQUksQ0FBQ00sWUFBWSxDQUFDTjtnQkFBUTtZQUM3QyxLQUFLO2dCQUFpQixJQUFJLENBQUNPLGVBQWUsQ0FBQ1A7Z0JBQVE7WUFDbkQsS0FBSztnQkFDSCxJQUFJQSxNQUFNcEQsSUFBSSxFQUFFO29CQUNkLElBQUksQ0FBQ2lELGlCQUFpQixDQUFDRyxNQUFNcEQsSUFBSSxDQUFDZ0QsR0FBRyxFQUFFSSxNQUFNcEQsSUFBSSxDQUFDd0MsS0FBSztnQkFDekQ7Z0JBQ0E7WUFDRixLQUFLO2dCQUFXLElBQUksQ0FBQ29CLE9BQU87Z0JBQUk7UUFDbEM7SUFDRjtJQUlBLDBDQUEwQztJQUNoQ1AsUUFBUUQsS0FBZ0IsRUFBUSxDQUFDO0lBQ2pDRSxjQUFjRixLQUFnQixFQUFRLENBQUM7SUFDdkNHLFlBQVlILEtBQWdCLEVBQVE7UUFBRSxJQUFJLENBQUNwRCxJQUFJLENBQUNrQixRQUFRLEdBQUc7SUFBTTtJQUNqRXNDLE9BQU9KLEtBQWdCLEVBQVE7UUFBRSxJQUFJQSxNQUFNcEQsSUFBSSxJQUFJLElBQUksQ0FBQ0EsSUFBSSxDQUFDa0IsUUFBUSxFQUFFO1lBQUUsSUFBSSxDQUFDbEIsSUFBSSxDQUFDUSxRQUFRLEdBQUc0QyxNQUFNcEQsSUFBSSxDQUFDUSxRQUFRO1FBQUU7SUFBRTtJQUNySGlELFVBQVVMLEtBQWdCLEVBQVE7UUFBRSxJQUFJLENBQUNwRCxJQUFJLENBQUNrQixRQUFRLEdBQUc7SUFBTztJQUNoRXdDLGFBQWFOLEtBQWdCLEVBQVEsQ0FBQztJQUN0Q08sZ0JBQWdCUCxLQUFnQixFQUFRLENBQUM7SUFDekNILGtCQUFrQkQsR0FBVyxFQUFFUixLQUFVLEVBQVE7UUFDekQsSUFBSSxDQUFDcUIsdUJBQXVCO0lBQzlCO0lBRVVwQixrQkFBa0JOLEtBQWEsRUFBRUssS0FBVSxFQUFRO1FBQzNELE1BQU1SLE1BQU0sSUFBSSxDQUFDSyxNQUFNLENBQUNGO1FBQ3hCLElBQUlILE9BQU9BLElBQUk3QixJQUFJLEtBQUssU0FBUztZQUMvQixJQUFJLENBQUN5RCxPQUFPO1FBQ2Q7SUFDRjtJQUdVQywwQkFBbUM7UUFDM0MsT0FBTyxJQUFJLENBQUNDLGNBQWM7SUFDNUI7SUFFVUEsaUJBQTBCO1FBQ2xDLE1BQU1DLFlBQVksSUFBSSxDQUFDL0QsSUFBSSxDQUFDZSxJQUFJLENBQUNxQixNQUFNLENBQUNKLENBQUFBLE1BQU9BLElBQUk3QixJQUFJLEtBQUs7UUFDNUQsTUFBTTZELGFBQWEsSUFBSSxDQUFDaEUsSUFBSSxDQUFDZSxJQUFJLENBQUNxQixNQUFNLENBQUNKLENBQUFBLE1BQU9BLElBQUk3QixJQUFJLEtBQUs7UUFDN0QsTUFBTThELFVBQVUxQyxLQUFLMkMsR0FBRyxDQUFDSCxVQUFVSSxNQUFNLEVBQUVILFdBQVdHLE1BQU07UUFFNUQsTUFBTUMsY0FBYztRQUNwQixNQUFNQyxtQkFBbUI7UUFDekIsTUFBTUMsYUFBYTtRQUNuQixNQUFNQyxnQkFBZ0I7UUFFdEIsSUFBSUMsZ0JBQWdCSDtRQUNwQixJQUFJSixVQUFVLEdBQUc7WUFDZixNQUFNUSxxQkFBcUJSLFVBQVVNLGdCQUFnQkQ7WUFDckRFLGdCQUFnQmpELEtBQUsyQyxHQUFHLENBQUNHLGtCQUFrQkk7UUFDN0M7UUFFQSw0REFBNEQ7UUFDNUQsSUFBSSxJQUFJLENBQUN6RSxJQUFJLENBQUNXLElBQUksQ0FBQ0MsS0FBSyxJQUFJLElBQUksQ0FBQ1osSUFBSSxDQUFDVyxJQUFJLENBQUNFLE1BQU0sSUFBSSxDQUFDLElBQUksQ0FBQ2IsSUFBSSxDQUFDZ0IsVUFBVSxDQUFDMEQsV0FBVyxFQUFFO1lBQ3BGLHFGQUFxRjtZQUNyRixNQUFNQyxpQkFBaUJQLGNBQWNJO1lBQ3JDLElBQUksSUFBSSxDQUFDeEUsSUFBSSxDQUFDVyxJQUFJLENBQUNFLE1BQU0sR0FBRzhELGdCQUFnQjtnQkFDeEMsSUFBSSxDQUFDM0UsSUFBSSxDQUFDVyxJQUFJLENBQUNFLE1BQU0sR0FBRzhEO2dCQUN4QixPQUFPO1lBQ1g7WUFDQSxPQUFPO1FBQ1g7UUFHQSxNQUFNQyxZQUFZUixjQUFjSTtRQUNoQyxJQUFJLElBQUksQ0FBQ3hFLElBQUksQ0FBQ1csSUFBSSxDQUFDRSxNQUFNLEtBQUsrRCxXQUFXO1lBQ3ZDLElBQUksQ0FBQzVFLElBQUksQ0FBQ1csSUFBSSxDQUFDRSxNQUFNLEdBQUcrRDtZQUN4QixPQUFPO1FBQ1Q7UUFDQSxPQUFPO0lBQ1Q7QUFDRiIsInNvdXJjZXMiOlsiL1VzZXJzL3VzZXIvRG9jdW1lbnRzL2F1Z21lbnQtcHJvamVjdHMvbmV4dGpzLXN1cGVyTm9kZS9zcmMvYmFzZS9CYXNlTm9kZS50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBCYXNlTm9kZURhdGEsIE5vZGVQaW4sIE5vZGVFdmVudCwgTm9kZVN0YXR1cywgTm9kZUNvbXBvbmVudFByb3BzIH0gZnJvbSAnLi9ub2RlJztcbmltcG9ydCBSZWFjdCBmcm9tICdyZWFjdCc7XG5cbmV4cG9ydCBhYnN0cmFjdCBjbGFzcyBCYXNlTm9kZSB7XG4gIHByb3RlY3RlZCBkYXRhOiBCYXNlTm9kZURhdGE7XG5cbiAgY29uc3RydWN0b3IoZGF0YTogUGljazxCYXNlTm9kZURhdGEsICd0eXBlJz4gJiBQYXJ0aWFsPEJhc2VOb2RlRGF0YT4pIHtcbiAgICB0aGlzLmRhdGEgPSB7XG4gICAgICBpZDogZGF0YS5pZCB8fCB0aGlzLmdlbmVyYXRlSWQoKSxcbiAgICAgIHR5cGU6IGRhdGEudHlwZSxcbiAgICAgIG5hbWU6IGRhdGEubmFtZSB8fCAnQmFzZSBOb2RlJyxcbiAgICAgIGljb246IGRhdGEuaWNvbiB8fCAn4p2TJyxcbiAgICAgIGNvbG9yOiBkYXRhLmNvbG9yIHx8ICdncmF5JyxcbiAgICAgIGRlc2NyaXB0aW9uOiBkYXRhLmRlc2NyaXB0aW9uIHx8ICcnLFxuICAgICAgcG9zaXRpb246IGRhdGEucG9zaXRpb24gfHwgeyB4OiAwLCB5OiAwIH0sXG4gICAgICBzaXplOiBkYXRhLnNpemUgfHwgeyB3aWR0aDogMjAwLCBoZWlnaHQ6IDEwMCB9LFxuICAgICAgc3RhdHVzOiBkYXRhLnN0YXR1cyB8fCAnaWRsZScsXG4gICAgICBwaW5zOiBkYXRhLnBpbnMgfHwgW10sXG4gICAgICBwcm9wZXJ0aWVzOiBkYXRhLnByb3BlcnRpZXMgfHwge30sXG4gICAgICBzZWxlY3RlZDogZGF0YS5zZWxlY3RlZCB8fCBmYWxzZSxcbiAgICAgIGRyYWdnaW5nOiBkYXRhLmRyYWdnaW5nIHx8IGZhbHNlLFxuICAgIH07XG4gIH1cblxuICAvLyAtLS0gU3RhdGljIG1ldGhvZCBmb3IgcHJvcGVydHkgcGFuZWwgcmVuZGVyaW5nIC0tLVxuICBwdWJsaWMgc3RhdGljIHJlbmRlclByb3BlcnRpZXM/KHByb3BzOiBOb2RlQ29tcG9uZW50UHJvcHMpOiBSZWFjdC5SZWFjdE5vZGUge1xuICAgIHJldHVybiBudWxsOyAvLyBEZWZhdWx0IGltcGxlbWVudGF0aW9uXG4gIH1cblxuICAvLyAtLS0gQ29yZSBNZXRob2RzIC0tLVxuICBwcml2YXRlIGdlbmVyYXRlSWQoKTogc3RyaW5nIHtcbiAgICByZXR1cm4gYG5vZGVfJHtEYXRlLm5vdygpfV8ke01hdGgucmFuZG9tKCkudG9TdHJpbmcoMzYpLnN1YnN0cigyLCA5KX1gO1xuICB9XG5cbiAgcHVibGljIGdldERhdGEoKTogQmFzZU5vZGVEYXRhIHtcbiAgICByZXR1cm4geyAuLi50aGlzLmRhdGEgfTtcbiAgfVxuXG4gIHB1YmxpYyB1cGRhdGVEYXRhKHVwZGF0ZXM6IFBhcnRpYWw8QmFzZU5vZGVEYXRhPik6IHZvaWQge1xuICAgIHRoaXMuZGF0YSA9IHsgLi4udGhpcy5kYXRhLCAuLi51cGRhdGVzIH07XG4gIH1cblxuICBwdWJsaWMgc2V0U3RhdHVzKHN0YXR1czogTm9kZVN0YXR1cyk6IHZvaWQge1xuICAgIHRoaXMuZGF0YS5zdGF0dXMgPSBzdGF0dXM7XG4gIH1cblxuICBwdWJsaWMgYWRkUGluKHBpbjogTm9kZVBpbik6IHZvaWQge1xuICAgIHRoaXMuZGF0YS5waW5zLnB1c2gocGluKTtcbiAgfVxuXG4gIHB1YmxpYyByZW1vdmVQaW4ocGluSWQ6IHN0cmluZyk6IHZvaWQge1xuICAgIHRoaXMuZGF0YS5waW5zID0gdGhpcy5kYXRhLnBpbnMuZmlsdGVyKHBpbiA9PiBwaW4uaWQgIT09IHBpbklkKTtcbiAgfVxuXG4gIHB1YmxpYyBnZXRQaW4ocGluSWQ6IHN0cmluZyk6IE5vZGVQaW4gfCB1bmRlZmluZWQge1xuICAgIHJldHVybiB0aGlzLmRhdGEucGlucy5maW5kKHBpbiA9PiBwaW4uaWQgPT09IHBpbklkKTtcbiAgfVxuXG4gIHB1YmxpYyBzZXRQaW5WYWx1ZShwaW5JZDogc3RyaW5nLCB2YWx1ZTogYW55KTogdm9pZCB7XG4gICAgY29uc3QgcGluID0gdGhpcy5nZXRQaW4ocGluSWQpO1xuICAgIGlmIChwaW4pIHtcbiAgICAgIHBpbi52YWx1ZSA9IHZhbHVlO1xuICAgICAgdGhpcy5vblBpblZhbHVlQ2hhbmdlZChwaW5JZCwgdmFsdWUpO1xuICAgIH1cbiAgfVxuXG4gIHB1YmxpYyBnZXRQaW5WYWx1ZShwaW5JZDogc3RyaW5nKTogYW55IHtcbiAgICBjb25zdCBwaW4gPSB0aGlzLmdldFBpbihwaW5JZCk7XG4gICAgcmV0dXJuIHBpbj8udmFsdWU7XG4gIH1cbiAgXG4gIHB1YmxpYyBnZXRJbnB1dFZhbHVlKHBpbklkOiBzdHJpbmcpOiBhbnkge1xuICAgIGNvbnN0IHBpbiA9IHRoaXMuZ2V0UGluKHBpbklkKTtcbiAgICBpZiAoIXBpbiB8fCBwaW4udHlwZSAhPT0gJ2lucHV0Jykge1xuICAgICAgcmV0dXJuIHVuZGVmaW5lZDtcbiAgICB9XG4gICAgaWYgKHBpbi5jb25uZWN0ZWQpIHtcbiAgICAgIHJldHVybiBwaW4udmFsdWU7XG4gICAgfVxuICAgIHJldHVybiBwaW4uZGVmYXVsdFZhbHVlID8/IHBpbi52YWx1ZTtcbiAgfVxuXG4gIHB1YmxpYyBzZXRQcm9wZXJ0eShrZXk6IHN0cmluZywgdmFsdWU6IGFueSk6IHZvaWQge1xuICAgIHRoaXMuZGF0YS5wcm9wZXJ0aWVzW2tleV0gPSB2YWx1ZTtcbiAgICB0aGlzLm9uUHJvcGVydHlDaGFuZ2VkKGtleSwgdmFsdWUpO1xuICB9XG5cbiAgcHVibGljIGdldFByb3BlcnR5KGtleTogc3RyaW5nKTogYW55IHtcbiAgICByZXR1cm4gdGhpcy5kYXRhLnByb3BlcnRpZXNba2V5XTtcbiAgfVxuXG4gIHB1YmxpYyBoYW5kbGVFdmVudChldmVudDogTm9kZUV2ZW50KTogdm9pZCB7XG4gICAgc3dpdGNoIChldmVudC50eXBlKSB7XG4gICAgICBjYXNlICdjbGljayc6IHRoaXMub25DbGljayhldmVudCk7IGJyZWFrO1xuICAgICAgY2FzZSAnZG91YmxlQ2xpY2snOiB0aGlzLm9uRG91YmxlQ2xpY2soZXZlbnQpOyBicmVhaztcbiAgICAgIGNhc2UgJ2RyYWdTdGFydCc6IHRoaXMub25EcmFnU3RhcnQoZXZlbnQpOyBicmVhaztcbiAgICAgIGNhc2UgJ2RyYWcnOiB0aGlzLm9uRHJhZyhldmVudCk7IGJyZWFrO1xuICAgICAgY2FzZSAnZHJhZ0VuZCc6IHRoaXMub25EcmFnRW5kKGV2ZW50KTsgYnJlYWs7XG4gICAgICBjYXNlICdwaW5Db25uZWN0JzogdGhpcy5vblBpbkNvbm5lY3QoZXZlbnQpOyBicmVhaztcbiAgICAgIGNhc2UgJ3BpbkRpc2Nvbm5lY3QnOiB0aGlzLm9uUGluRGlzY29ubmVjdChldmVudCk7IGJyZWFrO1xuICAgICAgY2FzZSAncHJvcGVydHlDaGFuZ2UnOlxuICAgICAgICBpZiAoZXZlbnQuZGF0YSkge1xuICAgICAgICAgIHRoaXMub25Qcm9wZXJ0eUNoYW5nZWQoZXZlbnQuZGF0YS5rZXksIGV2ZW50LmRhdGEudmFsdWUpO1xuICAgICAgICB9XG4gICAgICAgIGJyZWFrO1xuICAgICAgY2FzZSAndGVzdFJ1bic6IHRoaXMucHJvY2VzcygpOyBicmVhaztcbiAgICB9XG4gIH1cblxuICBwdWJsaWMgYWJzdHJhY3QgcHJvY2VzcygpOiBQcm9taXNlPHZvaWQ+O1xuXG4gIC8vIC0tLSBFdmVudCBIYW5kbGVycyAoZm9yIG92ZXJyaWRpbmcpIC0tLVxuICBwcm90ZWN0ZWQgb25DbGljayhldmVudDogTm9kZUV2ZW50KTogdm9pZCB7fVxuICBwcm90ZWN0ZWQgb25Eb3VibGVDbGljayhldmVudDogTm9kZUV2ZW50KTogdm9pZCB7fVxuICBwcm90ZWN0ZWQgb25EcmFnU3RhcnQoZXZlbnQ6IE5vZGVFdmVudCk6IHZvaWQgeyB0aGlzLmRhdGEuZHJhZ2dpbmcgPSB0cnVlOyB9XG4gIHByb3RlY3RlZCBvbkRyYWcoZXZlbnQ6IE5vZGVFdmVudCk6IHZvaWQgeyBpZiAoZXZlbnQuZGF0YSAmJiB0aGlzLmRhdGEuZHJhZ2dpbmcpIHsgdGhpcy5kYXRhLnBvc2l0aW9uID0gZXZlbnQuZGF0YS5wb3NpdGlvbjsgfSB9XG4gIHByb3RlY3RlZCBvbkRyYWdFbmQoZXZlbnQ6IE5vZGVFdmVudCk6IHZvaWQgeyB0aGlzLmRhdGEuZHJhZ2dpbmcgPSBmYWxzZTsgfVxuICBwcm90ZWN0ZWQgb25QaW5Db25uZWN0KGV2ZW50OiBOb2RlRXZlbnQpOiB2b2lkIHt9XG4gIHByb3RlY3RlZCBvblBpbkRpc2Nvbm5lY3QoZXZlbnQ6IE5vZGVFdmVudCk6IHZvaWQge31cbiAgcHJvdGVjdGVkIG9uUHJvcGVydHlDaGFuZ2VkKGtleTogc3RyaW5nLCB2YWx1ZTogYW55KTogdm9pZCB7XG4gICAgdGhpcy51cGRhdGVQaW5zQ29uZmlndXJhdGlvbigpO1xuICB9XG4gIFxuICBwcm90ZWN0ZWQgb25QaW5WYWx1ZUNoYW5nZWQocGluSWQ6IHN0cmluZywgdmFsdWU6IGFueSk6IHZvaWQge1xuICAgIGNvbnN0IHBpbiA9IHRoaXMuZ2V0UGluKHBpbklkKTtcbiAgICBpZiAocGluICYmIHBpbi50eXBlID09PSAnaW5wdXQnKSB7XG4gICAgICB0aGlzLnByb2Nlc3MoKTtcbiAgICB9XG4gIH1cblxuXG4gIHByb3RlY3RlZCB1cGRhdGVQaW5zQ29uZmlndXJhdGlvbigpOiBib29sZWFuIHtcbiAgICByZXR1cm4gdGhpcy51cGRhdGVOb2RlU2l6ZSgpO1xuICB9XG5cbiAgcHJvdGVjdGVkIHVwZGF0ZU5vZGVTaXplKCk6IGJvb2xlYW4ge1xuICAgIGNvbnN0IGlucHV0UGlucyA9IHRoaXMuZGF0YS5waW5zLmZpbHRlcihwaW4gPT4gcGluLnR5cGUgPT09ICdpbnB1dCcpO1xuICAgIGNvbnN0IG91dHB1dFBpbnMgPSB0aGlzLmRhdGEucGlucy5maWx0ZXIocGluID0+IHBpbi50eXBlID09PSAnb3V0cHV0Jyk7XG4gICAgY29uc3QgbWF4UGlucyA9IE1hdGgubWF4KGlucHV0UGlucy5sZW5ndGgsIG91dHB1dFBpbnMubGVuZ3RoKTtcblxuICAgIGNvbnN0IHRpdGxlSGVpZ2h0ID0gMzc7XG4gICAgY29uc3QgbWluQ29udGVudEhlaWdodCA9IDQwO1xuICAgIGNvbnN0IHBpblBhZGRpbmcgPSAyMDsgXG4gICAgY29uc3QgcGluU2xvdEhlaWdodCA9IDI4O1xuXG4gICAgbGV0IGNvbnRlbnRIZWlnaHQgPSBtaW5Db250ZW50SGVpZ2h0O1xuICAgIGlmIChtYXhQaW5zID4gMCkge1xuICAgICAgY29uc3QgcmVxdWlyZWRQaW5zSGVpZ2h0ID0gbWF4UGlucyAqIHBpblNsb3RIZWlnaHQgKyBwaW5QYWRkaW5nO1xuICAgICAgY29udGVudEhlaWdodCA9IE1hdGgubWF4KG1pbkNvbnRlbnRIZWlnaHQsIHJlcXVpcmVkUGluc0hlaWdodCk7XG4gICAgfVxuICAgIFxuICAgIC8vIElmIHRoZSBub2RlIGhhcyBmaXhlZCBzaXplLCBkb24ndCB1cGRhdGUgaXQgYXV0b21hdGljYWxseVxuICAgIGlmICh0aGlzLmRhdGEuc2l6ZS53aWR0aCAmJiB0aGlzLmRhdGEuc2l6ZS5oZWlnaHQgJiYgIXRoaXMuZGF0YS5wcm9wZXJ0aWVzLmR5bmFtaWNTaXplKSB7XG4gICAgICAgIC8vIFNwZWNpYWwgY2FzZSBmb3IgZml4ZWQtc2l6ZSBub2RlcyB0aGF0IG1pZ2h0IHN0aWxsIG5lZWQgdG8gYWRqdXN0IGlmIHBpbnMgb3ZlcmZsb3dcbiAgICAgICAgY29uc3QgcmVxdWlyZWRIZWlnaHQgPSB0aXRsZUhlaWdodCArIGNvbnRlbnRIZWlnaHQ7XG4gICAgICAgIGlmICh0aGlzLmRhdGEuc2l6ZS5oZWlnaHQgPCByZXF1aXJlZEhlaWdodCkge1xuICAgICAgICAgICAgdGhpcy5kYXRhLnNpemUuaGVpZ2h0ID0gcmVxdWlyZWRIZWlnaHQ7XG4gICAgICAgICAgICByZXR1cm4gdHJ1ZTtcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gZmFsc2U7XG4gICAgfVxuXG5cbiAgICBjb25zdCBuZXdIZWlnaHQgPSB0aXRsZUhlaWdodCArIGNvbnRlbnRIZWlnaHQ7XG4gICAgaWYgKHRoaXMuZGF0YS5zaXplLmhlaWdodCAhPT0gbmV3SGVpZ2h0KSB7XG4gICAgICB0aGlzLmRhdGEuc2l6ZS5oZWlnaHQgPSBuZXdIZWlnaHQ7XG4gICAgICByZXR1cm4gdHJ1ZTtcbiAgICB9XG4gICAgcmV0dXJuIGZhbHNlO1xuICB9XG59Il0sIm5hbWVzIjpbIkJhc2VOb2RlIiwiZGF0YSIsImlkIiwiZ2VuZXJhdGVJZCIsInR5cGUiLCJuYW1lIiwiaWNvbiIsImNvbG9yIiwiZGVzY3JpcHRpb24iLCJwb3NpdGlvbiIsIngiLCJ5Iiwic2l6ZSIsIndpZHRoIiwiaGVpZ2h0Iiwic3RhdHVzIiwicGlucyIsInByb3BlcnRpZXMiLCJzZWxlY3RlZCIsImRyYWdnaW5nIiwicmVuZGVyUHJvcGVydGllcyIsInByb3BzIiwiRGF0ZSIsIm5vdyIsIk1hdGgiLCJyYW5kb20iLCJ0b1N0cmluZyIsInN1YnN0ciIsImdldERhdGEiLCJ1cGRhdGVEYXRhIiwidXBkYXRlcyIsInNldFN0YXR1cyIsImFkZFBpbiIsInBpbiIsInB1c2giLCJyZW1vdmVQaW4iLCJwaW5JZCIsImZpbHRlciIsImdldFBpbiIsImZpbmQiLCJzZXRQaW5WYWx1ZSIsInZhbHVlIiwib25QaW5WYWx1ZUNoYW5nZWQiLCJnZXRQaW5WYWx1ZSIsImdldElucHV0VmFsdWUiLCJ1bmRlZmluZWQiLCJjb25uZWN0ZWQiLCJkZWZhdWx0VmFsdWUiLCJzZXRQcm9wZXJ0eSIsImtleSIsIm9uUHJvcGVydHlDaGFuZ2VkIiwiZ2V0UHJvcGVydHkiLCJoYW5kbGVFdmVudCIsImV2ZW50Iiwib25DbGljayIsIm9uRG91YmxlQ2xpY2siLCJvbkRyYWdTdGFydCIsIm9uRHJhZyIsIm9uRHJhZ0VuZCIsIm9uUGluQ29ubmVjdCIsIm9uUGluRGlzY29ubmVjdCIsInByb2Nlc3MiLCJ1cGRhdGVQaW5zQ29uZmlndXJhdGlvbiIsInVwZGF0ZU5vZGVTaXplIiwiaW5wdXRQaW5zIiwib3V0cHV0UGlucyIsIm1heFBpbnMiLCJtYXgiLCJsZW5ndGgiLCJ0aXRsZUhlaWdodCIsIm1pbkNvbnRlbnRIZWlnaHQiLCJwaW5QYWRkaW5nIiwicGluU2xvdEhlaWdodCIsImNvbnRlbnRIZWlnaHQiLCJyZXF1aXJlZFBpbnNIZWlnaHQiLCJkeW5hbWljU2l6ZSIsInJlcXVpcmVkSGVpZ2h0IiwibmV3SGVpZ2h0Il0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/base/BaseNode.ts\n");

/***/ }),

/***/ "(ssr)/./src/nodes/eda/LedNode.tsx":
/*!***********************************!*\
  !*** ./src/nodes/eda/LedNode.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LedNode: () => (/* binding */ LedNode)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _base_BaseNode__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../base/BaseNode */ \"(ssr)/./src/base/BaseNode.ts\");\n\n\n\nclass LedNode extends _base_BaseNode__WEBPACK_IMPORTED_MODULE_2__.BaseNode {\n    constructor(data){\n        super({\n            type: 'led',\n            name: 'RGB LED',\n            icon: '💡',\n            color: 'yellow',\n            description: '3色变色LED灯节点',\n            size: {\n                width: 140,\n                height: 100\n            },\n            properties: {\n                visiblePins: {\n                    r: true,\n                    g: true,\n                    b: true\n                },\n                inputRange: 'percentage',\n                redValue: 0,\n                greenValue: 0,\n                blueValue: 0,\n                brightness: 100\n            },\n            ...data\n        });\n        this.initializePins();\n    }\n    initializePins() {\n        this.updatePinsConfiguration();\n    }\n    updatePinsConfiguration() {\n        this.data.pins = [];\n        const { visiblePins, inputRange } = this.getProperties();\n        const dataType = inputRange === 'boolean' ? 'boolean' : 'number';\n        if (visiblePins.r) this.addPin({\n            id: 'red_input',\n            name: 'R',\n            type: 'input',\n            dataType,\n            value: 0,\n            defaultValue: 128\n        });\n        if (visiblePins.g) this.addPin({\n            id: 'green_input',\n            name: 'G',\n            type: 'input',\n            dataType,\n            value: 0,\n            defaultValue: 128\n        });\n        if (visiblePins.b) this.addPin({\n            id: 'blue_input',\n            name: 'B',\n            type: 'input',\n            dataType,\n            value: 0,\n            defaultValue: 128\n        });\n        this.addPin({\n            id: 'brightness_input',\n            name: 'Brightness',\n            type: 'input',\n            dataType: 'number',\n            value: 100,\n            defaultValue: 100\n        });\n        this.addPin({\n            id: 'color_output',\n            name: 'Color',\n            type: 'output',\n            dataType: 'color',\n            value: '#000000'\n        });\n        return super.updatePinsConfiguration();\n    }\n    async process() {\n        try {\n            this.setStatus('running');\n            const { inputRange } = this.getProperties();\n            const brightness = this.getInputValue('brightness_input');\n            const red = this.normalizeInputValue(this.getInputValue('red_input') || 0, inputRange);\n            const green = this.normalizeInputValue(this.getInputValue('green_input') || 0, inputRange);\n            const blue = this.normalizeInputValue(this.getInputValue('blue_input') || 0, inputRange);\n            const brightnessMultiplier = Math.max(0, Math.min(100, brightness)) / 100;\n            const finalRed = Math.round(red * brightnessMultiplier);\n            const finalGreen = Math.round(green * brightnessMultiplier);\n            const finalBlue = Math.round(blue * brightnessMultiplier);\n            this.setProperty('redValue', finalRed);\n            this.setProperty('greenValue', finalGreen);\n            this.setProperty('blueValue', finalBlue);\n            const colorHex = `#${finalRed.toString(16).padStart(2, '0')}${finalGreen.toString(16).padStart(2, '0')}${finalBlue.toString(16).padStart(2, '0')}`;\n            this.setPinValue('color_output', colorHex);\n            this.setStatus('success');\n        } catch (error) {\n            console.error('LED Node processing error:', error);\n            this.setStatus('error');\n        }\n    }\n    normalizeInputValue(value, inputRange) {\n        if (typeof value === 'boolean') return value ? 255 : 0;\n        const numValue = Number(value) || 0;\n        switch(inputRange){\n            case 'percentage':\n                return Math.round(Math.max(0, Math.min(100, numValue)) * 2.55);\n            case '0-255':\n                return Math.round(Math.max(0, Math.min(255, numValue)));\n            case '0-1':\n                return Math.round(Math.max(0, Math.min(1, numValue)) * 255);\n            case 'boolean':\n                return numValue > 0 ? 255 : 0;\n            default:\n                return Math.round(Math.max(0, Math.min(255, numValue)));\n        }\n    }\n    getProperties() {\n        return this.data.properties;\n    }\n    // --- 属性面板渲染逻辑 ---\n    static renderProperties({ node, onUpdateNode }) {\n        const properties = node.properties;\n        const handlePropertyChange = (key, value)=>{\n            onUpdateNode(node.id, {\n                properties: {\n                    ...node.properties,\n                    [key]: value\n                }\n            });\n        };\n        const handlePinVisibilityChange = (pin, isVisible)=>{\n            const currentVisibility = properties.visiblePins || {\n                r: true,\n                g: true,\n                b: true\n            };\n            const visibleCount = Object.values(currentVisibility).filter((v)=>v).length;\n            if (visibleCount === 1 && !isVisible) {\n                alert(\"至少需要一个可见的颜色引脚。\");\n                return;\n            }\n            const newVisibility = {\n                ...currentVisibility,\n                [pin]: isVisible\n            };\n            handlePropertyChange('visiblePins', newVisibility);\n        };\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                            children: \"可见引脚\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/nodes/eda/LedNode.tsx\",\n                            lineNumber: 119,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"checkbox\",\n                                            checked: properties.visiblePins?.r ?? true,\n                                            onChange: (e)=>handlePinVisibilityChange('r', e.target.checked),\n                                            className: \"h-4 w-4 rounded border-gray-300 text-red-600 focus:ring-red-500\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/nodes/eda/LedNode.tsx\",\n                                            lineNumber: 122,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"ml-2 text-sm text-red-600\",\n                                            children: \"R\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/nodes/eda/LedNode.tsx\",\n                                            lineNumber: 123,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/nodes/eda/LedNode.tsx\",\n                                    lineNumber: 121,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"checkbox\",\n                                            checked: properties.visiblePins?.g ?? true,\n                                            onChange: (e)=>handlePinVisibilityChange('g', e.target.checked),\n                                            className: \"h-4 w-4 rounded border-gray-300 text-green-600 focus:ring-green-500\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/nodes/eda/LedNode.tsx\",\n                                            lineNumber: 126,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"ml-2 text-sm text-green-600\",\n                                            children: \"G\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/nodes/eda/LedNode.tsx\",\n                                            lineNumber: 127,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/nodes/eda/LedNode.tsx\",\n                                    lineNumber: 125,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"checkbox\",\n                                            checked: properties.visiblePins?.b ?? true,\n                                            onChange: (e)=>handlePinVisibilityChange('b', e.target.checked),\n                                            className: \"h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/nodes/eda/LedNode.tsx\",\n                                            lineNumber: 130,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"ml-2 text-sm text-blue-600\",\n                                            children: \"B\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/nodes/eda/LedNode.tsx\",\n                                            lineNumber: 131,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/nodes/eda/LedNode.tsx\",\n                                    lineNumber: 129,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/nodes/eda/LedNode.tsx\",\n                            lineNumber: 120,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/nodes/eda/LedNode.tsx\",\n                    lineNumber: 118,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                            children: \"输入范围\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/nodes/eda/LedNode.tsx\",\n                            lineNumber: 136,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                            value: properties.inputRange || 'percentage',\n                            onChange: (e)=>handlePropertyChange('inputRange', e.target.value),\n                            className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"percentage\",\n                                    children: \"百分比 (0-100)\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/nodes/eda/LedNode.tsx\",\n                                    lineNumber: 138,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"0-255\",\n                                    children: \"0-255\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/nodes/eda/LedNode.tsx\",\n                                    lineNumber: 139,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"0-1\",\n                                    children: \"0-1\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/nodes/eda/LedNode.tsx\",\n                                    lineNumber: 140,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"boolean\",\n                                    children: \"布尔值\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/nodes/eda/LedNode.tsx\",\n                                    lineNumber: 141,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/nodes/eda/LedNode.tsx\",\n                            lineNumber: 137,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/nodes/eda/LedNode.tsx\",\n                    lineNumber: 135,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/nodes/eda/LedNode.tsx\",\n            lineNumber: 117,\n            columnNumber: 7\n        }, this);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/nodes/eda/LedNode.tsx\n");

/***/ })

};
;