"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_ssr_src_nodes_controls_KnobNode_tsx";
exports.ids = ["_ssr_src_nodes_controls_KnobNode_tsx"];
exports.modules = {

/***/ "(ssr)/./src/base/BaseNode.ts":
/*!******************************!*\
  !*** ./src/base/BaseNode.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BaseNode: () => (/* binding */ BaseNode)\n/* harmony export */ });\nclass BaseNode {\n    constructor(data){\n        this.data = {\n            id: data.id || this.generateId(),\n            type: data.type,\n            name: data.name || 'Base Node',\n            icon: data.icon || '❓',\n            color: data.color || 'gray',\n            description: data.description || '',\n            position: data.position || {\n                x: 0,\n                y: 0\n            },\n            size: data.size || {\n                width: 200,\n                height: 100\n            },\n            status: data.status || 'idle',\n            pins: data.pins || [],\n            properties: data.properties || {},\n            selected: data.selected || false,\n            dragging: data.dragging || false\n        };\n    }\n    // --- Static method for property panel rendering ---\n    static renderProperties(props) {\n        return null; // Default implementation\n    }\n    // --- Core Methods ---\n    generateId() {\n        return `node_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n    }\n    getData() {\n        return {\n            ...this.data\n        };\n    }\n    updateData(updates) {\n        this.data = {\n            ...this.data,\n            ...updates\n        };\n    }\n    setStatus(status) {\n        this.data.status = status;\n    }\n    addPin(pin) {\n        this.data.pins.push(pin);\n    }\n    removePin(pinId) {\n        this.data.pins = this.data.pins.filter((pin)=>pin.id !== pinId);\n    }\n    getPin(pinId) {\n        return this.data.pins.find((pin)=>pin.id === pinId);\n    }\n    setPinValue(pinId, value) {\n        const pin = this.getPin(pinId);\n        if (pin) {\n            pin.value = value;\n            this.onPinValueChanged(pinId, value);\n        }\n    }\n    getPinValue(pinId) {\n        const pin = this.getPin(pinId);\n        return pin?.value;\n    }\n    getInputValue(pinId) {\n        const pin = this.getPin(pinId);\n        if (!pin || pin.type !== 'input') {\n            return undefined;\n        }\n        if (pin.connected) {\n            return pin.value;\n        }\n        return pin.defaultValue ?? pin.value;\n    }\n    setProperty(key, value) {\n        this.data.properties[key] = value;\n        this.onPropertyChanged(key, value);\n    }\n    getProperty(key) {\n        return this.data.properties[key];\n    }\n    handleEvent(event) {\n        switch(event.type){\n            case 'click':\n                this.onClick(event);\n                break;\n            case 'doubleClick':\n                this.onDoubleClick(event);\n                break;\n            case 'dragStart':\n                this.onDragStart(event);\n                break;\n            case 'drag':\n                this.onDrag(event);\n                break;\n            case 'dragEnd':\n                this.onDragEnd(event);\n                break;\n            case 'pinConnect':\n                this.onPinConnect(event);\n                break;\n            case 'pinDisconnect':\n                this.onPinDisconnect(event);\n                break;\n            case 'propertyChange':\n                if (event.data) {\n                    this.onPropertyChanged(event.data.key, event.data.value);\n                }\n                break;\n            case 'testRun':\n                this.process();\n                break;\n        }\n    }\n    // --- Event Handlers (for overriding) ---\n    onClick(event) {}\n    onDoubleClick(event) {}\n    onDragStart(event) {\n        this.data.dragging = true;\n    }\n    onDrag(event) {\n        if (event.data && this.data.dragging) {\n            this.data.position = event.data.position;\n        }\n    }\n    onDragEnd(event) {\n        this.data.dragging = false;\n    }\n    onPinConnect(event) {}\n    onPinDisconnect(event) {}\n    onPropertyChanged(key, value) {\n        this.updatePinsConfiguration();\n    }\n    onPinValueChanged(pinId, value) {\n        const pin = this.getPin(pinId);\n        if (pin && pin.type === 'input') {\n            this.process();\n        }\n    }\n    updatePinsConfiguration() {\n        return this.updateNodeSize();\n    }\n    updateNodeSize() {\n        const inputPins = this.data.pins.filter((pin)=>pin.type === 'input');\n        const outputPins = this.data.pins.filter((pin)=>pin.type === 'output');\n        const maxPins = Math.max(inputPins.length, outputPins.length);\n        const titleHeight = 37;\n        const minContentHeight = 40;\n        const pinPadding = 20;\n        const pinSlotHeight = 28;\n        let contentHeight = minContentHeight;\n        if (maxPins > 0) {\n            const requiredPinsHeight = maxPins * pinSlotHeight + pinPadding;\n            contentHeight = Math.max(minContentHeight, requiredPinsHeight);\n        }\n        // If the node has fixed size, don't update it automatically\n        if (this.data.size.width && this.data.size.height && !this.data.properties.dynamicSize) {\n            // Special case for fixed-size nodes that might still need to adjust if pins overflow\n            const requiredHeight = titleHeight + contentHeight;\n            if (this.data.size.height < requiredHeight) {\n                this.data.size.height = requiredHeight;\n                return true;\n            }\n            return false;\n        }\n        const newHeight = titleHeight + contentHeight;\n        if (this.data.size.height !== newHeight) {\n            this.data.size.height = newHeight;\n            return true;\n        }\n        return false;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/base/BaseNode.ts\n");

/***/ }),

/***/ "(ssr)/./src/nodes/controls/KnobNode.tsx":
/*!*****************************************!*\
  !*** ./src/nodes/controls/KnobNode.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   KnobNode: () => (/* binding */ KnobNode)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _base_BaseNode__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../base/BaseNode */ \"(ssr)/./src/base/BaseNode.ts\");\n\n\n\nclass KnobNode extends _base_BaseNode__WEBPACK_IMPORTED_MODULE_2__.BaseNode {\n    constructor(data){\n        super({\n            type: 'knob',\n            name: '旋钮',\n            icon: '🎛️',\n            color: 'blue',\n            description: '一个可以调节数值的旋钮',\n            properties: {\n                value: 50,\n                outputRange: 'percentage'\n            },\n            size: {\n                width: 160,\n                height: 120\n            },\n            ...data\n        });\n        this.initializePins();\n    }\n    initializePins() {\n        this.updatePinsConfiguration();\n    }\n    updatePinsConfiguration() {\n        const value = this.getProperty('value');\n        this.data.pins = [\n            {\n                id: 'value_output',\n                name: 'Value',\n                type: 'output',\n                dataType: 'number',\n                value\n            }\n        ];\n        return super.updatePinsConfiguration();\n    }\n    async process() {\n        this.setStatus('running');\n        const value = this.getProperty('value');\n        this.setPinValue('value_output', value);\n        this.setStatus('success');\n    }\n    onPropertyChanged(key, value) {\n        if (key === 'value' || key === 'outputRange') {\n            this.process();\n        }\n        super.onPropertyChanged(key, value);\n    }\n    static renderProperties({ node, onUpdateNode }) {\n        const properties = node.properties;\n        const handlePropertyChange = (key, value)=>{\n            onUpdateNode(node.id, {\n                properties: {\n                    ...node.properties,\n                    [key]: value\n                }\n            });\n        };\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-4\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                        children: \"输出范围\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/nodes/controls/KnobNode.tsx\",\n                        lineNumber: 64,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                        value: properties.outputRange || 'percentage',\n                        onChange: (e)=>handlePropertyChange('outputRange', e.target.value),\n                        className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                value: \"percentage\",\n                                children: \"百分比 (0-100)\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/nodes/controls/KnobNode.tsx\",\n                                lineNumber: 72,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                value: \"0-255\",\n                                children: \"0-255\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/nodes/controls/KnobNode.tsx\",\n                                lineNumber: 73,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                value: \"0-1\",\n                                children: \"0-1\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/nodes/controls/KnobNode.tsx\",\n                                lineNumber: 74,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                value: \"boolean\",\n                                children: \"布尔值\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/nodes/controls/KnobNode.tsx\",\n                                lineNumber: 75,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/nodes/controls/KnobNode.tsx\",\n                        lineNumber: 67,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/nodes/controls/KnobNode.tsx\",\n                lineNumber: 63,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/nodes/controls/KnobNode.tsx\",\n            lineNumber: 62,\n            columnNumber: 7\n        }, this);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/nodes/controls/KnobNode.tsx\n");

/***/ })

};
;