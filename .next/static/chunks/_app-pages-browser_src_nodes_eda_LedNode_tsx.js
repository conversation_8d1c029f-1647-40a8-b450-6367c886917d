"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_src_nodes_eda_LedNode_tsx"],{

/***/ "(app-pages-browser)/./src/base/BaseNode.ts":
/*!******************************!*\
  !*** ./src/base/BaseNode.ts ***!
  \******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BaseNode: () => (/* binding */ BaseNode)\n/* harmony export */ });\nclass BaseNode {\n    // --- Static method for property panel rendering ---\n    static renderProperties(props) {\n        return null; // Default implementation\n    }\n    // --- Core Methods ---\n    generateId() {\n        return \"node_\".concat(Date.now(), \"_\").concat(Math.random().toString(36).substr(2, 9));\n    }\n    getData() {\n        return {\n            ...this.data\n        };\n    }\n    updateData(updates) {\n        this.data = {\n            ...this.data,\n            ...updates\n        };\n    }\n    setStatus(status) {\n        this.data.status = status;\n    }\n    addPin(pin) {\n        this.data.pins.push(pin);\n    }\n    removePin(pinId) {\n        this.data.pins = this.data.pins.filter((pin)=>pin.id !== pinId);\n    }\n    getPin(pinId) {\n        return this.data.pins.find((pin)=>pin.id === pinId);\n    }\n    setPinValue(pinId, value) {\n        const pin = this.getPin(pinId);\n        if (pin) {\n            pin.value = value;\n            this.onPinValueChanged(pinId, value);\n        }\n    }\n    getPinValue(pinId) {\n        const pin = this.getPin(pinId);\n        return pin === null || pin === void 0 ? void 0 : pin.value;\n    }\n    getInputValue(pinId) {\n        const pin = this.getPin(pinId);\n        if (!pin || pin.type !== 'input') {\n            return undefined;\n        }\n        if (pin.connected) {\n            return pin.value;\n        }\n        var _pin_defaultValue;\n        return (_pin_defaultValue = pin.defaultValue) !== null && _pin_defaultValue !== void 0 ? _pin_defaultValue : pin.value;\n    }\n    setProperty(key, value) {\n        this.data.properties[key] = value;\n        this.onPropertyChanged(key, value);\n    }\n    getProperty(key) {\n        return this.data.properties[key];\n    }\n    handleEvent(event) {\n        switch(event.type){\n            case 'click':\n                this.onClick(event);\n                break;\n            case 'doubleClick':\n                this.onDoubleClick(event);\n                break;\n            case 'dragStart':\n                this.onDragStart(event);\n                break;\n            case 'drag':\n                this.onDrag(event);\n                break;\n            case 'dragEnd':\n                this.onDragEnd(event);\n                break;\n            case 'pinConnect':\n                this.onPinConnect(event);\n                break;\n            case 'pinDisconnect':\n                this.onPinDisconnect(event);\n                break;\n            case 'propertyChange':\n                if (event.data) {\n                    this.onPropertyChanged(event.data.key, event.data.value);\n                }\n                break;\n            case 'testRun':\n                this.process();\n                break;\n        }\n    }\n    // --- Event Handlers (for overriding) ---\n    onClick(event) {}\n    onDoubleClick(event) {}\n    onDragStart(event) {\n        this.data.dragging = true;\n    }\n    onDrag(event) {\n        if (event.data && this.data.dragging) {\n            this.data.position = event.data.position;\n        }\n    }\n    onDragEnd(event) {\n        this.data.dragging = false;\n    }\n    onPinConnect(event) {}\n    onPinDisconnect(event) {}\n    onPropertyChanged(key, value) {\n        this.updatePinsConfiguration();\n    }\n    onPinValueChanged(pinId, value) {\n        const pin = this.getPin(pinId);\n        if (pin && pin.type === 'input') {\n            this.process();\n        }\n    }\n    updatePinsConfiguration() {\n        return this.updateNodeSize();\n    }\n    updateNodeSize() {\n        const inputPins = this.data.pins.filter((pin)=>pin.type === 'input');\n        const outputPins = this.data.pins.filter((pin)=>pin.type === 'output');\n        const maxPins = Math.max(inputPins.length, outputPins.length);\n        const titleHeight = 37;\n        const minContentHeight = 40;\n        const pinPadding = 20;\n        const pinSlotHeight = 28;\n        let contentHeight = minContentHeight;\n        if (maxPins > 0) {\n            const requiredPinsHeight = maxPins * pinSlotHeight + pinPadding;\n            contentHeight = Math.max(minContentHeight, requiredPinsHeight);\n        }\n        // If the node has fixed size, don't update it automatically\n        if (this.data.size.width && this.data.size.height && !this.data.properties.dynamicSize) {\n            // Special case for fixed-size nodes that might still need to adjust if pins overflow\n            const requiredHeight = titleHeight + contentHeight;\n            if (this.data.size.height < requiredHeight) {\n                this.data.size.height = requiredHeight;\n                return true;\n            }\n            return false;\n        }\n        const newHeight = titleHeight + contentHeight;\n        if (this.data.size.height !== newHeight) {\n            this.data.size.height = newHeight;\n            return true;\n        }\n        return false;\n    }\n    constructor(data){\n        this.data = {\n            id: data.id || this.generateId(),\n            type: data.type,\n            name: data.name || 'Base Node',\n            icon: data.icon || '❓',\n            color: data.color || 'gray',\n            description: data.description || '',\n            position: data.position || {\n                x: 0,\n                y: 0\n            },\n            size: data.size || {\n                width: 200,\n                height: 100\n            },\n            status: data.status || 'idle',\n            pins: data.pins || [],\n            properties: data.properties || {},\n            selected: data.selected || false,\n            dragging: data.dragging || false\n        };\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/base/BaseNode.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/nodes/eda/LedNode.tsx":
/*!***********************************!*\
  !*** ./src/nodes/eda/LedNode.tsx ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LedNode: () => (/* binding */ LedNode)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _base_BaseNode__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../base/BaseNode */ \"(app-pages-browser)/./src/base/BaseNode.ts\");\n\n\n\nclass LedNode extends _base_BaseNode__WEBPACK_IMPORTED_MODULE_2__.BaseNode {\n    initializePins() {\n        this.updatePinsConfiguration();\n    }\n    updatePinsConfiguration() {\n        this.data.pins = [];\n        const { visiblePins, inputRange } = this.getProperties();\n        const dataType = inputRange === 'boolean' ? 'boolean' : 'number';\n        if (visiblePins.r) this.addPin({\n            id: 'red_input',\n            name: 'R',\n            type: 'input',\n            dataType,\n            value: 0,\n            defaultValue: 128\n        });\n        if (visiblePins.g) this.addPin({\n            id: 'green_input',\n            name: 'G',\n            type: 'input',\n            dataType,\n            value: 0,\n            defaultValue: 128\n        });\n        if (visiblePins.b) this.addPin({\n            id: 'blue_input',\n            name: 'B',\n            type: 'input',\n            dataType,\n            value: 0,\n            defaultValue: 128\n        });\n        this.addPin({\n            id: 'brightness_input',\n            name: 'Brightness',\n            type: 'input',\n            dataType: 'number',\n            value: 100,\n            defaultValue: 100\n        });\n        this.addPin({\n            id: 'color_output',\n            name: 'Color',\n            type: 'output',\n            dataType: 'color',\n            value: '#000000'\n        });\n        return super.updatePinsConfiguration();\n    }\n    async process() {\n        try {\n            this.setStatus('running');\n            const { inputRange } = this.getProperties();\n            const brightness = this.getInputValue('brightness_input');\n            const red = this.normalizeInputValue(this.getInputValue('red_input') || 0, inputRange);\n            const green = this.normalizeInputValue(this.getInputValue('green_input') || 0, inputRange);\n            const blue = this.normalizeInputValue(this.getInputValue('blue_input') || 0, inputRange);\n            const brightnessMultiplier = Math.max(0, Math.min(100, brightness)) / 100;\n            const finalRed = Math.round(red * brightnessMultiplier);\n            const finalGreen = Math.round(green * brightnessMultiplier);\n            const finalBlue = Math.round(blue * brightnessMultiplier);\n            this.setProperty('redValue', finalRed);\n            this.setProperty('greenValue', finalGreen);\n            this.setProperty('blueValue', finalBlue);\n            const colorHex = \"#\".concat(finalRed.toString(16).padStart(2, '0')).concat(finalGreen.toString(16).padStart(2, '0')).concat(finalBlue.toString(16).padStart(2, '0'));\n            this.setPinValue('color_output', colorHex);\n            this.setStatus('success');\n        } catch (error) {\n            console.error('LED Node processing error:', error);\n            this.setStatus('error');\n        }\n    }\n    normalizeInputValue(value, inputRange) {\n        if (typeof value === 'boolean') return value ? 255 : 0;\n        const numValue = Number(value) || 0;\n        switch(inputRange){\n            case 'percentage':\n                return Math.round(Math.max(0, Math.min(100, numValue)) * 2.55);\n            case '0-255':\n                return Math.round(Math.max(0, Math.min(255, numValue)));\n            case '0-1':\n                return Math.round(Math.max(0, Math.min(1, numValue)) * 255);\n            case 'boolean':\n                return numValue > 0 ? 255 : 0;\n            default:\n                return Math.round(Math.max(0, Math.min(255, numValue)));\n        }\n    }\n    getProperties() {\n        return this.data.properties;\n    }\n    // --- 属性面板渲染逻辑 ---\n    static renderProperties(param) {\n        let { node, onUpdateNode } = param;\n        var _properties_visiblePins, _properties_visiblePins1, _properties_visiblePins2;\n        const properties = node.properties;\n        const handlePropertyChange = (key, value)=>{\n            onUpdateNode(node.id, {\n                properties: {\n                    ...node.properties,\n                    [key]: value\n                }\n            });\n        };\n        const handlePinVisibilityChange = (pin, isVisible)=>{\n            const currentVisibility = properties.visiblePins || {\n                r: true,\n                g: true,\n                b: true\n            };\n            const visibleCount = Object.values(currentVisibility).filter((v)=>v).length;\n            if (visibleCount === 1 && !isVisible) {\n                alert(\"至少需要一个可见的颜色引脚。\");\n                return;\n            }\n            const newVisibility = {\n                ...currentVisibility,\n                [pin]: isVisible\n            };\n            handlePropertyChange('visiblePins', newVisibility);\n        };\n        var _properties_visiblePins_r, _properties_visiblePins_g, _properties_visiblePins_b;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                            children: \"可见引脚\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/nodes/eda/LedNode.tsx\",\n                            lineNumber: 119,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"checkbox\",\n                                            checked: (_properties_visiblePins_r = (_properties_visiblePins = properties.visiblePins) === null || _properties_visiblePins === void 0 ? void 0 : _properties_visiblePins.r) !== null && _properties_visiblePins_r !== void 0 ? _properties_visiblePins_r : true,\n                                            onChange: (e)=>handlePinVisibilityChange('r', e.target.checked),\n                                            className: \"h-4 w-4 rounded border-gray-300 text-red-600 focus:ring-red-500\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/nodes/eda/LedNode.tsx\",\n                                            lineNumber: 122,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"ml-2 text-sm text-red-600\",\n                                            children: \"R\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/nodes/eda/LedNode.tsx\",\n                                            lineNumber: 123,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/nodes/eda/LedNode.tsx\",\n                                    lineNumber: 121,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"checkbox\",\n                                            checked: (_properties_visiblePins_g = (_properties_visiblePins1 = properties.visiblePins) === null || _properties_visiblePins1 === void 0 ? void 0 : _properties_visiblePins1.g) !== null && _properties_visiblePins_g !== void 0 ? _properties_visiblePins_g : true,\n                                            onChange: (e)=>handlePinVisibilityChange('g', e.target.checked),\n                                            className: \"h-4 w-4 rounded border-gray-300 text-green-600 focus:ring-green-500\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/nodes/eda/LedNode.tsx\",\n                                            lineNumber: 126,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"ml-2 text-sm text-green-600\",\n                                            children: \"G\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/nodes/eda/LedNode.tsx\",\n                                            lineNumber: 127,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/nodes/eda/LedNode.tsx\",\n                                    lineNumber: 125,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"checkbox\",\n                                            checked: (_properties_visiblePins_b = (_properties_visiblePins2 = properties.visiblePins) === null || _properties_visiblePins2 === void 0 ? void 0 : _properties_visiblePins2.b) !== null && _properties_visiblePins_b !== void 0 ? _properties_visiblePins_b : true,\n                                            onChange: (e)=>handlePinVisibilityChange('b', e.target.checked),\n                                            className: \"h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/nodes/eda/LedNode.tsx\",\n                                            lineNumber: 130,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"ml-2 text-sm text-blue-600\",\n                                            children: \"B\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/nodes/eda/LedNode.tsx\",\n                                            lineNumber: 131,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/nodes/eda/LedNode.tsx\",\n                                    lineNumber: 129,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/nodes/eda/LedNode.tsx\",\n                            lineNumber: 120,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/nodes/eda/LedNode.tsx\",\n                    lineNumber: 118,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                            children: \"输入范围\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/nodes/eda/LedNode.tsx\",\n                            lineNumber: 136,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                            value: properties.inputRange || 'percentage',\n                            onChange: (e)=>handlePropertyChange('inputRange', e.target.value),\n                            className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"percentage\",\n                                    children: \"百分比 (0-100)\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/nodes/eda/LedNode.tsx\",\n                                    lineNumber: 138,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"0-255\",\n                                    children: \"0-255\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/nodes/eda/LedNode.tsx\",\n                                    lineNumber: 139,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"0-1\",\n                                    children: \"0-1\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/nodes/eda/LedNode.tsx\",\n                                    lineNumber: 140,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"boolean\",\n                                    children: \"布尔值\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/nodes/eda/LedNode.tsx\",\n                                    lineNumber: 141,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/nodes/eda/LedNode.tsx\",\n                            lineNumber: 137,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/nodes/eda/LedNode.tsx\",\n                    lineNumber: 135,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/nodes/eda/LedNode.tsx\",\n            lineNumber: 117,\n            columnNumber: 7\n        }, this);\n    }\n    constructor(data){\n        super({\n            type: 'led',\n            name: 'RGB LED',\n            icon: '💡',\n            color: 'yellow',\n            description: '3色变色LED灯节点',\n            size: {\n                width: 140,\n                height: 100\n            },\n            properties: {\n                visiblePins: {\n                    r: true,\n                    g: true,\n                    b: true\n                },\n                inputRange: 'percentage',\n                redValue: 0,\n                greenValue: 0,\n                blueValue: 0,\n                brightness: 100\n            },\n            ...data\n        });\n        this.initializePins();\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/nodes/eda/LedNode.tsx\n"));

/***/ })

}]);