"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_src_nodes_eda_LedNode_tsx"],{

/***/ "(app-pages-browser)/./src/base/BaseNode.ts":
/*!******************************!*\
  !*** ./src/base/BaseNode.ts ***!
  \******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BaseNode: () => (/* binding */ BaseNode)\n/* harmony export */ });\nclass BaseNode {\n    // --- Static method for property panel rendering ---\n    static renderProperties(props) {\n        return null; // Default implementation\n    }\n    // --- Core Methods ---\n    generateId() {\n        return \"node_\".concat(Date.now(), \"_\").concat(Math.random().toString(36).substr(2, 9));\n    }\n    getData() {\n        return {\n            ...this.data\n        };\n    }\n    updateData(updates) {\n        this.data = {\n            ...this.data,\n            ...updates\n        };\n    }\n    setStatus(status) {\n        this.data.status = status;\n    }\n    addPin(pin) {\n        this.data.pins.push(pin);\n    }\n    removePin(pinId) {\n        this.data.pins = this.data.pins.filter((pin)=>pin.id !== pinId);\n    }\n    getPin(pinId) {\n        return this.data.pins.find((pin)=>pin.id === pinId);\n    }\n    setPinValue(pinId, value) {\n        const pin = this.getPin(pinId);\n        if (pin) {\n            pin.value = value;\n            this.onPinValueChanged(pinId, value);\n        }\n    }\n    getPinValue(pinId) {\n        const pin = this.getPin(pinId);\n        return pin === null || pin === void 0 ? void 0 : pin.value;\n    }\n    getInputValue(pinId) {\n        const pin = this.getPin(pinId);\n        if (!pin || pin.type !== 'input') {\n            return undefined;\n        }\n        if (pin.connected) {\n            return pin.value;\n        }\n        var _pin_defaultValue;\n        return (_pin_defaultValue = pin.defaultValue) !== null && _pin_defaultValue !== void 0 ? _pin_defaultValue : pin.value;\n    }\n    setProperty(key, value) {\n        this.data.properties[key] = value;\n        this.onPropertyChanged(key, value);\n    }\n    getProperty(key) {\n        return this.data.properties[key];\n    }\n    handleEvent(event) {\n        switch(event.type){\n            case 'click':\n                this.onClick(event);\n                break;\n            case 'doubleClick':\n                this.onDoubleClick(event);\n                break;\n            case 'dragStart':\n                this.onDragStart(event);\n                break;\n            case 'drag':\n                this.onDrag(event);\n                break;\n            case 'dragEnd':\n                this.onDragEnd(event);\n                break;\n            case 'pinConnect':\n                this.onPinConnect(event);\n                break;\n            case 'pinDisconnect':\n                this.onPinDisconnect(event);\n                break;\n            case 'propertyChange':\n                if (event.data) {\n                    this.onPropertyChanged(event.data.key, event.data.value);\n                }\n                break;\n            case 'testRun':\n                this.process();\n                break;\n        }\n    }\n    // --- Event Handlers (for overriding) ---\n    onClick(event) {}\n    onDoubleClick(event) {}\n    onDragStart(event) {\n        this.data.dragging = true;\n    }\n    onDrag(event) {\n        if (event.data && this.data.dragging) {\n            this.data.position = event.data.position;\n        }\n    }\n    onDragEnd(event) {\n        this.data.dragging = false;\n    }\n    onPinConnect(event) {}\n    onPinDisconnect(event) {}\n    onPropertyChanged(key, value) {\n        this.updatePinsConfiguration();\n    }\n    onPinValueChanged(pinId, value) {\n        const pin = this.getPin(pinId);\n        if (pin && pin.type === 'input') {\n            this.process();\n        }\n    }\n    updatePinsConfiguration() {\n        return this.updateNodeSize();\n    }\n    updateNodeSize() {\n        const inputPins = this.data.pins.filter((pin)=>pin.type === 'input');\n        const outputPins = this.data.pins.filter((pin)=>pin.type === 'output');\n        const maxPins = Math.max(inputPins.length, outputPins.length);\n        const titleHeight = 37;\n        const minContentHeight = 40;\n        const pinPadding = 20;\n        const pinSlotHeight = 28;\n        let contentHeight = minContentHeight;\n        if (maxPins > 0) {\n            const requiredPinsHeight = maxPins * pinSlotHeight + pinPadding;\n            contentHeight = Math.max(minContentHeight, requiredPinsHeight);\n        }\n        // For nodes with dynamic sizing enabled or when pins change, always update height\n        // Only skip auto-sizing if explicitly disabled AND no pin configuration changes\n        if (this.data.size.width && this.data.size.height && this.data.properties.dynamicSize === false) {\n            // Even for fixed-size nodes, allow adjustment if pins overflow or underflow\n            const requiredHeight = titleHeight + contentHeight;\n            if (this.data.size.height !== requiredHeight) {\n                this.data.size.height = requiredHeight;\n                return true;\n            }\n            return false;\n        }\n        const newHeight = titleHeight + contentHeight;\n        if (this.data.size.height !== newHeight) {\n            console.log(\"节点 \".concat(this.data.name, \" 高度变化: \").concat(this.data.size.height, \" -> \").concat(newHeight, \" (引脚数: \").concat(maxPins, \")\"));\n            this.data.size.height = newHeight;\n            return true;\n        }\n        return false;\n    }\n    constructor(data){\n        this.data = {\n            id: data.id || this.generateId(),\n            type: data.type,\n            name: data.name || 'Base Node',\n            icon: data.icon || '❓',\n            color: data.color || 'gray',\n            description: data.description || '',\n            position: data.position || {\n                x: 0,\n                y: 0\n            },\n            size: data.size || {\n                width: 200,\n                height: 100\n            },\n            status: data.status || 'idle',\n            pins: data.pins || [],\n            properties: data.properties || {},\n            selected: data.selected || false,\n            dragging: data.dragging || false\n        };\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/base/BaseNode.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/nodes/eda/LedNode.tsx":
/*!***********************************!*\
  !*** ./src/nodes/eda/LedNode.tsx ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LedNode: () => (/* binding */ LedNode)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _base_BaseNode__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../base/BaseNode */ \"(app-pages-browser)/./src/base/BaseNode.ts\");\n\n\n\nclass LedNode extends _base_BaseNode__WEBPACK_IMPORTED_MODULE_2__.BaseNode {\n    initializePins() {\n        this.updatePinsConfiguration();\n    }\n    updatePinsConfiguration() {\n        this.data.pins = [];\n        const { visiblePins, inputRange } = this.getProperties();\n        const dataType = inputRange === 'boolean' ? 'boolean' : 'number';\n        if (visiblePins.r) this.addPin({\n            id: 'red_input',\n            name: 'R',\n            type: 'input',\n            dataType,\n            value: 0,\n            defaultValue: 128\n        });\n        if (visiblePins.g) this.addPin({\n            id: 'green_input',\n            name: 'G',\n            type: 'input',\n            dataType,\n            value: 0,\n            defaultValue: 128\n        });\n        if (visiblePins.b) this.addPin({\n            id: 'blue_input',\n            name: 'B',\n            type: 'input',\n            dataType,\n            value: 0,\n            defaultValue: 128\n        });\n        this.addPin({\n            id: 'brightness_input',\n            name: 'Brightness',\n            type: 'input',\n            dataType: 'number',\n            value: 100,\n            defaultValue: 100\n        });\n        this.addPin({\n            id: 'color_output',\n            name: 'Color',\n            type: 'output',\n            dataType: 'color',\n            value: '#000000'\n        });\n        return super.updatePinsConfiguration();\n    }\n    async process() {\n        try {\n            this.setStatus('running');\n            const { inputRange } = this.getProperties();\n            const brightness = this.getInputValue('brightness_input');\n            const red = this.normalizeInputValue(this.getInputValue('red_input') || 0, inputRange);\n            const green = this.normalizeInputValue(this.getInputValue('green_input') || 0, inputRange);\n            const blue = this.normalizeInputValue(this.getInputValue('blue_input') || 0, inputRange);\n            const brightnessMultiplier = Math.max(0, Math.min(100, brightness)) / 100;\n            const finalRed = Math.round(red * brightnessMultiplier);\n            const finalGreen = Math.round(green * brightnessMultiplier);\n            const finalBlue = Math.round(blue * brightnessMultiplier);\n            this.setProperty('redValue', finalRed);\n            this.setProperty('greenValue', finalGreen);\n            this.setProperty('blueValue', finalBlue);\n            const colorHex = \"#\".concat(finalRed.toString(16).padStart(2, '0')).concat(finalGreen.toString(16).padStart(2, '0')).concat(finalBlue.toString(16).padStart(2, '0'));\n            this.setPinValue('color_output', colorHex);\n            this.setStatus('success');\n        } catch (error) {\n            console.error('LED Node processing error:', error);\n            this.setStatus('error');\n        }\n    }\n    normalizeInputValue(value, inputRange) {\n        if (typeof value === 'boolean') return value ? 255 : 0;\n        const numValue = Number(value) || 0;\n        switch(inputRange){\n            case 'percentage':\n                return Math.round(Math.max(0, Math.min(100, numValue)) * 2.55);\n            case '0-255':\n                return Math.round(Math.max(0, Math.min(255, numValue)));\n            case '0-1':\n                return Math.round(Math.max(0, Math.min(1, numValue)) * 255);\n            case 'boolean':\n                return numValue > 0 ? 255 : 0;\n            default:\n                return Math.round(Math.max(0, Math.min(255, numValue)));\n        }\n    }\n    getProperties() {\n        return this.data.properties;\n    }\n    // --- 属性面板渲染逻辑 ---\n    static renderProperties(param) {\n        let { node, onUpdateNode } = param;\n        var _properties_visiblePins, _properties_visiblePins1, _properties_visiblePins2;\n        const properties = node.properties;\n        const handlePropertyChange = (key, value)=>{\n            onUpdateNode(node.id, {\n                properties: {\n                    ...node.properties,\n                    [key]: value\n                }\n            });\n        };\n        const handlePinVisibilityChange = (pin, isVisible)=>{\n            const currentVisibility = properties.visiblePins || {\n                r: true,\n                g: true,\n                b: true\n            };\n            const visibleCount = Object.values(currentVisibility).filter((v)=>v).length;\n            if (visibleCount === 1 && !isVisible) {\n                alert(\"至少需要一个可见的颜色引脚。\");\n                return;\n            }\n            const newVisibility = {\n                ...currentVisibility,\n                [pin]: isVisible\n            };\n            handlePropertyChange('visiblePins', newVisibility);\n        };\n        var _properties_visiblePins_r, _properties_visiblePins_g, _properties_visiblePins_b;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                            children: \"可见引脚\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/nodes/eda/LedNode.tsx\",\n                            lineNumber: 120,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"checkbox\",\n                                            checked: (_properties_visiblePins_r = (_properties_visiblePins = properties.visiblePins) === null || _properties_visiblePins === void 0 ? void 0 : _properties_visiblePins.r) !== null && _properties_visiblePins_r !== void 0 ? _properties_visiblePins_r : true,\n                                            onChange: (e)=>handlePinVisibilityChange('r', e.target.checked),\n                                            className: \"h-4 w-4 rounded border-gray-300 text-red-600 focus:ring-red-500\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/nodes/eda/LedNode.tsx\",\n                                            lineNumber: 123,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"ml-2 text-sm text-red-600\",\n                                            children: \"R\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/nodes/eda/LedNode.tsx\",\n                                            lineNumber: 124,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/nodes/eda/LedNode.tsx\",\n                                    lineNumber: 122,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"checkbox\",\n                                            checked: (_properties_visiblePins_g = (_properties_visiblePins1 = properties.visiblePins) === null || _properties_visiblePins1 === void 0 ? void 0 : _properties_visiblePins1.g) !== null && _properties_visiblePins_g !== void 0 ? _properties_visiblePins_g : true,\n                                            onChange: (e)=>handlePinVisibilityChange('g', e.target.checked),\n                                            className: \"h-4 w-4 rounded border-gray-300 text-green-600 focus:ring-green-500\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/nodes/eda/LedNode.tsx\",\n                                            lineNumber: 127,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"ml-2 text-sm text-green-600\",\n                                            children: \"G\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/nodes/eda/LedNode.tsx\",\n                                            lineNumber: 128,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/nodes/eda/LedNode.tsx\",\n                                    lineNumber: 126,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"checkbox\",\n                                            checked: (_properties_visiblePins_b = (_properties_visiblePins2 = properties.visiblePins) === null || _properties_visiblePins2 === void 0 ? void 0 : _properties_visiblePins2.b) !== null && _properties_visiblePins_b !== void 0 ? _properties_visiblePins_b : true,\n                                            onChange: (e)=>handlePinVisibilityChange('b', e.target.checked),\n                                            className: \"h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/nodes/eda/LedNode.tsx\",\n                                            lineNumber: 131,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"ml-2 text-sm text-blue-600\",\n                                            children: \"B\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/nodes/eda/LedNode.tsx\",\n                                            lineNumber: 132,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/nodes/eda/LedNode.tsx\",\n                                    lineNumber: 130,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/nodes/eda/LedNode.tsx\",\n                            lineNumber: 121,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/nodes/eda/LedNode.tsx\",\n                    lineNumber: 119,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                            children: \"输入范围\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/nodes/eda/LedNode.tsx\",\n                            lineNumber: 137,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                            value: properties.inputRange || 'percentage',\n                            onChange: (e)=>handlePropertyChange('inputRange', e.target.value),\n                            className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"percentage\",\n                                    children: \"百分比 (0-100)\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/nodes/eda/LedNode.tsx\",\n                                    lineNumber: 139,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"0-255\",\n                                    children: \"0-255\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/nodes/eda/LedNode.tsx\",\n                                    lineNumber: 140,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"0-1\",\n                                    children: \"0-1\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/nodes/eda/LedNode.tsx\",\n                                    lineNumber: 141,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"boolean\",\n                                    children: \"布尔值\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/nodes/eda/LedNode.tsx\",\n                                    lineNumber: 142,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/nodes/eda/LedNode.tsx\",\n                            lineNumber: 138,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/nodes/eda/LedNode.tsx\",\n                    lineNumber: 136,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/nodes/eda/LedNode.tsx\",\n            lineNumber: 118,\n            columnNumber: 7\n        }, this);\n    }\n    constructor(data){\n        super({\n            type: 'led',\n            name: 'RGB LED',\n            icon: '💡',\n            color: 'yellow',\n            description: '3色变色LED灯节点',\n            size: {\n                width: 140,\n                height: 100\n            },\n            properties: {\n                visiblePins: {\n                    r: true,\n                    g: true,\n                    b: true\n                },\n                inputRange: 'percentage',\n                redValue: 0,\n                greenValue: 0,\n                blueValue: 0,\n                brightness: 100,\n                dynamicSize: true\n            },\n            ...data\n        });\n        this.initializePins();\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/nodes/eda/LedNode.tsx\n"));

/***/ })

}]);