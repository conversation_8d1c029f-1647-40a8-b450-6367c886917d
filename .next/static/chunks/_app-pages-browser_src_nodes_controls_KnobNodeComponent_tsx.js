"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_src_nodes_controls_KnobNodeComponent_tsx"],{

/***/ "(app-pages-browser)/./src/base/BaseNodeComponent.tsx":
/*!****************************************!*\
  !*** ./src/base/BaseNodeComponent.tsx ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BaseNodeComponent: () => (/* binding */ BaseNodeComponent)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ BaseNodeComponent auto */ \nvar _s = $RefreshSig$();\n\nconst BaseNodeComponent = (param)=>{\n    let { node, onEvent, onUpdateNode, children } = param;\n    _s();\n    const [isDragging, setIsDragging] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const nodeRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const animationFrameRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const pendingUpdateRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // 使用ref来存储拖拽起始信息，避免不必要的重渲染\n    const dragInfoRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // 计算引脚位置\n    const calculatePinPositions = ()=>{\n        const inputPins = node.pins.filter((pin)=>pin.type === 'input');\n        const outputPins = node.pins.filter((pin)=>pin.type === 'output');\n        const maxPins = Math.max(inputPins.length, outputPins.length);\n        // 标题栏高度\n        const titleHeight = 37;\n        // 引脚区域的起始Y坐标（相对于内容区）\n        const pinStartY = 14; // (pinSlotHeight / 2)\n        // 每个引脚的固定垂直间距\n        const pinSpacing = 28;\n        return {\n            inputPins,\n            outputPins,\n            maxPins,\n            titleHeight,\n            pinStartY,\n            pinSpacing\n        };\n    };\n    const { inputPins, outputPins, maxPins, titleHeight, pinStartY, pinSpacing } = calculatePinPositions();\n    // 使用requestAnimationFrame节流更新\n    const flushPendingUpdate = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"BaseNodeComponent.useCallback[flushPendingUpdate]\": ()=>{\n            if (pendingUpdateRef.current) {\n                onUpdateNode(node.id, {\n                    position: pendingUpdateRef.current\n                });\n                pendingUpdateRef.current = null;\n            }\n            animationFrameRef.current = null;\n        }\n    }[\"BaseNodeComponent.useCallback[flushPendingUpdate]\"], [\n        onUpdateNode,\n        node.id\n    ]);\n    const handleMouseMove = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"BaseNodeComponent.useCallback[handleMouseMove]\": (e)=>{\n            // 拖拽逻辑不应依赖isDragging state，而是依赖ref，以避免陈旧闭包问题\n            if (!dragInfoRef.current) return;\n            const deltaX = e.clientX - dragInfoRef.current.startX;\n            const deltaY = e.clientY - dragInfoRef.current.startY;\n            const newPosition = {\n                x: dragInfoRef.current.startNodeX + deltaX,\n                y: dragInfoRef.current.startNodeY + deltaY\n            };\n            // 恢复直接DOM操作以实现流畅视觉效果\n            if (nodeRef.current) {\n                nodeRef.current.style.left = \"\".concat(newPosition.x, \"px\");\n                nodeRef.current.style.top = \"\".concat(newPosition.y, \"px\");\n            }\n            // 节流状态更新\n            pendingUpdateRef.current = newPosition;\n            if (animationFrameRef.current === null) {\n                animationFrameRef.current = requestAnimationFrame(flushPendingUpdate);\n            }\n        }\n    }[\"BaseNodeComponent.useCallback[handleMouseMove]\"], [\n        flushPendingUpdate\n    ]);\n    const handleMouseUp = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"BaseNodeComponent.useCallback[handleMouseUp]\": ()=>{\n            setIsDragging(false);\n            dragInfoRef.current = null; // 清理拖拽信息\n            // 清理动画帧\n            if (animationFrameRef.current) {\n                cancelAnimationFrame(animationFrameRef.current);\n                animationFrameRef.current = null;\n            }\n            // 确保最后的位置更新被应用\n            if (pendingUpdateRef.current) {\n                onUpdateNode(node.id, {\n                    position: pendingUpdateRef.current\n                });\n                pendingUpdateRef.current = null;\n            }\n            onEvent({\n                type: 'dragEnd',\n                nodeId: node.id\n            });\n            // 移除全局鼠标事件监听\n            document.removeEventListener('mousemove', handleMouseMove);\n            document.removeEventListener('mouseup', handleMouseUp);\n        }\n    }[\"BaseNodeComponent.useCallback[handleMouseUp]\"], [\n        handleMouseMove,\n        onEvent,\n        onUpdateNode,\n        node.id\n    ]);\n    const handleMouseDown = (e)=>{\n        if (e.button !== 0) return; // 只处理左键\n        // 防止在引脚上开始拖拽\n        const target = e.target;\n        if (target.closest('.pin-connector')) {\n            return;\n        }\n        e.preventDefault();\n        e.stopPropagation();\n        // 记录拖拽起始信息\n        dragInfoRef.current = {\n            startX: e.clientX,\n            startY: e.clientY,\n            startNodeX: node.position.x,\n            startNodeY: node.position.y\n        };\n        setIsDragging(true);\n        onEvent({\n            type: 'dragStart',\n            nodeId: node.id,\n            data: {\n                position: node.position\n            }\n        });\n        // 添加全局鼠标事件监听\n        document.addEventListener('mousemove', handleMouseMove);\n        document.addEventListener('mouseup', handleMouseUp);\n    };\n    const handleClick = (e)=>{\n        e.stopPropagation();\n        onEvent({\n            type: 'click',\n            nodeId: node.id\n        });\n    };\n    const handleDoubleClick = (e)=>{\n        e.stopPropagation();\n        onEvent({\n            type: 'doubleClick',\n            nodeId: node.id\n        });\n    };\n    const getStatusColor = ()=>{\n        switch(node.status){\n            case 'running':\n                return 'border-yellow-400 bg-yellow-50';\n            case 'success':\n                return 'border-green-400 bg-green-50';\n            case 'error':\n                return 'border-red-400 bg-red-50';\n            case 'warning':\n                return 'border-orange-400 bg-orange-50';\n            default:\n                return 'border-gray-300 bg-white';\n        }\n    };\n    const getTitleBarColorClasses = function() {\n        let color = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 'gray';\n        const colorMap = {\n            gray: 'bg-gray-100 border-gray-200',\n            red: 'bg-red-100 border-red-200',\n            yellow: 'bg-yellow-100 border-yellow-200',\n            green: 'bg-green-100 border-green-200',\n            blue: 'bg-blue-100 border-blue-200',\n            purple: 'bg-purple-100 border-purple-200'\n        };\n        return colorMap[color] || colorMap.gray;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: nodeRef,\n        className: \"absolute border-2 rounded-lg shadow-md cursor-move select-none \".concat(getStatusColor(), \" \").concat(node.selected ? 'ring-2 ring-blue-400' : '', \" \").concat(isDragging ? 'shadow-lg' : ''),\n        style: {\n            left: node.position.x,\n            top: node.position.y,\n            width: node.size.width,\n            height: node.size.height\n        },\n        onMouseDown: handleMouseDown,\n        onClick: handleClick,\n        onDoubleClick: handleDoubleClick,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between px-2 py-2 border-b rounded-t-md \".concat(getTitleBarColorClasses(node.color)),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2 flex-1 min-w-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-lg\",\n                                children: node.icon\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/base/BaseNodeComponent.tsx\",\n                                lineNumber: 209,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm font-medium text-gray-800 truncate\",\n                                children: node.name\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/base/BaseNodeComponent.tsx\",\n                                lineNumber: 210,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/base/BaseNodeComponent.tsx\",\n                        lineNumber: 208,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: (e)=>{\n                            e.stopPropagation();\n                            onEvent({\n                                type: 'testRun',\n                                nodeId: node.id\n                            });\n                        },\n                        className: \"p-1 rounded-full text-gray-400 hover:bg-gray-200 hover:text-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-300\",\n                        title: \"测试运行节点\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            className: \"w-4 h-4\",\n                            fill: \"currentColor\",\n                            viewBox: \"0 0 20 20\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                fillRule: \"evenodd\",\n                                d: \"M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z\",\n                                clipRule: \"evenodd\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/base/BaseNodeComponent.tsx\",\n                                lineNumber: 223,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/base/BaseNodeComponent.tsx\",\n                            lineNumber: 222,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/base/BaseNodeComponent.tsx\",\n                        lineNumber: 214,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/base/BaseNodeComponent.tsx\",\n                lineNumber: 207,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-2 flex-1\",\n                children: children\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/base/BaseNodeComponent.tsx\",\n                lineNumber: 229,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute left-0\",\n                style: {\n                    top: titleHeight\n                },\n                children: inputPins.map((pin, index)=>{\n                    const pinY = pinStartY + index * pinSpacing;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute flex items-center\",\n                        style: {\n                            top: pinY,\n                            left: -6,\n                            transform: 'translateY(-50%)' // 垂直居中对齐\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"pin-connector w-3 h-3 rounded-full border-2 cursor-pointer transition-colors \".concat(pin.connected ? 'bg-blue-500 border-blue-600' : 'bg-white border-gray-400 hover:border-blue-400'),\n                                title: pin.name\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/base/BaseNodeComponent.tsx\",\n                                lineNumber: 247,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"absolute left-4 top-1/2 transform -translate-y-1/2 text-xs text-gray-600 whitespace-nowrap\",\n                                children: pin.name\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/base/BaseNodeComponent.tsx\",\n                                lineNumber: 255,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, pin.id, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/base/BaseNodeComponent.tsx\",\n                        lineNumber: 238,\n                        columnNumber: 13\n                    }, undefined);\n                })\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/base/BaseNodeComponent.tsx\",\n                lineNumber: 234,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute right-0\",\n                style: {\n                    top: titleHeight\n                },\n                children: outputPins.map((pin, index)=>{\n                    const pinY = pinStartY + index * pinSpacing;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute flex items-center justify-end\",\n                        style: {\n                            top: pinY,\n                            right: -6,\n                            transform: 'translateY(-50%)' // 垂直居中对齐\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"absolute right-4 top-1/2 transform -translate-y-1/2 text-xs text-gray-600 whitespace-nowrap\",\n                                children: pin.name\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/base/BaseNodeComponent.tsx\",\n                                lineNumber: 277,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"pin-connector w-3 h-3 rounded-full border-2 cursor-pointer transition-colors \".concat(pin.connected ? 'bg-green-500 border-green-600' : 'bg-white border-gray-400 hover:border-green-400'),\n                                title: pin.name\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/base/BaseNodeComponent.tsx\",\n                                lineNumber: 280,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, pin.id, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/base/BaseNodeComponent.tsx\",\n                        lineNumber: 268,\n                        columnNumber: 13\n                    }, undefined);\n                })\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/base/BaseNodeComponent.tsx\",\n                lineNumber: 264,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/base/BaseNodeComponent.tsx\",\n        lineNumber: 189,\n        columnNumber: 5\n    }, undefined);\n};\n_s(BaseNodeComponent, \"4xQWUKAA1b3ze7N46qzTbZBIPZk=\");\n_c = BaseNodeComponent;\nvar _c;\n$RefreshReg$(_c, \"BaseNodeComponent\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9iYXNlL0Jhc2VOb2RlQ29tcG9uZW50LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFFNkQ7QUFPdEQsTUFBTUksb0JBQXNEO1FBQUMsRUFDbEVDLElBQUksRUFDSkMsT0FBTyxFQUNQQyxZQUFZLEVBQ1pDLFFBQVEsRUFDVDs7SUFDQyxNQUFNLENBQUNDLFlBQVlDLGNBQWMsR0FBR1QsK0NBQVFBLENBQUM7SUFDN0MsTUFBTVUsVUFBVVQsNkNBQU1BLENBQWlCO0lBQ3ZDLE1BQU1VLG9CQUFvQlYsNkNBQU1BLENBQWdCO0lBQ2hELE1BQU1XLG1CQUFtQlgsNkNBQU1BLENBQWtDO0lBQ2pFLDJCQUEyQjtJQUMzQixNQUFNWSxjQUFjWiw2Q0FBTUEsQ0FLaEI7SUFFVixTQUFTO0lBQ1QsTUFBTWEsd0JBQXdCO1FBQzVCLE1BQU1DLFlBQVlYLEtBQUtZLElBQUksQ0FBQ0MsTUFBTSxDQUFDQyxDQUFBQSxNQUFPQSxJQUFJQyxJQUFJLEtBQUs7UUFDdkQsTUFBTUMsYUFBYWhCLEtBQUtZLElBQUksQ0FBQ0MsTUFBTSxDQUFDQyxDQUFBQSxNQUFPQSxJQUFJQyxJQUFJLEtBQUs7UUFDeEQsTUFBTUUsVUFBVUMsS0FBS0MsR0FBRyxDQUFDUixVQUFVUyxNQUFNLEVBQUVKLFdBQVdJLE1BQU07UUFFNUQsUUFBUTtRQUNSLE1BQU1DLGNBQWM7UUFDcEIscUJBQXFCO1FBQ3JCLE1BQU1DLFlBQVksSUFBSSxzQkFBc0I7UUFDNUMsY0FBYztRQUNkLE1BQU1DLGFBQWE7UUFFbkIsT0FBTztZQUNMWjtZQUNBSztZQUNBQztZQUNBSTtZQUNBQztZQUNBQztRQUNGO0lBQ0Y7SUFFQSxNQUFNLEVBQUVaLFNBQVMsRUFBRUssVUFBVSxFQUFFQyxPQUFPLEVBQUVJLFdBQVcsRUFBRUMsU0FBUyxFQUFFQyxVQUFVLEVBQUUsR0FBR2I7SUFFL0UsOEJBQThCO0lBQzlCLE1BQU1jLHFCQUFxQjFCLGtEQUFXQTs2REFBQztZQUNyQyxJQUFJVSxpQkFBaUJpQixPQUFPLEVBQUU7Z0JBQzVCdkIsYUFBYUYsS0FBSzBCLEVBQUUsRUFBRTtvQkFBRUMsVUFBVW5CLGlCQUFpQmlCLE9BQU87Z0JBQUM7Z0JBQzNEakIsaUJBQWlCaUIsT0FBTyxHQUFHO1lBQzdCO1lBQ0FsQixrQkFBa0JrQixPQUFPLEdBQUc7UUFDOUI7NERBQUc7UUFBQ3ZCO1FBQWNGLEtBQUswQixFQUFFO0tBQUM7SUFFMUIsTUFBTUUsa0JBQWtCOUIsa0RBQVdBOzBEQUFDLENBQUMrQjtZQUNuQyw2Q0FBNkM7WUFDN0MsSUFBSSxDQUFDcEIsWUFBWWdCLE9BQU8sRUFBRTtZQUUxQixNQUFNSyxTQUFTRCxFQUFFRSxPQUFPLEdBQUd0QixZQUFZZ0IsT0FBTyxDQUFDTyxNQUFNO1lBQ3JELE1BQU1DLFNBQVNKLEVBQUVLLE9BQU8sR0FBR3pCLFlBQVlnQixPQUFPLENBQUNVLE1BQU07WUFFckQsTUFBTUMsY0FBYztnQkFDbEJDLEdBQUc1QixZQUFZZ0IsT0FBTyxDQUFDYSxVQUFVLEdBQUdSO2dCQUNwQ1MsR0FBRzlCLFlBQVlnQixPQUFPLENBQUNlLFVBQVUsR0FBR1A7WUFDdEM7WUFFQSxxQkFBcUI7WUFDckIsSUFBSTNCLFFBQVFtQixPQUFPLEVBQUU7Z0JBQ25CbkIsUUFBUW1CLE9BQU8sQ0FBQ2dCLEtBQUssQ0FBQ0MsSUFBSSxHQUFHLEdBQWlCLE9BQWROLFlBQVlDLENBQUMsRUFBQztnQkFDOUMvQixRQUFRbUIsT0FBTyxDQUFDZ0IsS0FBSyxDQUFDRSxHQUFHLEdBQUcsR0FBaUIsT0FBZFAsWUFBWUcsQ0FBQyxFQUFDO1lBQy9DO1lBRUEsU0FBUztZQUNUL0IsaUJBQWlCaUIsT0FBTyxHQUFHVztZQUMzQixJQUFJN0Isa0JBQWtCa0IsT0FBTyxLQUFLLE1BQU07Z0JBQ3RDbEIsa0JBQWtCa0IsT0FBTyxHQUFHbUIsc0JBQXNCcEI7WUFDcEQ7UUFDRjt5REFBRztRQUFDQTtLQUFtQjtJQUV2QixNQUFNcUIsZ0JBQWdCL0Msa0RBQVdBO3dEQUFDO1lBQ2hDTyxjQUFjO1lBQ2RJLFlBQVlnQixPQUFPLEdBQUcsTUFBTSxTQUFTO1lBRXJDLFFBQVE7WUFDUixJQUFJbEIsa0JBQWtCa0IsT0FBTyxFQUFFO2dCQUM3QnFCLHFCQUFxQnZDLGtCQUFrQmtCLE9BQU87Z0JBQzlDbEIsa0JBQWtCa0IsT0FBTyxHQUFHO1lBQzlCO1lBRUEsZUFBZTtZQUNmLElBQUlqQixpQkFBaUJpQixPQUFPLEVBQUU7Z0JBQzVCdkIsYUFBYUYsS0FBSzBCLEVBQUUsRUFBRTtvQkFBRUMsVUFBVW5CLGlCQUFpQmlCLE9BQU87Z0JBQUM7Z0JBQzNEakIsaUJBQWlCaUIsT0FBTyxHQUFHO1lBQzdCO1lBRUF4QixRQUFRO2dCQUNOYyxNQUFNO2dCQUNOZ0MsUUFBUS9DLEtBQUswQixFQUFFO1lBQ2pCO1lBRUEsYUFBYTtZQUNic0IsU0FBU0MsbUJBQW1CLENBQUMsYUFBYXJCO1lBQzFDb0IsU0FBU0MsbUJBQW1CLENBQUMsV0FBV0o7UUFDMUM7dURBQUc7UUFBQ2pCO1FBQWlCM0I7UUFBU0M7UUFBY0YsS0FBSzBCLEVBQUU7S0FBQztJQUVwRCxNQUFNd0Isa0JBQWtCLENBQUNyQjtRQUN2QixJQUFJQSxFQUFFc0IsTUFBTSxLQUFLLEdBQUcsUUFBUSxRQUFRO1FBRXBDLGFBQWE7UUFDYixNQUFNQyxTQUFTdkIsRUFBRXVCLE1BQU07UUFDdkIsSUFBSUEsT0FBT0MsT0FBTyxDQUFDLG1CQUFtQjtZQUNwQztRQUNGO1FBRUF4QixFQUFFeUIsY0FBYztRQUNoQnpCLEVBQUUwQixlQUFlO1FBRWpCLFdBQVc7UUFDWDlDLFlBQVlnQixPQUFPLEdBQUc7WUFDcEJPLFFBQVFILEVBQUVFLE9BQU87WUFDakJJLFFBQVFOLEVBQUVLLE9BQU87WUFDakJJLFlBQVl0QyxLQUFLMkIsUUFBUSxDQUFDVSxDQUFDO1lBQzNCRyxZQUFZeEMsS0FBSzJCLFFBQVEsQ0FBQ1ksQ0FBQztRQUM3QjtRQUNBbEMsY0FBYztRQUVkSixRQUFRO1lBQ05jLE1BQU07WUFDTmdDLFFBQVEvQyxLQUFLMEIsRUFBRTtZQUNmOEIsTUFBTTtnQkFBRTdCLFVBQVUzQixLQUFLMkIsUUFBUTtZQUFDO1FBQ2xDO1FBRUEsYUFBYTtRQUNicUIsU0FBU1MsZ0JBQWdCLENBQUMsYUFBYTdCO1FBQ3ZDb0IsU0FBU1MsZ0JBQWdCLENBQUMsV0FBV1o7SUFDdkM7SUFFQSxNQUFNYSxjQUFjLENBQUM3QjtRQUNuQkEsRUFBRTBCLGVBQWU7UUFDakJ0RCxRQUFRO1lBQ05jLE1BQU07WUFDTmdDLFFBQVEvQyxLQUFLMEIsRUFBRTtRQUNqQjtJQUNGO0lBRUEsTUFBTWlDLG9CQUFvQixDQUFDOUI7UUFDekJBLEVBQUUwQixlQUFlO1FBQ2pCdEQsUUFBUTtZQUNOYyxNQUFNO1lBQ05nQyxRQUFRL0MsS0FBSzBCLEVBQUU7UUFDakI7SUFDRjtJQUVBLE1BQU1rQyxpQkFBaUI7UUFDckIsT0FBUTVELEtBQUs2RCxNQUFNO1lBQ2pCLEtBQUs7Z0JBQ0gsT0FBTztZQUNULEtBQUs7Z0JBQ0gsT0FBTztZQUNULEtBQUs7Z0JBQ0gsT0FBTztZQUNULEtBQUs7Z0JBQ0gsT0FBTztZQUNUO2dCQUNFLE9BQU87UUFDWDtJQUNGO0lBRUEsTUFBTUMsMEJBQTBCO1lBQUNDLHlFQUFnQjtRQUMvQyxNQUFNQyxXQUFtQztZQUN2Q0MsTUFBTTtZQUNOQyxLQUFLO1lBQ0xDLFFBQVE7WUFDUkMsT0FBTztZQUNQQyxNQUFNO1lBQ05DLFFBQVE7UUFDVjtRQUNBLE9BQU9OLFFBQVEsQ0FBQ0QsTUFBTSxJQUFJQyxTQUFTQyxJQUFJO0lBQ3pDO0lBRUEscUJBQ0UsOERBQUNNO1FBQ0NDLEtBQUtsRTtRQUNMbUUsV0FBVyxrRUFFUHpFLE9BREY0RCxrQkFDRCxLQUNDeEQsT0FERUosS0FBSzBFLFFBQVEsR0FBRyx5QkFBeUIsSUFBRyxLQUUvQyxPQURDdEUsYUFBYSxjQUFjO1FBRTdCcUMsT0FBTztZQUNMQyxNQUFNMUMsS0FBSzJCLFFBQVEsQ0FBQ1UsQ0FBQztZQUNyQk0sS0FBSzNDLEtBQUsyQixRQUFRLENBQUNZLENBQUM7WUFDcEJvQyxPQUFPM0UsS0FBSzRFLElBQUksQ0FBQ0QsS0FBSztZQUN0QkUsUUFBUTdFLEtBQUs0RSxJQUFJLENBQUNDLE1BQU07UUFDMUI7UUFDQUMsYUFBYTVCO1FBQ2I2QixTQUFTckI7UUFDVHNCLGVBQWVyQjs7MEJBR2YsOERBQUNZO2dCQUFJRSxXQUFXLHFFQUF5RyxPQUFwQ1gsd0JBQXdCOUQsS0FBSytELEtBQUs7O2tDQUNySCw4REFBQ1E7d0JBQUlFLFdBQVU7OzBDQUNiLDhEQUFDUTtnQ0FBS1IsV0FBVTswQ0FBV3pFLEtBQUtrRixJQUFJOzs7Ozs7MENBQ3BDLDhEQUFDWDtnQ0FBSUUsV0FBVTswQ0FDWnpFLEtBQUttRixJQUFJOzs7Ozs7Ozs7Ozs7a0NBR2QsOERBQUNoQzt3QkFDQzRCLFNBQVMsQ0FBQ2xEOzRCQUNSQSxFQUFFMEIsZUFBZTs0QkFDakJ0RCxRQUFRO2dDQUFFYyxNQUFNO2dDQUFXZ0MsUUFBUS9DLEtBQUswQixFQUFFOzRCQUFDO3dCQUM3Qzt3QkFDQStDLFdBQVU7d0JBQ1ZXLE9BQU07a0NBRU4sNEVBQUNDOzRCQUFJWixXQUFVOzRCQUFVYSxNQUFLOzRCQUFlQyxTQUFRO3NDQUNuRCw0RUFBQ0M7Z0NBQUtDLFVBQVM7Z0NBQVVDLEdBQUU7Z0NBQTBHQyxVQUFTOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQU1wSiw4REFBQ3BCO2dCQUFJRSxXQUFVOzBCQUNadEU7Ozs7OzswQkFJSCw4REFBQ29FO2dCQUFJRSxXQUFVO2dCQUFrQmhDLE9BQU87b0JBQUVFLEtBQUt0QjtnQkFBWTswQkFDeERWLFVBQVVpRixHQUFHLENBQUMsQ0FBQzlFLEtBQUsrRTtvQkFDbkIsTUFBTUMsT0FBT3hFLFlBQWF1RSxRQUFRdEU7b0JBQ2xDLHFCQUNFLDhEQUFDZ0Q7d0JBRUNFLFdBQVU7d0JBQ1ZoQyxPQUFPOzRCQUNMRSxLQUFLbUQ7NEJBQ0xwRCxNQUFNLENBQUM7NEJBQ1BxRCxXQUFXLG1CQUFtQixTQUFTO3dCQUN6Qzs7MENBRUEsOERBQUN4QjtnQ0FDQ0UsV0FBVyxnRkFJVixPQUhDM0QsSUFBSWtGLFNBQVMsR0FDVCxnQ0FDQTtnQ0FFTlosT0FBT3RFLElBQUlxRSxJQUFJOzs7Ozs7MENBRWpCLDhEQUFDRjtnQ0FBS1IsV0FBVTswQ0FDYjNELElBQUlxRSxJQUFJOzs7Ozs7O3VCQWpCTnJFLElBQUlZLEVBQUU7Ozs7O2dCQXFCakI7Ozs7OzswQkFJRiw4REFBQzZDO2dCQUFJRSxXQUFVO2dCQUFtQmhDLE9BQU87b0JBQUVFLEtBQUt0QjtnQkFBWTswQkFDekRMLFdBQVc0RSxHQUFHLENBQUMsQ0FBQzlFLEtBQUsrRTtvQkFDcEIsTUFBTUMsT0FBT3hFLFlBQWF1RSxRQUFRdEU7b0JBQ2xDLHFCQUNFLDhEQUFDZ0Q7d0JBRUNFLFdBQVU7d0JBQ1ZoQyxPQUFPOzRCQUNMRSxLQUFLbUQ7NEJBQ0xHLE9BQU8sQ0FBQzs0QkFDUkYsV0FBVyxtQkFBbUIsU0FBUzt3QkFDekM7OzBDQUVBLDhEQUFDZDtnQ0FBS1IsV0FBVTswQ0FDYjNELElBQUlxRSxJQUFJOzs7Ozs7MENBRVgsOERBQUNaO2dDQUNDRSxXQUFXLGdGQUlWLE9BSEMzRCxJQUFJa0YsU0FBUyxHQUNULGtDQUNBO2dDQUVOWixPQUFPdEUsSUFBSXFFLElBQUk7Ozs7Ozs7dUJBakJackUsSUFBSVksRUFBRTs7Ozs7Z0JBcUJqQjs7Ozs7Ozs7Ozs7O0FBSVIsRUFBRTtHQTVSVzNCO0tBQUFBIiwic291cmNlcyI6WyIvVXNlcnMvdXNlci9Eb2N1bWVudHMvYXVnbWVudC1wcm9qZWN0cy9uZXh0anMtc3VwZXJOb2RlL3NyYy9iYXNlL0Jhc2VOb2RlQ29tcG9uZW50LnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XG5cbmltcG9ydCBSZWFjdCwgeyB1c2VTdGF0ZSwgdXNlUmVmLCB1c2VDYWxsYmFjayB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IE5vZGVDb21wb25lbnRQcm9wcywgTm9kZUV2ZW50IH0gZnJvbSAnLi9ub2RlJztcblxuZXhwb3J0IGludGVyZmFjZSBCYXNlTm9kZUNvbXBvbmVudFByb3BzIGV4dGVuZHMgTm9kZUNvbXBvbmVudFByb3BzIHtcbiAgY2hpbGRyZW4/OiBSZWFjdC5SZWFjdE5vZGU7XG59XG5cbmV4cG9ydCBjb25zdCBCYXNlTm9kZUNvbXBvbmVudDogUmVhY3QuRkM8QmFzZU5vZGVDb21wb25lbnRQcm9wcz4gPSAoe1xuICBub2RlLFxuICBvbkV2ZW50LFxuICBvblVwZGF0ZU5vZGUsXG4gIGNoaWxkcmVuLFxufSkgPT4ge1xuICBjb25zdCBbaXNEcmFnZ2luZywgc2V0SXNEcmFnZ2luZ10gPSB1c2VTdGF0ZShmYWxzZSk7XG4gIGNvbnN0IG5vZGVSZWYgPSB1c2VSZWY8SFRNTERpdkVsZW1lbnQ+KG51bGwpO1xuICBjb25zdCBhbmltYXRpb25GcmFtZVJlZiA9IHVzZVJlZjxudW1iZXIgfCBudWxsPihudWxsKTtcbiAgY29uc3QgcGVuZGluZ1VwZGF0ZVJlZiA9IHVzZVJlZjx7IHg6IG51bWJlcjsgeTogbnVtYmVyIH0gfCBudWxsPihudWxsKTtcbiAgLy8g5L2/55SocmVm5p2l5a2Y5YKo5ouW5ou96LW35aeL5L+h5oGv77yM6YG/5YWN5LiN5b+F6KaB55qE6YeN5riy5p+TXG4gIGNvbnN0IGRyYWdJbmZvUmVmID0gdXNlUmVmPHtcbiAgICBzdGFydFg6IG51bWJlcjtcbiAgICBzdGFydFk6IG51bWJlcjtcbiAgICBzdGFydE5vZGVYOiBudW1iZXI7XG4gICAgc3RhcnROb2RlWTogbnVtYmVyO1xuICB9IHwgbnVsbD4obnVsbCk7XG5cbiAgLy8g6K6h566X5byV6ISa5L2N572uXG4gIGNvbnN0IGNhbGN1bGF0ZVBpblBvc2l0aW9ucyA9ICgpID0+IHtcbiAgICBjb25zdCBpbnB1dFBpbnMgPSBub2RlLnBpbnMuZmlsdGVyKHBpbiA9PiBwaW4udHlwZSA9PT0gJ2lucHV0Jyk7XG4gICAgY29uc3Qgb3V0cHV0UGlucyA9IG5vZGUucGlucy5maWx0ZXIocGluID0+IHBpbi50eXBlID09PSAnb3V0cHV0Jyk7XG4gICAgY29uc3QgbWF4UGlucyA9IE1hdGgubWF4KGlucHV0UGlucy5sZW5ndGgsIG91dHB1dFBpbnMubGVuZ3RoKTtcblxuICAgIC8vIOagh+mimOagj+mrmOW6plxuICAgIGNvbnN0IHRpdGxlSGVpZ2h0ID0gMzc7XG4gICAgLy8g5byV6ISa5Yy65Z+f55qE6LW35aeLWeWdkOagh++8iOebuOWvueS6juWGheWuueWMuu+8iVxuICAgIGNvbnN0IHBpblN0YXJ0WSA9IDE0OyAvLyAocGluU2xvdEhlaWdodCAvIDIpXG4gICAgLy8g5q+P5Liq5byV6ISa55qE5Zu65a6a5Z6C55u06Ze06LedXG4gICAgY29uc3QgcGluU3BhY2luZyA9IDI4O1xuXG4gICAgcmV0dXJuIHtcbiAgICAgIGlucHV0UGlucyxcbiAgICAgIG91dHB1dFBpbnMsXG4gICAgICBtYXhQaW5zLFxuICAgICAgdGl0bGVIZWlnaHQsXG4gICAgICBwaW5TdGFydFksXG4gICAgICBwaW5TcGFjaW5nLFxuICAgIH07XG4gIH07XG5cbiAgY29uc3QgeyBpbnB1dFBpbnMsIG91dHB1dFBpbnMsIG1heFBpbnMsIHRpdGxlSGVpZ2h0LCBwaW5TdGFydFksIHBpblNwYWNpbmcgfSA9IGNhbGN1bGF0ZVBpblBvc2l0aW9ucygpO1xuXG4gIC8vIOS9v+eUqHJlcXVlc3RBbmltYXRpb25GcmFtZeiKgua1geabtOaWsFxuICBjb25zdCBmbHVzaFBlbmRpbmdVcGRhdGUgPSB1c2VDYWxsYmFjaygoKSA9PiB7XG4gICAgaWYgKHBlbmRpbmdVcGRhdGVSZWYuY3VycmVudCkge1xuICAgICAgb25VcGRhdGVOb2RlKG5vZGUuaWQsIHsgcG9zaXRpb246IHBlbmRpbmdVcGRhdGVSZWYuY3VycmVudCB9KTtcbiAgICAgIHBlbmRpbmdVcGRhdGVSZWYuY3VycmVudCA9IG51bGw7XG4gICAgfVxuICAgIGFuaW1hdGlvbkZyYW1lUmVmLmN1cnJlbnQgPSBudWxsO1xuICB9LCBbb25VcGRhdGVOb2RlLCBub2RlLmlkXSk7XG5cbiAgY29uc3QgaGFuZGxlTW91c2VNb3ZlID0gdXNlQ2FsbGJhY2soKGU6IE1vdXNlRXZlbnQpID0+IHtcbiAgICAvLyDmi5bmi73pgLvovpHkuI3lupTkvp3otZZpc0RyYWdnaW5nIHN0YXRl77yM6ICM5piv5L6d6LWWcmVm77yM5Lul6YG/5YWN6ZmI5pen6Zet5YyF6Zeu6aKYXG4gICAgaWYgKCFkcmFnSW5mb1JlZi5jdXJyZW50KSByZXR1cm47XG5cbiAgICBjb25zdCBkZWx0YVggPSBlLmNsaWVudFggLSBkcmFnSW5mb1JlZi5jdXJyZW50LnN0YXJ0WDtcbiAgICBjb25zdCBkZWx0YVkgPSBlLmNsaWVudFkgLSBkcmFnSW5mb1JlZi5jdXJyZW50LnN0YXJ0WTtcblxuICAgIGNvbnN0IG5ld1Bvc2l0aW9uID0ge1xuICAgICAgeDogZHJhZ0luZm9SZWYuY3VycmVudC5zdGFydE5vZGVYICsgZGVsdGFYLFxuICAgICAgeTogZHJhZ0luZm9SZWYuY3VycmVudC5zdGFydE5vZGVZICsgZGVsdGFZLFxuICAgIH07XG5cbiAgICAvLyDmgaLlpI3nm7TmjqVET03mk43kvZzku6Xlrp7njrDmtYHnlYXop4bop4nmlYjmnpxcbiAgICBpZiAobm9kZVJlZi5jdXJyZW50KSB7XG4gICAgICBub2RlUmVmLmN1cnJlbnQuc3R5bGUubGVmdCA9IGAke25ld1Bvc2l0aW9uLnh9cHhgO1xuICAgICAgbm9kZVJlZi5jdXJyZW50LnN0eWxlLnRvcCA9IGAke25ld1Bvc2l0aW9uLnl9cHhgO1xuICAgIH1cblxuICAgIC8vIOiKgua1geeKtuaAgeabtOaWsFxuICAgIHBlbmRpbmdVcGRhdGVSZWYuY3VycmVudCA9IG5ld1Bvc2l0aW9uO1xuICAgIGlmIChhbmltYXRpb25GcmFtZVJlZi5jdXJyZW50ID09PSBudWxsKSB7XG4gICAgICBhbmltYXRpb25GcmFtZVJlZi5jdXJyZW50ID0gcmVxdWVzdEFuaW1hdGlvbkZyYW1lKGZsdXNoUGVuZGluZ1VwZGF0ZSk7XG4gICAgfVxuICB9LCBbZmx1c2hQZW5kaW5nVXBkYXRlXSk7XG5cbiAgY29uc3QgaGFuZGxlTW91c2VVcCA9IHVzZUNhbGxiYWNrKCgpID0+IHtcbiAgICBzZXRJc0RyYWdnaW5nKGZhbHNlKTtcbiAgICBkcmFnSW5mb1JlZi5jdXJyZW50ID0gbnVsbDsgLy8g5riF55CG5ouW5ou95L+h5oGvXG5cbiAgICAvLyDmuIXnkIbliqjnlLvluKdcbiAgICBpZiAoYW5pbWF0aW9uRnJhbWVSZWYuY3VycmVudCkge1xuICAgICAgY2FuY2VsQW5pbWF0aW9uRnJhbWUoYW5pbWF0aW9uRnJhbWVSZWYuY3VycmVudCk7XG4gICAgICBhbmltYXRpb25GcmFtZVJlZi5jdXJyZW50ID0gbnVsbDtcbiAgICB9XG5cbiAgICAvLyDnoa7kv53mnIDlkI7nmoTkvY3nva7mm7TmlrDooqvlupTnlKhcbiAgICBpZiAocGVuZGluZ1VwZGF0ZVJlZi5jdXJyZW50KSB7XG4gICAgICBvblVwZGF0ZU5vZGUobm9kZS5pZCwgeyBwb3NpdGlvbjogcGVuZGluZ1VwZGF0ZVJlZi5jdXJyZW50IH0pO1xuICAgICAgcGVuZGluZ1VwZGF0ZVJlZi5jdXJyZW50ID0gbnVsbDtcbiAgICB9XG5cbiAgICBvbkV2ZW50KHtcbiAgICAgIHR5cGU6ICdkcmFnRW5kJyxcbiAgICAgIG5vZGVJZDogbm9kZS5pZCxcbiAgICB9KTtcblxuICAgIC8vIOenu+mZpOWFqOWxgOm8oOagh+S6i+S7tuebkeWQrFxuICAgIGRvY3VtZW50LnJlbW92ZUV2ZW50TGlzdGVuZXIoJ21vdXNlbW92ZScsIGhhbmRsZU1vdXNlTW92ZSk7XG4gICAgZG9jdW1lbnQucmVtb3ZlRXZlbnRMaXN0ZW5lcignbW91c2V1cCcsIGhhbmRsZU1vdXNlVXApO1xuICB9LCBbaGFuZGxlTW91c2VNb3ZlLCBvbkV2ZW50LCBvblVwZGF0ZU5vZGUsIG5vZGUuaWRdKTtcblxuICBjb25zdCBoYW5kbGVNb3VzZURvd24gPSAoZTogUmVhY3QuTW91c2VFdmVudCkgPT4ge1xuICAgIGlmIChlLmJ1dHRvbiAhPT0gMCkgcmV0dXJuOyAvLyDlj6rlpITnkIblt6bplK5cblxuICAgIC8vIOmYsuatouWcqOW8leiEmuS4iuW8gOWni+aLluaLvVxuICAgIGNvbnN0IHRhcmdldCA9IGUudGFyZ2V0IGFzIEhUTUxFbGVtZW50O1xuICAgIGlmICh0YXJnZXQuY2xvc2VzdCgnLnBpbi1jb25uZWN0b3InKSkge1xuICAgICAgcmV0dXJuO1xuICAgIH1cblxuICAgIGUucHJldmVudERlZmF1bHQoKTtcbiAgICBlLnN0b3BQcm9wYWdhdGlvbigpO1xuXG4gICAgLy8g6K6w5b2V5ouW5ou96LW35aeL5L+h5oGvXG4gICAgZHJhZ0luZm9SZWYuY3VycmVudCA9IHtcbiAgICAgIHN0YXJ0WDogZS5jbGllbnRYLFxuICAgICAgc3RhcnRZOiBlLmNsaWVudFksXG4gICAgICBzdGFydE5vZGVYOiBub2RlLnBvc2l0aW9uLngsXG4gICAgICBzdGFydE5vZGVZOiBub2RlLnBvc2l0aW9uLnksXG4gICAgfTtcbiAgICBzZXRJc0RyYWdnaW5nKHRydWUpO1xuXG4gICAgb25FdmVudCh7XG4gICAgICB0eXBlOiAnZHJhZ1N0YXJ0JyxcbiAgICAgIG5vZGVJZDogbm9kZS5pZCxcbiAgICAgIGRhdGE6IHsgcG9zaXRpb246IG5vZGUucG9zaXRpb24gfSxcbiAgICB9KTtcblxuICAgIC8vIOa3u+WKoOWFqOWxgOm8oOagh+S6i+S7tuebkeWQrFxuICAgIGRvY3VtZW50LmFkZEV2ZW50TGlzdGVuZXIoJ21vdXNlbW92ZScsIGhhbmRsZU1vdXNlTW92ZSk7XG4gICAgZG9jdW1lbnQuYWRkRXZlbnRMaXN0ZW5lcignbW91c2V1cCcsIGhhbmRsZU1vdXNlVXApO1xuICB9O1xuXG4gIGNvbnN0IGhhbmRsZUNsaWNrID0gKGU6IFJlYWN0Lk1vdXNlRXZlbnQpID0+IHtcbiAgICBlLnN0b3BQcm9wYWdhdGlvbigpO1xuICAgIG9uRXZlbnQoe1xuICAgICAgdHlwZTogJ2NsaWNrJyxcbiAgICAgIG5vZGVJZDogbm9kZS5pZCxcbiAgICB9KTtcbiAgfTtcblxuICBjb25zdCBoYW5kbGVEb3VibGVDbGljayA9IChlOiBSZWFjdC5Nb3VzZUV2ZW50KSA9PiB7XG4gICAgZS5zdG9wUHJvcGFnYXRpb24oKTtcbiAgICBvbkV2ZW50KHtcbiAgICAgIHR5cGU6ICdkb3VibGVDbGljaycsXG4gICAgICBub2RlSWQ6IG5vZGUuaWQsXG4gICAgfSk7XG4gIH07XG5cbiAgY29uc3QgZ2V0U3RhdHVzQ29sb3IgPSAoKSA9PiB7XG4gICAgc3dpdGNoIChub2RlLnN0YXR1cykge1xuICAgICAgY2FzZSAncnVubmluZyc6XG4gICAgICAgIHJldHVybiAnYm9yZGVyLXllbGxvdy00MDAgYmcteWVsbG93LTUwJztcbiAgICAgIGNhc2UgJ3N1Y2Nlc3MnOlxuICAgICAgICByZXR1cm4gJ2JvcmRlci1ncmVlbi00MDAgYmctZ3JlZW4tNTAnO1xuICAgICAgY2FzZSAnZXJyb3InOlxuICAgICAgICByZXR1cm4gJ2JvcmRlci1yZWQtNDAwIGJnLXJlZC01MCc7XG4gICAgICBjYXNlICd3YXJuaW5nJzpcbiAgICAgICAgcmV0dXJuICdib3JkZXItb3JhbmdlLTQwMCBiZy1vcmFuZ2UtNTAnO1xuICAgICAgZGVmYXVsdDpcbiAgICAgICAgcmV0dXJuICdib3JkZXItZ3JheS0zMDAgYmctd2hpdGUnO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCBnZXRUaXRsZUJhckNvbG9yQ2xhc3NlcyA9IChjb2xvcjogc3RyaW5nID0gJ2dyYXknKSA9PiB7XG4gICAgY29uc3QgY29sb3JNYXA6IFJlY29yZDxzdHJpbmcsIHN0cmluZz4gPSB7XG4gICAgICBncmF5OiAnYmctZ3JheS0xMDAgYm9yZGVyLWdyYXktMjAwJyxcbiAgICAgIHJlZDogJ2JnLXJlZC0xMDAgYm9yZGVyLXJlZC0yMDAnLFxuICAgICAgeWVsbG93OiAnYmcteWVsbG93LTEwMCBib3JkZXIteWVsbG93LTIwMCcsXG4gICAgICBncmVlbjogJ2JnLWdyZWVuLTEwMCBib3JkZXItZ3JlZW4tMjAwJyxcbiAgICAgIGJsdWU6ICdiZy1ibHVlLTEwMCBib3JkZXItYmx1ZS0yMDAnLFxuICAgICAgcHVycGxlOiAnYmctcHVycGxlLTEwMCBib3JkZXItcHVycGxlLTIwMCcsXG4gICAgfTtcbiAgICByZXR1cm4gY29sb3JNYXBbY29sb3JdIHx8IGNvbG9yTWFwLmdyYXk7XG4gIH1cblxuICByZXR1cm4gKFxuICAgIDxkaXZcbiAgICAgIHJlZj17bm9kZVJlZn1cbiAgICAgIGNsYXNzTmFtZT17YGFic29sdXRlIGJvcmRlci0yIHJvdW5kZWQtbGcgc2hhZG93LW1kIGN1cnNvci1tb3ZlIHNlbGVjdC1ub25lICR7XG4gICAgICAgIGdldFN0YXR1c0NvbG9yKClcbiAgICAgIH0gJHtub2RlLnNlbGVjdGVkID8gJ3JpbmctMiByaW5nLWJsdWUtNDAwJyA6ICcnfSAke1xuICAgICAgICBpc0RyYWdnaW5nID8gJ3NoYWRvdy1sZycgOiAnJ1xuICAgICAgfWB9XG4gICAgICBzdHlsZT17e1xuICAgICAgICBsZWZ0OiBub2RlLnBvc2l0aW9uLngsXG4gICAgICAgIHRvcDogbm9kZS5wb3NpdGlvbi55LFxuICAgICAgICB3aWR0aDogbm9kZS5zaXplLndpZHRoLFxuICAgICAgICBoZWlnaHQ6IG5vZGUuc2l6ZS5oZWlnaHQsXG4gICAgICB9fVxuICAgICAgb25Nb3VzZURvd249e2hhbmRsZU1vdXNlRG93bn1cbiAgICAgIG9uQ2xpY2s9e2hhbmRsZUNsaWNrfVxuICAgICAgb25Eb3VibGVDbGljaz17aGFuZGxlRG91YmxlQ2xpY2t9XG4gICAgPlxuICAgICAgey8qIOiKgueCueagh+mimOagjyAqL31cbiAgICAgIDxkaXYgY2xhc3NOYW1lPXtgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIHB4LTIgcHktMiBib3JkZXItYiByb3VuZGVkLXQtbWQgJHtnZXRUaXRsZUJhckNvbG9yQ2xhc3Nlcyhub2RlLmNvbG9yKX1gfT5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMiBmbGV4LTEgbWluLXctMFwiPlxuICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtbGdcIj57bm9kZS5pY29ufTwvc3Bhbj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTgwMCB0cnVuY2F0ZVwiPlxuICAgICAgICAgICAge25vZGUubmFtZX1cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICAgIDxidXR0b25cbiAgICAgICAgICBvbkNsaWNrPXsoZSkgPT4ge1xuICAgICAgICAgICAgZS5zdG9wUHJvcGFnYXRpb24oKTtcbiAgICAgICAgICAgIG9uRXZlbnQoeyB0eXBlOiAndGVzdFJ1bicsIG5vZGVJZDogbm9kZS5pZCB9KTtcbiAgICAgICAgICB9fVxuICAgICAgICAgIGNsYXNzTmFtZT1cInAtMSByb3VuZGVkLWZ1bGwgdGV4dC1ncmF5LTQwMCBob3ZlcjpiZy1ncmF5LTIwMCBob3Zlcjp0ZXh0LWdyYXktNjAwIGZvY3VzOm91dGxpbmUtbm9uZSBmb2N1czpyaW5nLTIgZm9jdXM6cmluZy1ncmF5LTMwMFwiXG4gICAgICAgICAgdGl0bGU9XCLmtYvor5Xov5DooYzoioLngrlcIlxuICAgICAgICA+XG4gICAgICAgICAgPHN2ZyBjbGFzc05hbWU9XCJ3LTQgaC00XCIgZmlsbD1cImN1cnJlbnRDb2xvclwiIHZpZXdCb3g9XCIwIDAgMjAgMjBcIj5cbiAgICAgICAgICAgIDxwYXRoIGZpbGxSdWxlPVwiZXZlbm9kZFwiIGQ9XCJNMTAgMThhOCA4IDAgMTAwLTE2IDggOCAwIDAwMCAxNnpNOS41NTUgNy4xNjhBMSAxIDAgMDA4IDh2NGExIDEgMCAwMDEuNTU1LjgzMmwzLTJhMSAxIDAgMDAwLTEuNjY0bC0zLTJ6XCIgY2xpcFJ1bGU9XCJldmVub2RkXCIgLz5cbiAgICAgICAgICA8L3N2Zz5cbiAgICAgICAgPC9idXR0b24+XG4gICAgICA8L2Rpdj5cblxuICAgICAgey8qIOiKgueCueWGheWuueWMuuWfnyAqL31cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwicC0yIGZsZXgtMVwiPlxuICAgICAgICB7Y2hpbGRyZW59XG4gICAgICA8L2Rpdj5cblxuICAgICAgey8qIOi+k+WFpeW8leiEmiAqL31cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgbGVmdC0wXCIgc3R5bGU9e3sgdG9wOiB0aXRsZUhlaWdodCB9fT5cbiAgICAgICAge2lucHV0UGlucy5tYXAoKHBpbiwgaW5kZXgpID0+IHtcbiAgICAgICAgICBjb25zdCBwaW5ZID0gcGluU3RhcnRZICsgKGluZGV4ICogcGluU3BhY2luZyk7XG4gICAgICAgICAgcmV0dXJuIChcbiAgICAgICAgICAgIDxkaXZcbiAgICAgICAgICAgICAga2V5PXtwaW4uaWR9XG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cImFic29sdXRlIGZsZXggaXRlbXMtY2VudGVyXCJcbiAgICAgICAgICAgICAgc3R5bGU9e3tcbiAgICAgICAgICAgICAgICB0b3A6IHBpblksXG4gICAgICAgICAgICAgICAgbGVmdDogLTYsIC8vIOW8leiEmuWchueCueeahOS4gOWNiuWcqOiKgueCueWkllxuICAgICAgICAgICAgICAgIHRyYW5zZm9ybTogJ3RyYW5zbGF0ZVkoLTUwJSknIC8vIOWeguebtOWxheS4reWvuem9kFxuICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICA8ZGl2XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgcGluLWNvbm5lY3RvciB3LTMgaC0zIHJvdW5kZWQtZnVsbCBib3JkZXItMiBjdXJzb3ItcG9pbnRlciB0cmFuc2l0aW9uLWNvbG9ycyAke1xuICAgICAgICAgICAgICAgICAgcGluLmNvbm5lY3RlZFxuICAgICAgICAgICAgICAgICAgICA/ICdiZy1ibHVlLTUwMCBib3JkZXItYmx1ZS02MDAnXG4gICAgICAgICAgICAgICAgICAgIDogJ2JnLXdoaXRlIGJvcmRlci1ncmF5LTQwMCBob3Zlcjpib3JkZXItYmx1ZS00MDAnXG4gICAgICAgICAgICAgICAgfWB9XG4gICAgICAgICAgICAgICAgdGl0bGU9e3Bpbi5uYW1lfVxuICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJhYnNvbHV0ZSBsZWZ0LTQgdG9wLTEvMiB0cmFuc2Zvcm0gLXRyYW5zbGF0ZS15LTEvMiB0ZXh0LXhzIHRleHQtZ3JheS02MDAgd2hpdGVzcGFjZS1ub3dyYXBcIj5cbiAgICAgICAgICAgICAgICB7cGluLm5hbWV9XG4gICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICk7XG4gICAgICAgIH0pfVxuICAgICAgPC9kaXY+XG5cbiAgICAgIHsvKiDovpPlh7rlvJXohJogKi99XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIHJpZ2h0LTBcIiBzdHlsZT17eyB0b3A6IHRpdGxlSGVpZ2h0IH19PlxuICAgICAgICB7b3V0cHV0UGlucy5tYXAoKHBpbiwgaW5kZXgpID0+IHtcbiAgICAgICAgICBjb25zdCBwaW5ZID0gcGluU3RhcnRZICsgKGluZGV4ICogcGluU3BhY2luZyk7XG4gICAgICAgICAgcmV0dXJuIChcbiAgICAgICAgICAgIDxkaXZcbiAgICAgICAgICAgICAga2V5PXtwaW4uaWR9XG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cImFic29sdXRlIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktZW5kXCJcbiAgICAgICAgICAgICAgc3R5bGU9e3tcbiAgICAgICAgICAgICAgICB0b3A6IHBpblksXG4gICAgICAgICAgICAgICAgcmlnaHQ6IC02LCAvLyDlvJXohJrlnIbngrnnmoTkuIDljYrlnKjoioLngrnlpJZcbiAgICAgICAgICAgICAgICB0cmFuc2Zvcm06ICd0cmFuc2xhdGVZKC01MCUpJyAvLyDlnoLnm7TlsYXkuK3lr7npvZBcbiAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiYWJzb2x1dGUgcmlnaHQtNCB0b3AtMS8yIHRyYW5zZm9ybSAtdHJhbnNsYXRlLXktMS8yIHRleHQteHMgdGV4dC1ncmF5LTYwMCB3aGl0ZXNwYWNlLW5vd3JhcFwiPlxuICAgICAgICAgICAgICAgIHtwaW4ubmFtZX1cbiAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICA8ZGl2XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgcGluLWNvbm5lY3RvciB3LTMgaC0zIHJvdW5kZWQtZnVsbCBib3JkZXItMiBjdXJzb3ItcG9pbnRlciB0cmFuc2l0aW9uLWNvbG9ycyAke1xuICAgICAgICAgICAgICAgICAgcGluLmNvbm5lY3RlZFxuICAgICAgICAgICAgICAgICAgICA/ICdiZy1ncmVlbi01MDAgYm9yZGVyLWdyZWVuLTYwMCdcbiAgICAgICAgICAgICAgICAgICAgOiAnYmctd2hpdGUgYm9yZGVyLWdyYXktNDAwIGhvdmVyOmJvcmRlci1ncmVlbi00MDAnXG4gICAgICAgICAgICAgICAgfWB9XG4gICAgICAgICAgICAgICAgdGl0bGU9e3Bpbi5uYW1lfVxuICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgKTtcbiAgICAgICAgfSl9XG4gICAgICA8L2Rpdj5cbiAgICA8L2Rpdj5cbiAgKTtcbn07Il0sIm5hbWVzIjpbIlJlYWN0IiwidXNlU3RhdGUiLCJ1c2VSZWYiLCJ1c2VDYWxsYmFjayIsIkJhc2VOb2RlQ29tcG9uZW50Iiwibm9kZSIsIm9uRXZlbnQiLCJvblVwZGF0ZU5vZGUiLCJjaGlsZHJlbiIsImlzRHJhZ2dpbmciLCJzZXRJc0RyYWdnaW5nIiwibm9kZVJlZiIsImFuaW1hdGlvbkZyYW1lUmVmIiwicGVuZGluZ1VwZGF0ZVJlZiIsImRyYWdJbmZvUmVmIiwiY2FsY3VsYXRlUGluUG9zaXRpb25zIiwiaW5wdXRQaW5zIiwicGlucyIsImZpbHRlciIsInBpbiIsInR5cGUiLCJvdXRwdXRQaW5zIiwibWF4UGlucyIsIk1hdGgiLCJtYXgiLCJsZW5ndGgiLCJ0aXRsZUhlaWdodCIsInBpblN0YXJ0WSIsInBpblNwYWNpbmciLCJmbHVzaFBlbmRpbmdVcGRhdGUiLCJjdXJyZW50IiwiaWQiLCJwb3NpdGlvbiIsImhhbmRsZU1vdXNlTW92ZSIsImUiLCJkZWx0YVgiLCJjbGllbnRYIiwic3RhcnRYIiwiZGVsdGFZIiwiY2xpZW50WSIsInN0YXJ0WSIsIm5ld1Bvc2l0aW9uIiwieCIsInN0YXJ0Tm9kZVgiLCJ5Iiwic3RhcnROb2RlWSIsInN0eWxlIiwibGVmdCIsInRvcCIsInJlcXVlc3RBbmltYXRpb25GcmFtZSIsImhhbmRsZU1vdXNlVXAiLCJjYW5jZWxBbmltYXRpb25GcmFtZSIsIm5vZGVJZCIsImRvY3VtZW50IiwicmVtb3ZlRXZlbnRMaXN0ZW5lciIsImhhbmRsZU1vdXNlRG93biIsImJ1dHRvbiIsInRhcmdldCIsImNsb3Nlc3QiLCJwcmV2ZW50RGVmYXVsdCIsInN0b3BQcm9wYWdhdGlvbiIsImRhdGEiLCJhZGRFdmVudExpc3RlbmVyIiwiaGFuZGxlQ2xpY2siLCJoYW5kbGVEb3VibGVDbGljayIsImdldFN0YXR1c0NvbG9yIiwic3RhdHVzIiwiZ2V0VGl0bGVCYXJDb2xvckNsYXNzZXMiLCJjb2xvciIsImNvbG9yTWFwIiwiZ3JheSIsInJlZCIsInllbGxvdyIsImdyZWVuIiwiYmx1ZSIsInB1cnBsZSIsImRpdiIsInJlZiIsImNsYXNzTmFtZSIsInNlbGVjdGVkIiwid2lkdGgiLCJzaXplIiwiaGVpZ2h0Iiwib25Nb3VzZURvd24iLCJvbkNsaWNrIiwib25Eb3VibGVDbGljayIsInNwYW4iLCJpY29uIiwibmFtZSIsInRpdGxlIiwic3ZnIiwiZmlsbCIsInZpZXdCb3giLCJwYXRoIiwiZmlsbFJ1bGUiLCJkIiwiY2xpcFJ1bGUiLCJtYXAiLCJpbmRleCIsInBpblkiLCJ0cmFuc2Zvcm0iLCJjb25uZWN0ZWQiLCJyaWdodCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/base/BaseNodeComponent.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/nodes/controls/KnobNodeComponent.tsx":
/*!**************************************************!*\
  !*** ./src/nodes/controls/KnobNodeComponent.tsx ***!
  \**************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   KnobNodeComponent: () => (/* binding */ KnobNodeComponent)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _base_BaseNodeComponent__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/base/BaseNodeComponent */ \"(app-pages-browser)/./src/base/BaseNodeComponent.tsx\");\n/* __next_internal_client_entry_do_not_use__ KnobNodeComponent auto */ \n\n\nconst KnobNodeComponent = (param)=>{\n    let { node, onEvent, onUpdateNode } = param;\n    const properties = node.properties;\n    const handleChange = (e)=>{\n        const newValue = parseInt(e.target.value, 10);\n        onUpdateNode(node.id, {\n            properties: {\n                ...properties,\n                value: newValue\n            }\n        });\n    // onPropertyChanged will trigger process, no need for testRun event\n    };\n    const getRangeAttributes = ()=>{\n        switch(properties.outputRange){\n            case '0-255':\n                return {\n                    min: 0,\n                    max: 255,\n                    step: 1\n                };\n            case '0-1':\n                return {\n                    min: 0,\n                    max: 1,\n                    step: 0.01\n                };\n            case 'boolean':\n                return {\n                    min: 0,\n                    max: 1,\n                    step: 1\n                };\n            case 'percentage':\n            default:\n                return {\n                    min: 0,\n                    max: 100,\n                    step: 1\n                };\n        }\n    };\n    const { min, max, step } = getRangeAttributes();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_base_BaseNodeComponent__WEBPACK_IMPORTED_MODULE_2__.BaseNodeComponent, {\n        node: node,\n        onEvent: onEvent,\n        onUpdateNode: onUpdateNode,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"p-3\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                    type: \"range\",\n                    min: min,\n                    max: max,\n                    step: step,\n                    value: properties.value,\n                    onChange: handleChange,\n                    onMouseDown: (e)=>e.stopPropagation(),\n                    className: \"w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/nodes/controls/KnobNodeComponent.tsx\",\n                    lineNumber: 46,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-1 text-center text-gray-700 font-medium\",\n                    children: properties.outputRange === 'boolean' ? properties.value > 0 ? 'On' : 'Off' : properties.value\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/nodes/controls/KnobNodeComponent.tsx\",\n                    lineNumber: 56,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/nodes/controls/KnobNodeComponent.tsx\",\n            lineNumber: 45,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/nodes/controls/KnobNodeComponent.tsx\",\n        lineNumber: 40,\n        columnNumber: 5\n    }, undefined);\n};\n_c = KnobNodeComponent;\nvar _c;\n$RefreshReg$(_c, \"KnobNodeComponent\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/nodes/controls/KnobNodeComponent.tsx\n"));

/***/ })

}]);