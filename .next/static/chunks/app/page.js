/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["app/page"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fuser%2FDocuments%2Faugment-projects%2Fnextjs-superNode%2Fsrc%2Fcomponents%2FWorkflowCanvas.tsx%22%2C%22ids%22%3A%5B%22WorkflowCanvas%22%5D%7D&server=false!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fuser%2FDocuments%2Faugment-projects%2Fnextjs-superNode%2Fsrc%2Fcomponents%2FWorkflowCanvas.tsx%22%2C%22ids%22%3A%5B%22WorkflowCanvas%22%5D%7D&server=false! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/WorkflowCanvas.tsx */ \"(app-pages-browser)/./src/components/WorkflowCanvas.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy8ucG5wbS9uZXh0QDE1LjQuMl9yZWFjdC1kb21AMTguMy4xX3JlYWN0QDE4LjMuMV9fcmVhY3RAMTguMy4xL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtZmxpZ2h0LWNsaWVudC1lbnRyeS1sb2FkZXIuanM/bW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGVXNlcnMlMkZ1c2VyJTJGRG9jdW1lbnRzJTJGYXVnbWVudC1wcm9qZWN0cyUyRm5leHRqcy1zdXBlck5vZGUlMkZzcmMlMkZjb21wb25lbnRzJTJGV29ya2Zsb3dDYW52YXMudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyV29ya2Zsb3dDYW52YXMlMjIlNUQlN0Qmc2VydmVyPWZhbHNlISIsIm1hcHBpbmdzIjoiQUFBQSxnTUFBa0siLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcIldvcmtmbG93Q2FudmFzXCJdICovIFwiL1VzZXJzL3VzZXIvRG9jdW1lbnRzL2F1Z21lbnQtcHJvamVjdHMvbmV4dGpzLXN1cGVyTm9kZS9zcmMvY29tcG9uZW50cy9Xb3JrZmxvd0NhbnZhcy50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fuser%2FDocuments%2Faugment-projects%2Fnextjs-superNode%2Fsrc%2Fcomponents%2FWorkflowCanvas.tsx%22%2C%22ids%22%3A%5B%22WorkflowCanvas%22%5D%7D&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js":
/*!*******************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js ***!
  \*******************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\n true &&\n  (function () {\n    function getComponentNameFromType(type) {\n      if (null == type) return null;\n      if (\"function\" === typeof type)\n        return type.$$typeof === REACT_CLIENT_REFERENCE\n          ? null\n          : type.displayName || type.name || null;\n      if (\"string\" === typeof type) return type;\n      switch (type) {\n        case REACT_FRAGMENT_TYPE:\n          return \"Fragment\";\n        case REACT_PROFILER_TYPE:\n          return \"Profiler\";\n        case REACT_STRICT_MODE_TYPE:\n          return \"StrictMode\";\n        case REACT_SUSPENSE_TYPE:\n          return \"Suspense\";\n        case REACT_SUSPENSE_LIST_TYPE:\n          return \"SuspenseList\";\n        case REACT_ACTIVITY_TYPE:\n          return \"Activity\";\n      }\n      if (\"object\" === typeof type)\n        switch (\n          (\"number\" === typeof type.tag &&\n            console.error(\n              \"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"\n            ),\n          type.$$typeof)\n        ) {\n          case REACT_PORTAL_TYPE:\n            return \"Portal\";\n          case REACT_CONTEXT_TYPE:\n            return type.displayName || \"Context\";\n          case REACT_CONSUMER_TYPE:\n            return (type._context.displayName || \"Context\") + \".Consumer\";\n          case REACT_FORWARD_REF_TYPE:\n            var innerType = type.render;\n            type = type.displayName;\n            type ||\n              ((type = innerType.displayName || innerType.name || \"\"),\n              (type = \"\" !== type ? \"ForwardRef(\" + type + \")\" : \"ForwardRef\"));\n            return type;\n          case REACT_MEMO_TYPE:\n            return (\n              (innerType = type.displayName || null),\n              null !== innerType\n                ? innerType\n                : getComponentNameFromType(type.type) || \"Memo\"\n            );\n          case REACT_LAZY_TYPE:\n            innerType = type._payload;\n            type = type._init;\n            try {\n              return getComponentNameFromType(type(innerType));\n            } catch (x) {}\n        }\n      return null;\n    }\n    function testStringCoercion(value) {\n      return \"\" + value;\n    }\n    function checkKeyStringCoercion(value) {\n      try {\n        testStringCoercion(value);\n        var JSCompiler_inline_result = !1;\n      } catch (e) {\n        JSCompiler_inline_result = !0;\n      }\n      if (JSCompiler_inline_result) {\n        JSCompiler_inline_result = console;\n        var JSCompiler_temp_const = JSCompiler_inline_result.error;\n        var JSCompiler_inline_result$jscomp$0 =\n          (\"function\" === typeof Symbol &&\n            Symbol.toStringTag &&\n            value[Symbol.toStringTag]) ||\n          value.constructor.name ||\n          \"Object\";\n        JSCompiler_temp_const.call(\n          JSCompiler_inline_result,\n          \"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\",\n          JSCompiler_inline_result$jscomp$0\n        );\n        return testStringCoercion(value);\n      }\n    }\n    function getTaskName(type) {\n      if (type === REACT_FRAGMENT_TYPE) return \"<>\";\n      if (\n        \"object\" === typeof type &&\n        null !== type &&\n        type.$$typeof === REACT_LAZY_TYPE\n      )\n        return \"<...>\";\n      try {\n        var name = getComponentNameFromType(type);\n        return name ? \"<\" + name + \">\" : \"<...>\";\n      } catch (x) {\n        return \"<...>\";\n      }\n    }\n    function getOwner() {\n      var dispatcher = ReactSharedInternals.A;\n      return null === dispatcher ? null : dispatcher.getOwner();\n    }\n    function UnknownOwner() {\n      return Error(\"react-stack-top-frame\");\n    }\n    function hasValidKey(config) {\n      if (hasOwnProperty.call(config, \"key\")) {\n        var getter = Object.getOwnPropertyDescriptor(config, \"key\").get;\n        if (getter && getter.isReactWarning) return !1;\n      }\n      return void 0 !== config.key;\n    }\n    function defineKeyPropWarningGetter(props, displayName) {\n      function warnAboutAccessingKey() {\n        specialPropKeyWarningShown ||\n          ((specialPropKeyWarningShown = !0),\n          console.error(\n            \"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\",\n            displayName\n          ));\n      }\n      warnAboutAccessingKey.isReactWarning = !0;\n      Object.defineProperty(props, \"key\", {\n        get: warnAboutAccessingKey,\n        configurable: !0\n      });\n    }\n    function elementRefGetterWithDeprecationWarning() {\n      var componentName = getComponentNameFromType(this.type);\n      didWarnAboutElementRef[componentName] ||\n        ((didWarnAboutElementRef[componentName] = !0),\n        console.error(\n          \"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\"\n        ));\n      componentName = this.props.ref;\n      return void 0 !== componentName ? componentName : null;\n    }\n    function ReactElement(\n      type,\n      key,\n      self,\n      source,\n      owner,\n      props,\n      debugStack,\n      debugTask\n    ) {\n      self = props.ref;\n      type = {\n        $$typeof: REACT_ELEMENT_TYPE,\n        type: type,\n        key: key,\n        props: props,\n        _owner: owner\n      };\n      null !== (void 0 !== self ? self : null)\n        ? Object.defineProperty(type, \"ref\", {\n            enumerable: !1,\n            get: elementRefGetterWithDeprecationWarning\n          })\n        : Object.defineProperty(type, \"ref\", { enumerable: !1, value: null });\n      type._store = {};\n      Object.defineProperty(type._store, \"validated\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: 0\n      });\n      Object.defineProperty(type, \"_debugInfo\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: null\n      });\n      Object.defineProperty(type, \"_debugStack\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugStack\n      });\n      Object.defineProperty(type, \"_debugTask\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugTask\n      });\n      Object.freeze && (Object.freeze(type.props), Object.freeze(type));\n      return type;\n    }\n    function jsxDEVImpl(\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self,\n      debugStack,\n      debugTask\n    ) {\n      var children = config.children;\n      if (void 0 !== children)\n        if (isStaticChildren)\n          if (isArrayImpl(children)) {\n            for (\n              isStaticChildren = 0;\n              isStaticChildren < children.length;\n              isStaticChildren++\n            )\n              validateChildKeys(children[isStaticChildren]);\n            Object.freeze && Object.freeze(children);\n          } else\n            console.error(\n              \"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\"\n            );\n        else validateChildKeys(children);\n      if (hasOwnProperty.call(config, \"key\")) {\n        children = getComponentNameFromType(type);\n        var keys = Object.keys(config).filter(function (k) {\n          return \"key\" !== k;\n        });\n        isStaticChildren =\n          0 < keys.length\n            ? \"{key: someKey, \" + keys.join(\": ..., \") + \": ...}\"\n            : \"{key: someKey}\";\n        didWarnAboutKeySpread[children + isStaticChildren] ||\n          ((keys =\n            0 < keys.length ? \"{\" + keys.join(\": ..., \") + \": ...}\" : \"{}\"),\n          console.error(\n            'A props object containing a \"key\" prop is being spread into JSX:\\n  let props = %s;\\n  <%s {...props} />\\nReact keys must be passed directly to JSX without using spread:\\n  let props = %s;\\n  <%s key={someKey} {...props} />',\n            isStaticChildren,\n            children,\n            keys,\n            children\n          ),\n          (didWarnAboutKeySpread[children + isStaticChildren] = !0));\n      }\n      children = null;\n      void 0 !== maybeKey &&\n        (checkKeyStringCoercion(maybeKey), (children = \"\" + maybeKey));\n      hasValidKey(config) &&\n        (checkKeyStringCoercion(config.key), (children = \"\" + config.key));\n      if (\"key\" in config) {\n        maybeKey = {};\n        for (var propName in config)\n          \"key\" !== propName && (maybeKey[propName] = config[propName]);\n      } else maybeKey = config;\n      children &&\n        defineKeyPropWarningGetter(\n          maybeKey,\n          \"function\" === typeof type\n            ? type.displayName || type.name || \"Unknown\"\n            : type\n        );\n      return ReactElement(\n        type,\n        children,\n        self,\n        source,\n        getOwner(),\n        maybeKey,\n        debugStack,\n        debugTask\n      );\n    }\n    function validateChildKeys(node) {\n      \"object\" === typeof node &&\n        null !== node &&\n        node.$$typeof === REACT_ELEMENT_TYPE &&\n        node._store &&\n        (node._store.validated = 1);\n    }\n    var React = __webpack_require__(/*! next/dist/compiled/react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/index.js\"),\n      REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n      REACT_PORTAL_TYPE = Symbol.for(\"react.portal\"),\n      REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\"),\n      REACT_STRICT_MODE_TYPE = Symbol.for(\"react.strict_mode\"),\n      REACT_PROFILER_TYPE = Symbol.for(\"react.profiler\"),\n      REACT_CONSUMER_TYPE = Symbol.for(\"react.consumer\"),\n      REACT_CONTEXT_TYPE = Symbol.for(\"react.context\"),\n      REACT_FORWARD_REF_TYPE = Symbol.for(\"react.forward_ref\"),\n      REACT_SUSPENSE_TYPE = Symbol.for(\"react.suspense\"),\n      REACT_SUSPENSE_LIST_TYPE = Symbol.for(\"react.suspense_list\"),\n      REACT_MEMO_TYPE = Symbol.for(\"react.memo\"),\n      REACT_LAZY_TYPE = Symbol.for(\"react.lazy\"),\n      REACT_ACTIVITY_TYPE = Symbol.for(\"react.activity\"),\n      REACT_CLIENT_REFERENCE = Symbol.for(\"react.client.reference\"),\n      ReactSharedInternals =\n        React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,\n      hasOwnProperty = Object.prototype.hasOwnProperty,\n      isArrayImpl = Array.isArray,\n      createTask = console.createTask\n        ? console.createTask\n        : function () {\n            return null;\n          };\n    React = {\n      react_stack_bottom_frame: function (callStackForError) {\n        return callStackForError();\n      }\n    };\n    var specialPropKeyWarningShown;\n    var didWarnAboutElementRef = {};\n    var unknownOwnerDebugStack = React.react_stack_bottom_frame.bind(\n      React,\n      UnknownOwner\n    )();\n    var unknownOwnerDebugTask = createTask(getTaskName(UnknownOwner));\n    var didWarnAboutKeySpread = {};\n    exports.Fragment = REACT_FRAGMENT_TYPE;\n    exports.jsxDEV = function (\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self\n    ) {\n      var trackActualOwner =\n        1e4 > ReactSharedInternals.recentlyCreatedOwnerStacks++;\n      return jsxDEVImpl(\n        type,\n        config,\n        maybeKey,\n        isStaticChildren,\n        source,\n        self,\n        trackActualOwner\n          ? Error(\"react-stack-top-frame\")\n          : unknownOwnerDebugStack,\n        trackActualOwner ? createTask(getTaskName(type)) : unknownOwnerDebugTask\n      );\n    };\n  })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js":
/*!*********************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js ***!
  \*********************************************************************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\n\nif (false) {} else {\n  module.exports = __webpack_require__(/*! ./cjs/react-jsx-dev-runtime.development.js */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy8ucG5wbS9uZXh0QDE1LjQuMl9yZWFjdC1kb21AMTguMy4xX3JlYWN0QDE4LjMuMV9fcmVhY3RAMTguMy4xL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY29tcGlsZWQvcmVhY3QvanN4LWRldi1ydW50aW1lLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLElBQUksS0FBcUMsRUFBRSxFQUUxQyxDQUFDO0FBQ0YsRUFBRSx5UUFBc0U7QUFDeEUiLCJzb3VyY2VzIjpbIi9Vc2Vycy91c2VyL0RvY3VtZW50cy9hdWdtZW50LXByb2plY3RzL25leHRqcy1zdXBlck5vZGUvbm9kZV9tb2R1bGVzLy5wbnBtL25leHRAMTUuNC4yX3JlYWN0LWRvbUAxOC4zLjFfcmVhY3RAMTguMy4xX19yZWFjdEAxOC4zLjEvbm9kZV9tb2R1bGVzL25leHQvZGlzdC9jb21waWxlZC9yZWFjdC9qc3gtZGV2LXJ1bnRpbWUuanMiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnO1xuXG5pZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgPT09ICdwcm9kdWN0aW9uJykge1xuICBtb2R1bGUuZXhwb3J0cyA9IHJlcXVpcmUoJy4vY2pzL3JlYWN0LWpzeC1kZXYtcnVudGltZS5wcm9kdWN0aW9uLmpzJyk7XG59IGVsc2Uge1xuICBtb2R1bGUuZXhwb3J0cyA9IHJlcXVpcmUoJy4vY2pzL3JlYWN0LWpzeC1kZXYtcnVudGltZS5kZXZlbG9wbWVudC5qcycpO1xufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/PropertyPanel.tsx":
/*!******************************************!*\
  !*** ./src/components/PropertyPanel.tsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PropertyPanel: () => (/* binding */ PropertyPanel)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ PropertyPanel auto */ \n\nconst PropertyPanel = (param)=>{\n    let { node, nodeInstance, onUpdateNode, onClose } = param;\n    var _nodeInstance_constructor_renderProperties, _nodeInstance_constructor;\n    if (!node || !nodeInstance) return null;\n    const handleNameChange = (name)=>{\n        onUpdateNode(node.id, {\n            name\n        });\n    };\n    const handleDescriptionChange = (description)=>{\n        onUpdateNode(node.id, {\n            description\n        });\n    };\n    // Dynamically render properties using the static method on the node's class\n    const NodeProperties = (_nodeInstance_constructor_renderProperties = (_nodeInstance_constructor = nodeInstance.constructor).renderProperties) === null || _nodeInstance_constructor_renderProperties === void 0 ? void 0 : _nodeInstance_constructor_renderProperties.call(_nodeInstance_constructor, {\n        node,\n        onUpdateNode,\n        onEvent: ()=>{}\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed right-0 top-0 h-full w-80 bg-white shadow-lg border-l border-gray-200 z-50 overflow-y-auto\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"p-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between mb-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold text-gray-800\",\n                            children: \"节点属性\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/PropertyPanel.tsx\",\n                            lineNumber: 41,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onClose,\n                            className: \"text-gray-400 hover:text-gray-600 transition-colors\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"w-6 h-6\",\n                                fill: \"none\",\n                                stroke: \"currentColor\",\n                                viewBox: \"0 0 24 24\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 2,\n                                    d: \"M6 18L18 6M6 6l12 12\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/PropertyPanel.tsx\",\n                                    lineNumber: 47,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/PropertyPanel.tsx\",\n                                lineNumber: 46,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/PropertyPanel.tsx\",\n                            lineNumber: 42,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/PropertyPanel.tsx\",\n                    lineNumber: 40,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4 mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                    children: \"节点名称\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/PropertyPanel.tsx\",\n                                    lineNumber: 55,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    value: node.name,\n                                    onChange: (e)=>handleNameChange(e.target.value),\n                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/PropertyPanel.tsx\",\n                                    lineNumber: 56,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/PropertyPanel.tsx\",\n                            lineNumber: 54,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                    children: \"描述\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/PropertyPanel.tsx\",\n                                    lineNumber: 64,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                    value: node.description || '',\n                                    onChange: (e)=>handleDescriptionChange(e.target.value),\n                                    rows: 3,\n                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/PropertyPanel.tsx\",\n                                    lineNumber: 65,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/PropertyPanel.tsx\",\n                            lineNumber: 63,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                    children: \"节点类型\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/PropertyPanel.tsx\",\n                                    lineNumber: 73,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    value: node.type,\n                                    disabled: true,\n                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-gray-500\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/PropertyPanel.tsx\",\n                                    lineNumber: 74,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/PropertyPanel.tsx\",\n                            lineNumber: 72,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/PropertyPanel.tsx\",\n                    lineNumber: 53,\n                    columnNumber: 9\n                }, undefined),\n                NodeProperties && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"border-t pt-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                            className: \"text-md font-medium text-gray-800 mb-4\",\n                            children: \"节点配置\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/PropertyPanel.tsx\",\n                            lineNumber: 86,\n                            columnNumber: 17\n                        }, undefined),\n                        NodeProperties\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/PropertyPanel.tsx\",\n                    lineNumber: 85,\n                    columnNumber: 13\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/PropertyPanel.tsx\",\n            lineNumber: 39,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/PropertyPanel.tsx\",\n        lineNumber: 38,\n        columnNumber: 5\n    }, undefined);\n};\n_c = PropertyPanel;\nvar _c;\n$RefreshReg$(_c, \"PropertyPanel\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/PropertyPanel.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/WorkflowCanvas.tsx":
/*!*******************************************!*\
  !*** ./src/components/WorkflowCanvas.tsx ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   WorkflowCanvas: () => (/* binding */ WorkflowCanvas)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _PropertyPanel__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./PropertyPanel */ \"(app-pages-browser)/./src/components/PropertyPanel.tsx\");\n/* harmony import */ var _generated_node_registry__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/generated/node-registry */ \"(app-pages-browser)/./src/generated/node-registry.ts\");\n/* __next_internal_client_entry_do_not_use__ WorkflowCanvas auto */ \nvar _s = $RefreshSig$();\n\n\n\nconst nodeComponents = Object.keys(_generated_node_registry__WEBPACK_IMPORTED_MODULE_3__.nodeRegistry).reduce((acc, key)=>{\n    const type = key;\n    const { category, nodeName } = _generated_node_registry__WEBPACK_IMPORTED_MODULE_3__.nodeRegistry[type];\n    const componentName = \"\".concat(nodeName, \"Component\");\n    acc[type] = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.lazy)(()=>__webpack_require__(\"(app-pages-browser)/./src/nodes lazy recursive ^\\\\.\\\\/.*\\\\.tsx$\")(\"./\".concat(category, \"/\").concat(componentName, \".tsx\")).then((module)=>({\n                default: module[componentName]\n            })));\n    return acc;\n}, {});\nconst WorkflowCanvas = ()=>{\n    _s();\n    const [nodes, setNodes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedNode, setSelectedNode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showPropertyPanel, setShowPropertyPanel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const canvasRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const nodeInstancesRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(new Map());\n    const createNode = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"WorkflowCanvas.useCallback[createNode]\": async (nodeType, position)=>{\n            const nodeInfo = _generated_node_registry__WEBPACK_IMPORTED_MODULE_3__.nodeRegistry[nodeType];\n            try {\n                const { category, nodeName } = nodeInfo;\n                const module = await __webpack_require__(\"(app-pages-browser)/./src/nodes lazy recursive ^\\\\.\\\\/.*\\\\.tsx$\")(\"./\".concat(category, \"/\").concat(nodeName, \".tsx\"));\n                const NodeClass = module[nodeName];\n                const instance = new NodeClass({\n                    position\n                });\n                const nodeData = instance.getData();\n                setNodes({\n                    \"WorkflowCanvas.useCallback[createNode]\": (prev)=>[\n                            ...prev,\n                            nodeData\n                        ]\n                }[\"WorkflowCanvas.useCallback[createNode]\"]);\n                nodeInstancesRef.current.set(nodeData.id, instance);\n            } catch (error) {\n                console.error('Error creating node of type \"'.concat(nodeType, '\":'), error);\n            }\n        }\n    }[\"WorkflowCanvas.useCallback[createNode]\"], []);\n    const updateNode = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"WorkflowCanvas.useCallback[updateNode]\": (nodeId, updates)=>{\n            const nodeInstance = nodeInstancesRef.current.get(nodeId);\n            if (!nodeInstance) return;\n            nodeInstance.updateData(updates);\n            if (updates.properties) {\n                Object.entries(updates.properties).forEach({\n                    \"WorkflowCanvas.useCallback[updateNode]\": (param)=>{\n                        let [key, value] = param;\n                        nodeInstance.setProperty(key, value);\n                    }\n                }[\"WorkflowCanvas.useCallback[updateNode]\"]);\n            }\n            const finalNodeData = nodeInstance.getData();\n            setNodes({\n                \"WorkflowCanvas.useCallback[updateNode]\": (prev)=>{\n                    const nodeIndex = prev.findIndex({\n                        \"WorkflowCanvas.useCallback[updateNode].nodeIndex\": (node)=>node.id === nodeId\n                    }[\"WorkflowCanvas.useCallback[updateNode].nodeIndex\"]);\n                    if (nodeIndex === -1) return prev;\n                    if (JSON.stringify(prev[nodeIndex]) === JSON.stringify(finalNodeData)) return prev;\n                    const newNodes = [\n                        ...prev\n                    ];\n                    newNodes[nodeIndex] = finalNodeData;\n                    return newNodes;\n                }\n            }[\"WorkflowCanvas.useCallback[updateNode]\"]);\n            if ((selectedNode === null || selectedNode === void 0 ? void 0 : selectedNode.id) === nodeId) {\n                setSelectedNode(finalNodeData);\n            }\n        }\n    }[\"WorkflowCanvas.useCallback[updateNode]\"], [\n        selectedNode\n    ]);\n    const handleNodeEvent = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"WorkflowCanvas.useCallback[handleNodeEvent]\": (event)=>{\n            const nodeInstance = nodeInstancesRef.current.get(event.nodeId);\n            if (!nodeInstance) return;\n            nodeInstance.handleEvent(event);\n            if (event.type === 'testRun') {\n                nodeInstance.process().then({\n                    \"WorkflowCanvas.useCallback[handleNodeEvent]\": ()=>{\n                        const { status, pins, properties } = nodeInstance.getData();\n                        updateNode(event.nodeId, {\n                            status,\n                            pins,\n                            properties\n                        });\n                    }\n                }[\"WorkflowCanvas.useCallback[handleNodeEvent]\"]);\n            } else if (event.type === 'click') {\n                const clickedNode = nodes.find({\n                    \"WorkflowCanvas.useCallback[handleNodeEvent].clickedNode\": (n)=>n.id === event.nodeId\n                }[\"WorkflowCanvas.useCallback[handleNodeEvent].clickedNode\"]);\n                if (clickedNode) {\n                    setNodes({\n                        \"WorkflowCanvas.useCallback[handleNodeEvent]\": (prev)=>prev.map({\n                                \"WorkflowCanvas.useCallback[handleNodeEvent]\": (node)=>({\n                                        ...node,\n                                        selected: node.id === event.nodeId\n                                    })\n                            }[\"WorkflowCanvas.useCallback[handleNodeEvent]\"])\n                    }[\"WorkflowCanvas.useCallback[handleNodeEvent]\"]);\n                    setSelectedNode(clickedNode);\n                }\n            } else if (event.type === 'doubleClick') {\n                const doubleClickedNode = nodes.find({\n                    \"WorkflowCanvas.useCallback[handleNodeEvent].doubleClickedNode\": (n)=>n.id === event.nodeId\n                }[\"WorkflowCanvas.useCallback[handleNodeEvent].doubleClickedNode\"]);\n                if (doubleClickedNode) {\n                    setSelectedNode(doubleClickedNode);\n                    setShowPropertyPanel(true);\n                }\n            }\n        }\n    }[\"WorkflowCanvas.useCallback[handleNodeEvent]\"], [\n        nodes,\n        updateNode\n    ]);\n    const handleCanvasClick = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"WorkflowCanvas.useCallback[handleCanvasClick]\": (e)=>{\n            if (e.target === canvasRef.current) {\n                setNodes({\n                    \"WorkflowCanvas.useCallback[handleCanvasClick]\": (prev)=>prev.map({\n                            \"WorkflowCanvas.useCallback[handleCanvasClick]\": (node)=>({\n                                    ...node,\n                                    selected: false\n                                })\n                        }[\"WorkflowCanvas.useCallback[handleCanvasClick]\"])\n                }[\"WorkflowCanvas.useCallback[handleCanvasClick]\"]);\n                setSelectedNode(null);\n            }\n        }\n    }[\"WorkflowCanvas.useCallback[handleCanvasClick]\"], []);\n    const renderNode = (node)=>{\n        const NodeComponent = nodeComponents[node.type];\n        if (!NodeComponent) return null;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_1__.Suspense, {\n            fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    left: node.position.x,\n                    top: node.position.y\n                },\n                className: \"absolute p-4 bg-gray-200 rounded animate-pulse\",\n                children: \"Loading...\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/WorkflowCanvas.tsx\",\n                lineNumber: 107,\n                columnNumber: 41\n            }, void 0),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NodeComponent, {\n                node: node,\n                onEvent: handleNodeEvent,\n                onUpdateNode: updateNode\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/WorkflowCanvas.tsx\",\n                lineNumber: 108,\n                columnNumber: 9\n            }, undefined)\n        }, node.id, false, {\n            fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/WorkflowCanvas.tsx\",\n            lineNumber: 107,\n            columnNumber: 7\n        }, undefined);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative w-full h-screen bg-gray-50 overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-4 left-4 z-10 bg-white rounded-lg shadow-md p-2 flex flex-wrap gap-2\",\n                children: [\n                    Object.entries(_generated_node_registry__WEBPACK_IMPORTED_MODULE_3__.nodeRegistry).map((param)=>{\n                        let [type, { name }] = param;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>createNode(type, {\n                                    x: 100,\n                                    y: 100\n                                }),\n                            className: \"px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors\",\n                            children: [\n                                \"添加 \",\n                                name\n                            ]\n                        }, type, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/WorkflowCanvas.tsx\",\n                            lineNumber: 121,\n                            columnNumber: 11\n                        }, undefined);\n                    }),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>{\n                            setNodes([]);\n                            nodeInstancesRef.current.clear();\n                            setSelectedNode(null);\n                            setShowPropertyPanel(false);\n                        },\n                        className: \"px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600 transition-colors\",\n                        children: \"清空画布\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/WorkflowCanvas.tsx\",\n                        lineNumber: 129,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/WorkflowCanvas.tsx\",\n                lineNumber: 119,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-4 right-4 z-10 bg-white rounded-lg shadow-md p-2\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-sm text-gray-600\",\n                    children: [\n                        \"节点数量: \",\n                        nodes.length,\n                        selectedNode && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-1\",\n                            children: [\n                                \"选中: \",\n                                selectedNode.name\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/WorkflowCanvas.tsx\",\n                            lineNumber: 145,\n                            columnNumber: 28\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/WorkflowCanvas.tsx\",\n                    lineNumber: 143,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/WorkflowCanvas.tsx\",\n                lineNumber: 142,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: canvasRef,\n                className: \"w-full h-full relative\",\n                onClick: handleCanvasClick,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 opacity-20\",\n                        style: {\n                            backgroundImage: \"linear-gradient(to right, #e5e7eb 1px, transparent 1px), linear-gradient(to bottom, #e5e7eb 1px, transparent 1px)\",\n                            backgroundSize: '20px 20px'\n                        }\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/WorkflowCanvas.tsx\",\n                        lineNumber: 150,\n                        columnNumber: 9\n                    }, undefined),\n                    nodes.map(renderNode),\n                    nodes.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 flex items-center justify-center pointer-events-none\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-gray-400 text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-lg mb-2\",\n                                    children: \"工作流画布\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/WorkflowCanvas.tsx\",\n                                    lineNumber: 161,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm\",\n                                    children: \"点击工具栏按钮添加节点\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/WorkflowCanvas.tsx\",\n                                    lineNumber: 162,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/WorkflowCanvas.tsx\",\n                            lineNumber: 160,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/WorkflowCanvas.tsx\",\n                        lineNumber: 159,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/WorkflowCanvas.tsx\",\n                lineNumber: 149,\n                columnNumber: 7\n            }, undefined),\n            showPropertyPanel && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_PropertyPanel__WEBPACK_IMPORTED_MODULE_2__.PropertyPanel, {\n                node: selectedNode,\n                nodeInstance: selectedNode ? nodeInstancesRef.current.get(selectedNode.id) : undefined,\n                onUpdateNode: updateNode,\n                onClose: ()=>setShowPropertyPanel(false)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/WorkflowCanvas.tsx\",\n                lineNumber: 169,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/WorkflowCanvas.tsx\",\n        lineNumber: 118,\n        columnNumber: 5\n    }, undefined);\n};\n_s(WorkflowCanvas, \"EdMtAbEaUN+Oei9y6EWlXz88QtM=\");\n_c = WorkflowCanvas;\nvar _c;\n$RefreshReg$(_c, \"WorkflowCanvas\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/WorkflowCanvas.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/generated/node-registry.ts":
/*!****************************************!*\
  !*** ./src/generated/node-registry.ts ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   nodeRegistry: () => (/* binding */ nodeRegistry)\n/* harmony export */ });\n// This file is auto-generated by scripts/generate-node-registry.mjs\n// Do not edit this file directly.\nconst nodeRegistry = {\n    \"knob\": {\n        \"name\": \"旋钮\",\n        \"category\": \"controls\",\n        \"nodeName\": \"KnobNode\"\n    },\n    \"led\": {\n        \"name\": \"RGB LED\",\n        \"category\": \"eda\",\n        \"nodeName\": \"LedNode\"\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9nZW5lcmF0ZWQvbm9kZS1yZWdpc3RyeS50cyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsb0VBQW9FO0FBQ3BFLGtDQUFrQztBQUUzQixNQUFNQSxlQUFlO0lBQzFCLFFBQVE7UUFDTixRQUFRO1FBQ1IsWUFBWTtRQUNaLFlBQVk7SUFDZDtJQUNBLE9BQU87UUFDTCxRQUFRO1FBQ1IsWUFBWTtRQUNaLFlBQVk7SUFDZDtBQUNGLEVBQVciLCJzb3VyY2VzIjpbIi9Vc2Vycy91c2VyL0RvY3VtZW50cy9hdWdtZW50LXByb2plY3RzL25leHRqcy1zdXBlck5vZGUvc3JjL2dlbmVyYXRlZC9ub2RlLXJlZ2lzdHJ5LnRzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIFRoaXMgZmlsZSBpcyBhdXRvLWdlbmVyYXRlZCBieSBzY3JpcHRzL2dlbmVyYXRlLW5vZGUtcmVnaXN0cnkubWpzXG4vLyBEbyBub3QgZWRpdCB0aGlzIGZpbGUgZGlyZWN0bHkuXG5cbmV4cG9ydCBjb25zdCBub2RlUmVnaXN0cnkgPSB7XG4gIFwia25vYlwiOiB7XG4gICAgXCJuYW1lXCI6IFwi5peL6ZKuXCIsXG4gICAgXCJjYXRlZ29yeVwiOiBcImNvbnRyb2xzXCIsXG4gICAgXCJub2RlTmFtZVwiOiBcIktub2JOb2RlXCJcbiAgfSxcbiAgXCJsZWRcIjoge1xuICAgIFwibmFtZVwiOiBcIlJHQiBMRURcIixcbiAgICBcImNhdGVnb3J5XCI6IFwiZWRhXCIsXG4gICAgXCJub2RlTmFtZVwiOiBcIkxlZE5vZGVcIlxuICB9XG59IGFzIGNvbnN0O1xuIl0sIm5hbWVzIjpbIm5vZGVSZWdpc3RyeSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/generated/node-registry.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/nodes lazy recursive ^\\.\\/.*\\.tsx$":
/*!********************************************************!*\
  !*** ./src/nodes/ lazy ^\.\/.*\.tsx$ namespace object ***!
  \********************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

var map = {
	"./controls/KnobNode.tsx": [
		"(app-pages-browser)/./src/nodes/controls/KnobNode.tsx",
		"_app-pages-browser_src_nodes_controls_KnobNode_tsx"
	],
	"./controls/KnobNodeComponent.tsx": [
		"(app-pages-browser)/./src/nodes/controls/KnobNodeComponent.tsx",
		"_app-pages-browser_src_nodes_controls_KnobNodeComponent_tsx"
	],
	"./eda/LedNode.tsx": [
		"(app-pages-browser)/./src/nodes/eda/LedNode.tsx",
		"_app-pages-browser_src_nodes_eda_LedNode_tsx"
	],
	"./eda/LedNodeComponent.tsx": [
		"(app-pages-browser)/./src/nodes/eda/LedNodeComponent.tsx",
		"_app-pages-browser_src_nodes_eda_LedNodeComponent_tsx"
	]
};
function webpackAsyncContext(req) {
	if(!__webpack_require__.o(map, req)) {
		return Promise.resolve().then(() => {
			var e = new Error("Cannot find module '" + req + "'");
			e.code = 'MODULE_NOT_FOUND';
			throw e;
		});
	}

	var ids = map[req], id = ids[0];
	return __webpack_require__.e(ids[1]).then(() => {
		return __webpack_require__(id);
	});
}
webpackAsyncContext.keys = () => (Object.keys(map));
webpackAsyncContext.id = "(app-pages-browser)/./src/nodes lazy recursive ^\\.\\/.*\\.tsx$";
module.exports = webpackAsyncContext;

/***/ })

},
/******/ __webpack_require__ => { // webpackRuntimeModules
/******/ var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
/******/ __webpack_require__.O(0, ["main-app"], () => (__webpack_exec__("(app-pages-browser)/./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fuser%2FDocuments%2Faugment-projects%2Fnextjs-superNode%2Fsrc%2Fcomponents%2FWorkflowCanvas.tsx%22%2C%22ids%22%3A%5B%22WorkflowCanvas%22%5D%7D&server=false!")));
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);