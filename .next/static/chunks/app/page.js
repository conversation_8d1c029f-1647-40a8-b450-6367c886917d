/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["app/page"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fuser%2FDocuments%2Faugment-projects%2Fnextjs-superNode%2Fsrc%2Fcomponents%2FWorkflowCanvas.tsx%22%2C%22ids%22%3A%5B%22WorkflowCanvas%22%5D%7D&server=false!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fuser%2FDocuments%2Faugment-projects%2Fnextjs-superNode%2Fsrc%2Fcomponents%2FWorkflowCanvas.tsx%22%2C%22ids%22%3A%5B%22WorkflowCanvas%22%5D%7D&server=false! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/WorkflowCanvas.tsx */ \"(app-pages-browser)/./src/components/WorkflowCanvas.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy8ucG5wbS9uZXh0QDE1LjQuMl9yZWFjdC1kb21AMTguMy4xX3JlYWN0QDE4LjMuMV9fcmVhY3RAMTguMy4xL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtZmxpZ2h0LWNsaWVudC1lbnRyeS1sb2FkZXIuanM/bW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGVXNlcnMlMkZ1c2VyJTJGRG9jdW1lbnRzJTJGYXVnbWVudC1wcm9qZWN0cyUyRm5leHRqcy1zdXBlck5vZGUlMkZzcmMlMkZjb21wb25lbnRzJTJGV29ya2Zsb3dDYW52YXMudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyV29ya2Zsb3dDYW52YXMlMjIlNUQlN0Qmc2VydmVyPWZhbHNlISIsIm1hcHBpbmdzIjoiQUFBQSxnTUFBa0siLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcIldvcmtmbG93Q2FudmFzXCJdICovIFwiL1VzZXJzL3VzZXIvRG9jdW1lbnRzL2F1Z21lbnQtcHJvamVjdHMvbmV4dGpzLXN1cGVyTm9kZS9zcmMvY29tcG9uZW50cy9Xb3JrZmxvd0NhbnZhcy50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fuser%2FDocuments%2Faugment-projects%2Fnextjs-superNode%2Fsrc%2Fcomponents%2FWorkflowCanvas.tsx%22%2C%22ids%22%3A%5B%22WorkflowCanvas%22%5D%7D&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js":
/*!*******************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js ***!
  \*******************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\n true &&\n  (function () {\n    function getComponentNameFromType(type) {\n      if (null == type) return null;\n      if (\"function\" === typeof type)\n        return type.$$typeof === REACT_CLIENT_REFERENCE\n          ? null\n          : type.displayName || type.name || null;\n      if (\"string\" === typeof type) return type;\n      switch (type) {\n        case REACT_FRAGMENT_TYPE:\n          return \"Fragment\";\n        case REACT_PROFILER_TYPE:\n          return \"Profiler\";\n        case REACT_STRICT_MODE_TYPE:\n          return \"StrictMode\";\n        case REACT_SUSPENSE_TYPE:\n          return \"Suspense\";\n        case REACT_SUSPENSE_LIST_TYPE:\n          return \"SuspenseList\";\n        case REACT_ACTIVITY_TYPE:\n          return \"Activity\";\n      }\n      if (\"object\" === typeof type)\n        switch (\n          (\"number\" === typeof type.tag &&\n            console.error(\n              \"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"\n            ),\n          type.$$typeof)\n        ) {\n          case REACT_PORTAL_TYPE:\n            return \"Portal\";\n          case REACT_CONTEXT_TYPE:\n            return type.displayName || \"Context\";\n          case REACT_CONSUMER_TYPE:\n            return (type._context.displayName || \"Context\") + \".Consumer\";\n          case REACT_FORWARD_REF_TYPE:\n            var innerType = type.render;\n            type = type.displayName;\n            type ||\n              ((type = innerType.displayName || innerType.name || \"\"),\n              (type = \"\" !== type ? \"ForwardRef(\" + type + \")\" : \"ForwardRef\"));\n            return type;\n          case REACT_MEMO_TYPE:\n            return (\n              (innerType = type.displayName || null),\n              null !== innerType\n                ? innerType\n                : getComponentNameFromType(type.type) || \"Memo\"\n            );\n          case REACT_LAZY_TYPE:\n            innerType = type._payload;\n            type = type._init;\n            try {\n              return getComponentNameFromType(type(innerType));\n            } catch (x) {}\n        }\n      return null;\n    }\n    function testStringCoercion(value) {\n      return \"\" + value;\n    }\n    function checkKeyStringCoercion(value) {\n      try {\n        testStringCoercion(value);\n        var JSCompiler_inline_result = !1;\n      } catch (e) {\n        JSCompiler_inline_result = !0;\n      }\n      if (JSCompiler_inline_result) {\n        JSCompiler_inline_result = console;\n        var JSCompiler_temp_const = JSCompiler_inline_result.error;\n        var JSCompiler_inline_result$jscomp$0 =\n          (\"function\" === typeof Symbol &&\n            Symbol.toStringTag &&\n            value[Symbol.toStringTag]) ||\n          value.constructor.name ||\n          \"Object\";\n        JSCompiler_temp_const.call(\n          JSCompiler_inline_result,\n          \"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\",\n          JSCompiler_inline_result$jscomp$0\n        );\n        return testStringCoercion(value);\n      }\n    }\n    function getTaskName(type) {\n      if (type === REACT_FRAGMENT_TYPE) return \"<>\";\n      if (\n        \"object\" === typeof type &&\n        null !== type &&\n        type.$$typeof === REACT_LAZY_TYPE\n      )\n        return \"<...>\";\n      try {\n        var name = getComponentNameFromType(type);\n        return name ? \"<\" + name + \">\" : \"<...>\";\n      } catch (x) {\n        return \"<...>\";\n      }\n    }\n    function getOwner() {\n      var dispatcher = ReactSharedInternals.A;\n      return null === dispatcher ? null : dispatcher.getOwner();\n    }\n    function UnknownOwner() {\n      return Error(\"react-stack-top-frame\");\n    }\n    function hasValidKey(config) {\n      if (hasOwnProperty.call(config, \"key\")) {\n        var getter = Object.getOwnPropertyDescriptor(config, \"key\").get;\n        if (getter && getter.isReactWarning) return !1;\n      }\n      return void 0 !== config.key;\n    }\n    function defineKeyPropWarningGetter(props, displayName) {\n      function warnAboutAccessingKey() {\n        specialPropKeyWarningShown ||\n          ((specialPropKeyWarningShown = !0),\n          console.error(\n            \"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\",\n            displayName\n          ));\n      }\n      warnAboutAccessingKey.isReactWarning = !0;\n      Object.defineProperty(props, \"key\", {\n        get: warnAboutAccessingKey,\n        configurable: !0\n      });\n    }\n    function elementRefGetterWithDeprecationWarning() {\n      var componentName = getComponentNameFromType(this.type);\n      didWarnAboutElementRef[componentName] ||\n        ((didWarnAboutElementRef[componentName] = !0),\n        console.error(\n          \"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\"\n        ));\n      componentName = this.props.ref;\n      return void 0 !== componentName ? componentName : null;\n    }\n    function ReactElement(\n      type,\n      key,\n      self,\n      source,\n      owner,\n      props,\n      debugStack,\n      debugTask\n    ) {\n      self = props.ref;\n      type = {\n        $$typeof: REACT_ELEMENT_TYPE,\n        type: type,\n        key: key,\n        props: props,\n        _owner: owner\n      };\n      null !== (void 0 !== self ? self : null)\n        ? Object.defineProperty(type, \"ref\", {\n            enumerable: !1,\n            get: elementRefGetterWithDeprecationWarning\n          })\n        : Object.defineProperty(type, \"ref\", { enumerable: !1, value: null });\n      type._store = {};\n      Object.defineProperty(type._store, \"validated\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: 0\n      });\n      Object.defineProperty(type, \"_debugInfo\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: null\n      });\n      Object.defineProperty(type, \"_debugStack\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugStack\n      });\n      Object.defineProperty(type, \"_debugTask\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugTask\n      });\n      Object.freeze && (Object.freeze(type.props), Object.freeze(type));\n      return type;\n    }\n    function jsxDEVImpl(\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self,\n      debugStack,\n      debugTask\n    ) {\n      var children = config.children;\n      if (void 0 !== children)\n        if (isStaticChildren)\n          if (isArrayImpl(children)) {\n            for (\n              isStaticChildren = 0;\n              isStaticChildren < children.length;\n              isStaticChildren++\n            )\n              validateChildKeys(children[isStaticChildren]);\n            Object.freeze && Object.freeze(children);\n          } else\n            console.error(\n              \"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\"\n            );\n        else validateChildKeys(children);\n      if (hasOwnProperty.call(config, \"key\")) {\n        children = getComponentNameFromType(type);\n        var keys = Object.keys(config).filter(function (k) {\n          return \"key\" !== k;\n        });\n        isStaticChildren =\n          0 < keys.length\n            ? \"{key: someKey, \" + keys.join(\": ..., \") + \": ...}\"\n            : \"{key: someKey}\";\n        didWarnAboutKeySpread[children + isStaticChildren] ||\n          ((keys =\n            0 < keys.length ? \"{\" + keys.join(\": ..., \") + \": ...}\" : \"{}\"),\n          console.error(\n            'A props object containing a \"key\" prop is being spread into JSX:\\n  let props = %s;\\n  <%s {...props} />\\nReact keys must be passed directly to JSX without using spread:\\n  let props = %s;\\n  <%s key={someKey} {...props} />',\n            isStaticChildren,\n            children,\n            keys,\n            children\n          ),\n          (didWarnAboutKeySpread[children + isStaticChildren] = !0));\n      }\n      children = null;\n      void 0 !== maybeKey &&\n        (checkKeyStringCoercion(maybeKey), (children = \"\" + maybeKey));\n      hasValidKey(config) &&\n        (checkKeyStringCoercion(config.key), (children = \"\" + config.key));\n      if (\"key\" in config) {\n        maybeKey = {};\n        for (var propName in config)\n          \"key\" !== propName && (maybeKey[propName] = config[propName]);\n      } else maybeKey = config;\n      children &&\n        defineKeyPropWarningGetter(\n          maybeKey,\n          \"function\" === typeof type\n            ? type.displayName || type.name || \"Unknown\"\n            : type\n        );\n      return ReactElement(\n        type,\n        children,\n        self,\n        source,\n        getOwner(),\n        maybeKey,\n        debugStack,\n        debugTask\n      );\n    }\n    function validateChildKeys(node) {\n      \"object\" === typeof node &&\n        null !== node &&\n        node.$$typeof === REACT_ELEMENT_TYPE &&\n        node._store &&\n        (node._store.validated = 1);\n    }\n    var React = __webpack_require__(/*! next/dist/compiled/react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/index.js\"),\n      REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n      REACT_PORTAL_TYPE = Symbol.for(\"react.portal\"),\n      REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\"),\n      REACT_STRICT_MODE_TYPE = Symbol.for(\"react.strict_mode\"),\n      REACT_PROFILER_TYPE = Symbol.for(\"react.profiler\"),\n      REACT_CONSUMER_TYPE = Symbol.for(\"react.consumer\"),\n      REACT_CONTEXT_TYPE = Symbol.for(\"react.context\"),\n      REACT_FORWARD_REF_TYPE = Symbol.for(\"react.forward_ref\"),\n      REACT_SUSPENSE_TYPE = Symbol.for(\"react.suspense\"),\n      REACT_SUSPENSE_LIST_TYPE = Symbol.for(\"react.suspense_list\"),\n      REACT_MEMO_TYPE = Symbol.for(\"react.memo\"),\n      REACT_LAZY_TYPE = Symbol.for(\"react.lazy\"),\n      REACT_ACTIVITY_TYPE = Symbol.for(\"react.activity\"),\n      REACT_CLIENT_REFERENCE = Symbol.for(\"react.client.reference\"),\n      ReactSharedInternals =\n        React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,\n      hasOwnProperty = Object.prototype.hasOwnProperty,\n      isArrayImpl = Array.isArray,\n      createTask = console.createTask\n        ? console.createTask\n        : function () {\n            return null;\n          };\n    React = {\n      react_stack_bottom_frame: function (callStackForError) {\n        return callStackForError();\n      }\n    };\n    var specialPropKeyWarningShown;\n    var didWarnAboutElementRef = {};\n    var unknownOwnerDebugStack = React.react_stack_bottom_frame.bind(\n      React,\n      UnknownOwner\n    )();\n    var unknownOwnerDebugTask = createTask(getTaskName(UnknownOwner));\n    var didWarnAboutKeySpread = {};\n    exports.Fragment = REACT_FRAGMENT_TYPE;\n    exports.jsxDEV = function (\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self\n    ) {\n      var trackActualOwner =\n        1e4 > ReactSharedInternals.recentlyCreatedOwnerStacks++;\n      return jsxDEVImpl(\n        type,\n        config,\n        maybeKey,\n        isStaticChildren,\n        source,\n        self,\n        trackActualOwner\n          ? Error(\"react-stack-top-frame\")\n          : unknownOwnerDebugStack,\n        trackActualOwner ? createTask(getTaskName(type)) : unknownOwnerDebugTask\n      );\n    };\n  })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js":
/*!*********************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js ***!
  \*********************************************************************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\n\nif (false) {} else {\n  module.exports = __webpack_require__(/*! ./cjs/react-jsx-dev-runtime.development.js */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy8ucG5wbS9uZXh0QDE1LjQuMl9yZWFjdC1kb21AMTguMy4xX3JlYWN0QDE4LjMuMV9fcmVhY3RAMTguMy4xL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY29tcGlsZWQvcmVhY3QvanN4LWRldi1ydW50aW1lLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLElBQUksS0FBcUMsRUFBRSxFQUUxQyxDQUFDO0FBQ0YsRUFBRSx5UUFBc0U7QUFDeEUiLCJzb3VyY2VzIjpbIi9Vc2Vycy91c2VyL0RvY3VtZW50cy9hdWdtZW50LXByb2plY3RzL25leHRqcy1zdXBlck5vZGUvbm9kZV9tb2R1bGVzLy5wbnBtL25leHRAMTUuNC4yX3JlYWN0LWRvbUAxOC4zLjFfcmVhY3RAMTguMy4xX19yZWFjdEAxOC4zLjEvbm9kZV9tb2R1bGVzL25leHQvZGlzdC9jb21waWxlZC9yZWFjdC9qc3gtZGV2LXJ1bnRpbWUuanMiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnO1xuXG5pZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgPT09ICdwcm9kdWN0aW9uJykge1xuICBtb2R1bGUuZXhwb3J0cyA9IHJlcXVpcmUoJy4vY2pzL3JlYWN0LWpzeC1kZXYtcnVudGltZS5wcm9kdWN0aW9uLmpzJyk7XG59IGVsc2Uge1xuICBtb2R1bGUuZXhwb3J0cyA9IHJlcXVpcmUoJy4vY2pzL3JlYWN0LWpzeC1kZXYtcnVudGltZS5kZXZlbG9wbWVudC5qcycpO1xufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/PropertyPanel.tsx":
/*!******************************************!*\
  !*** ./src/components/PropertyPanel.tsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PropertyPanel: () => (/* binding */ PropertyPanel)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ PropertyPanel auto */ \n\nconst PropertyPanel = (param)=>{\n    let { node, onUpdateNode, onClose } = param;\n    if (!node) return null;\n    const handlePropertyChange = (key, value)=>{\n        onUpdateNode(node.id, {\n            properties: {\n                ...node.properties,\n                [key]: value\n            }\n        });\n    };\n    const handleNameChange = (name)=>{\n        onUpdateNode(node.id, {\n            name\n        });\n    };\n    const handleDescriptionChange = (description)=>{\n        onUpdateNode(node.id, {\n            description\n        });\n    };\n    const renderLedProperties = ()=>{\n        const properties = node.properties;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                            children: \"颜色模式\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/PropertyPanel.tsx\",\n                            lineNumber: 43,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                            value: properties.colorMode || '3color',\n                            onChange: (e)=>handlePropertyChange('colorMode', e.target.value),\n                            className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"1color\",\n                                    children: \"单色 (红色)\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/PropertyPanel.tsx\",\n                                    lineNumber: 51,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"2color\",\n                                    children: \"双色 (红绿)\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/PropertyPanel.tsx\",\n                                    lineNumber: 52,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"3color\",\n                                    children: \"三色 (RGB)\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/PropertyPanel.tsx\",\n                                    lineNumber: 53,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/PropertyPanel.tsx\",\n                            lineNumber: 46,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/PropertyPanel.tsx\",\n                    lineNumber: 42,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                            children: \"输入范围\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/PropertyPanel.tsx\",\n                            lineNumber: 58,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                            value: properties.inputRange || 'percentage',\n                            onChange: (e)=>handlePropertyChange('inputRange', e.target.value),\n                            className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"percentage\",\n                                    children: \"百分比 (0-100)\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/PropertyPanel.tsx\",\n                                    lineNumber: 66,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"0-255\",\n                                    children: \"0-255\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/PropertyPanel.tsx\",\n                                    lineNumber: 67,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"0-1\",\n                                    children: \"0-1\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/PropertyPanel.tsx\",\n                                    lineNumber: 68,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"boolean\",\n                                    children: \"布尔值\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/PropertyPanel.tsx\",\n                                    lineNumber: 69,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/PropertyPanel.tsx\",\n                            lineNumber: 61,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/PropertyPanel.tsx\",\n                    lineNumber: 57,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                            children: \"亮度 (%)\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/PropertyPanel.tsx\",\n                            lineNumber: 74,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"range\",\n                            min: \"0\",\n                            max: \"100\",\n                            value: properties.brightness || 100,\n                            onChange: (e)=>handlePropertyChange('brightness', parseInt(e.target.value)),\n                            className: \"w-full\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/PropertyPanel.tsx\",\n                            lineNumber: 77,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm text-gray-500 text-center\",\n                            children: [\n                                properties.brightness || 100,\n                                \"%\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/PropertyPanel.tsx\",\n                            lineNumber: 85,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/PropertyPanel.tsx\",\n                    lineNumber: 73,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"border-t pt-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                            className: \"text-sm font-medium text-gray-700 mb-2\",\n                            children: \"当前颜色值\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/PropertyPanel.tsx\",\n                            lineNumber: 91,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-3 gap-2 text-sm\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-red-600 font-medium\",\n                                        children: [\n                                            \"红: \",\n                                            properties.redValue || 0\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/PropertyPanel.tsx\",\n                                        lineNumber: 94,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/PropertyPanel.tsx\",\n                                    lineNumber: 93,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-green-600 font-medium\",\n                                        children: [\n                                            \"绿: \",\n                                            properties.greenValue || 0\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/PropertyPanel.tsx\",\n                                        lineNumber: 97,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/PropertyPanel.tsx\",\n                                    lineNumber: 96,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-blue-600 font-medium\",\n                                        children: [\n                                            \"蓝: \",\n                                            properties.blueValue || 0\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/PropertyPanel.tsx\",\n                                        lineNumber: 100,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/PropertyPanel.tsx\",\n                                    lineNumber: 99,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/PropertyPanel.tsx\",\n                            lineNumber: 92,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-2 text-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full h-8 rounded border\",\n                                style: {\n                                    backgroundColor: \"rgb(\".concat(properties.redValue || 0, \", \").concat(properties.greenValue || 0, \", \").concat(properties.blueValue || 0, \")\")\n                                }\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/PropertyPanel.tsx\",\n                                lineNumber: 104,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/PropertyPanel.tsx\",\n                            lineNumber: 103,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/PropertyPanel.tsx\",\n                    lineNumber: 90,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/PropertyPanel.tsx\",\n            lineNumber: 41,\n            columnNumber: 7\n        }, undefined);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed right-0 top-0 h-full w-80 bg-white shadow-lg border-l border-gray-200 z-50 overflow-y-auto\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"p-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between mb-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold text-gray-800\",\n                            children: \"节点属性\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/PropertyPanel.tsx\",\n                            lineNumber: 121,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onClose,\n                            className: \"text-gray-400 hover:text-gray-600 transition-colors\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"w-6 h-6\",\n                                fill: \"none\",\n                                stroke: \"currentColor\",\n                                viewBox: \"0 0 24 24\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 2,\n                                    d: \"M6 18L18 6M6 6l12 12\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/PropertyPanel.tsx\",\n                                    lineNumber: 127,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/PropertyPanel.tsx\",\n                                lineNumber: 126,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/PropertyPanel.tsx\",\n                            lineNumber: 122,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/PropertyPanel.tsx\",\n                    lineNumber: 120,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4 mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                    children: \"节点名称\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/PropertyPanel.tsx\",\n                                    lineNumber: 135,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    value: node.name,\n                                    onChange: (e)=>handleNameChange(e.target.value),\n                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/PropertyPanel.tsx\",\n                                    lineNumber: 138,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/PropertyPanel.tsx\",\n                            lineNumber: 134,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                    children: \"描述\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/PropertyPanel.tsx\",\n                                    lineNumber: 147,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                    value: node.description || '',\n                                    onChange: (e)=>handleDescriptionChange(e.target.value),\n                                    rows: 3,\n                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/PropertyPanel.tsx\",\n                                    lineNumber: 150,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/PropertyPanel.tsx\",\n                            lineNumber: 146,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                    children: \"节点类型\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/PropertyPanel.tsx\",\n                                    lineNumber: 159,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    value: node.type,\n                                    disabled: true,\n                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-gray-500\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/PropertyPanel.tsx\",\n                                    lineNumber: 162,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/PropertyPanel.tsx\",\n                            lineNumber: 158,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/PropertyPanel.tsx\",\n                    lineNumber: 133,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"border-t pt-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                            className: \"text-md font-medium text-gray-800 mb-4\",\n                            children: \"节点配置\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/PropertyPanel.tsx\",\n                            lineNumber: 173,\n                            columnNumber: 11\n                        }, undefined),\n                        node.type === 'led' && renderLedProperties()\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/PropertyPanel.tsx\",\n                    lineNumber: 172,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/PropertyPanel.tsx\",\n            lineNumber: 118,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/PropertyPanel.tsx\",\n        lineNumber: 117,\n        columnNumber: 5\n    }, undefined);\n};\n_c = PropertyPanel;\nvar _c;\n$RefreshReg$(_c, \"PropertyPanel\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/PropertyPanel.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/WorkflowCanvas.tsx":
/*!*******************************************!*\
  !*** ./src/components/WorkflowCanvas.tsx ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   WorkflowCanvas: () => (/* binding */ WorkflowCanvas)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _nodes_LedNode__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/nodes/LedNode */ \"(app-pages-browser)/./src/nodes/LedNode.ts\");\n/* harmony import */ var _nodes_LedNodeComponent__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./nodes/LedNodeComponent */ \"(app-pages-browser)/./src/components/nodes/LedNodeComponent.tsx\");\n/* harmony import */ var _PropertyPanel__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./PropertyPanel */ \"(app-pages-browser)/./src/components/PropertyPanel.tsx\");\n/* __next_internal_client_entry_do_not_use__ WorkflowCanvas auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst WorkflowCanvas = ()=>{\n    _s();\n    const [nodes, setNodes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedNode, setSelectedNode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showPropertyPanel, setShowPropertyPanel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const canvasRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const nodeInstancesRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(new Map());\n    // 创建新的LED节点\n    const createLedNode = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"WorkflowCanvas.useCallback[createLedNode]\": (position)=>{\n            const ledNode = new _nodes_LedNode__WEBPACK_IMPORTED_MODULE_2__.LedNode({\n                position\n            });\n            const nodeData = ledNode.getData();\n            setNodes({\n                \"WorkflowCanvas.useCallback[createLedNode]\": (prev)=>[\n                        ...prev,\n                        nodeData\n                    ]\n            }[\"WorkflowCanvas.useCallback[createLedNode]\"]);\n            nodeInstancesRef.current.set(nodeData.id, ledNode);\n            return nodeData;\n        }\n    }[\"WorkflowCanvas.useCallback[createLedNode]\"], []);\n    // 更新节点数据\n    const updateNode = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"WorkflowCanvas.useCallback[updateNode]\": (nodeId, updates)=>{\n            // 使用函数式更新减少重新渲染\n            setNodes({\n                \"WorkflowCanvas.useCallback[updateNode]\": (prev)=>{\n                    const nodeIndex = prev.findIndex({\n                        \"WorkflowCanvas.useCallback[updateNode].nodeIndex\": (node)=>node.id === nodeId\n                    }[\"WorkflowCanvas.useCallback[updateNode].nodeIndex\"]);\n                    if (nodeIndex === -1) return prev;\n                    const currentNode = prev[nodeIndex];\n                    const updatedNode = {\n                        ...currentNode,\n                        ...updates\n                    };\n                    // 检查是否真的有变化，避免不必要的更新\n                    if (JSON.stringify(currentNode) === JSON.stringify(updatedNode)) {\n                        return prev;\n                    }\n                    // 更新节点实例\n                    const nodeInstance = nodeInstancesRef.current.get(nodeId);\n                    if (nodeInstance) {\n                        nodeInstance.updateData(updatedNode);\n                        // 如果属性发生变化，触发处理\n                        if (updates.properties) {\n                            Object.entries(updates.properties).forEach({\n                                \"WorkflowCanvas.useCallback[updateNode]\": (param)=>{\n                                    let [key, value] = param;\n                                    nodeInstance.setProperty(key, value);\n                                }\n                            }[\"WorkflowCanvas.useCallback[updateNode]\"]);\n                        }\n                    }\n                    // 创建新数组，只更新变化的节点\n                    const newNodes = [\n                        ...prev\n                    ];\n                    newNodes[nodeIndex] = updatedNode;\n                    return newNodes;\n                }\n            }[\"WorkflowCanvas.useCallback[updateNode]\"]);\n            // 如果更新的是当前选中的节点，也更新选中状态\n            if ((selectedNode === null || selectedNode === void 0 ? void 0 : selectedNode.id) === nodeId) {\n                setSelectedNode({\n                    \"WorkflowCanvas.useCallback[updateNode]\": (prev)=>prev ? {\n                            ...prev,\n                            ...updates\n                        } : null\n                }[\"WorkflowCanvas.useCallback[updateNode]\"]);\n            }\n        }\n    }[\"WorkflowCanvas.useCallback[updateNode]\"], [\n        selectedNode\n    ]);\n    // 处理节点事件\n    const handleNodeEvent = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"WorkflowCanvas.useCallback[handleNodeEvent]\": (event)=>{\n            const nodeInstance = nodeInstancesRef.current.get(event.nodeId);\n            if (nodeInstance) {\n                nodeInstance.handleEvent(event);\n            }\n            switch(event.type){\n                case 'click':\n                    const clickedNode = nodes.find({\n                        \"WorkflowCanvas.useCallback[handleNodeEvent].clickedNode\": (n)=>n.id === event.nodeId\n                    }[\"WorkflowCanvas.useCallback[handleNodeEvent].clickedNode\"]);\n                    if (clickedNode) {\n                        // 取消其他节点的选中状态\n                        setNodes({\n                            \"WorkflowCanvas.useCallback[handleNodeEvent]\": (prev)=>prev.map({\n                                    \"WorkflowCanvas.useCallback[handleNodeEvent]\": (node)=>({\n                                            ...node,\n                                            selected: node.id === event.nodeId\n                                        })\n                                }[\"WorkflowCanvas.useCallback[handleNodeEvent]\"])\n                        }[\"WorkflowCanvas.useCallback[handleNodeEvent]\"]);\n                        setSelectedNode(clickedNode);\n                    }\n                    break;\n                case 'doubleClick':\n                    const doubleClickedNode = nodes.find({\n                        \"WorkflowCanvas.useCallback[handleNodeEvent].doubleClickedNode\": (n)=>n.id === event.nodeId\n                    }[\"WorkflowCanvas.useCallback[handleNodeEvent].doubleClickedNode\"]);\n                    if (doubleClickedNode) {\n                        setSelectedNode(doubleClickedNode);\n                        setShowPropertyPanel(true);\n                    }\n                    break;\n            }\n        }\n    }[\"WorkflowCanvas.useCallback[handleNodeEvent]\"], [\n        nodes\n    ]);\n    // 处理画布点击\n    const handleCanvasClick = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"WorkflowCanvas.useCallback[handleCanvasClick]\": (e)=>{\n            if (e.target === canvasRef.current) {\n                // 取消所有节点的选中状态\n                setNodes({\n                    \"WorkflowCanvas.useCallback[handleCanvasClick]\": (prev)=>prev.map({\n                            \"WorkflowCanvas.useCallback[handleCanvasClick]\": (node)=>({\n                                    ...node,\n                                    selected: false\n                                })\n                        }[\"WorkflowCanvas.useCallback[handleCanvasClick]\"])\n                }[\"WorkflowCanvas.useCallback[handleCanvasClick]\"]);\n                setSelectedNode(null);\n            }\n        }\n    }[\"WorkflowCanvas.useCallback[handleCanvasClick]\"], []);\n    // 处理画布双击 - 创建新节点\n    const handleCanvasDoubleClick = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"WorkflowCanvas.useCallback[handleCanvasDoubleClick]\": (e)=>{\n            if (e.target === canvasRef.current) {\n                const rect = canvasRef.current.getBoundingClientRect();\n                const position = {\n                    x: e.clientX - rect.left - 60,\n                    y: e.clientY - rect.top - 40\n                };\n                createLedNode(position);\n            }\n        }\n    }[\"WorkflowCanvas.useCallback[handleCanvasDoubleClick]\"], [\n        createLedNode\n    ]);\n    // 渲染节点\n    const renderNode = (node)=>{\n        switch(node.type){\n            case 'led':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_nodes_LedNodeComponent__WEBPACK_IMPORTED_MODULE_3__.LedNodeComponent, {\n                    node: node,\n                    onEvent: handleNodeEvent,\n                    onUpdateNode: updateNode\n                }, node.id, false, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/WorkflowCanvas.tsx\",\n                    lineNumber: 125,\n                    columnNumber: 11\n                }, undefined);\n            default:\n                return null;\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative w-full h-screen bg-gray-50 overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-4 left-4 z-10 bg-white rounded-lg shadow-md p-2 flex space-x-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>createLedNode({\n                                x: 100,\n                                y: 100\n                            }),\n                        className: \"px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors\",\n                        children: \"添加LED节点\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/WorkflowCanvas.tsx\",\n                        lineNumber: 141,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>{\n                            setNodes([]);\n                            nodeInstancesRef.current.clear();\n                            setSelectedNode(null);\n                            setShowPropertyPanel(false);\n                        },\n                        className: \"px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600 transition-colors\",\n                        children: \"清空画布\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/WorkflowCanvas.tsx\",\n                        lineNumber: 147,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/WorkflowCanvas.tsx\",\n                lineNumber: 140,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-4 right-4 z-10 bg-white rounded-lg shadow-md p-2\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-sm text-gray-600\",\n                    children: [\n                        \"节点数量: \",\n                        nodes.length,\n                        selectedNode && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-1\",\n                            children: [\n                                \"选中: \",\n                                selectedNode.name\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/WorkflowCanvas.tsx\",\n                            lineNumber: 165,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/WorkflowCanvas.tsx\",\n                    lineNumber: 162,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/WorkflowCanvas.tsx\",\n                lineNumber: 161,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: canvasRef,\n                className: \"w-full h-full relative cursor-crosshair\",\n                onClick: handleCanvasClick,\n                onDoubleClick: handleCanvasDoubleClick,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 opacity-20\",\n                        style: {\n                            backgroundImage: \"\\n              linear-gradient(to right, #e5e7eb 1px, transparent 1px),\\n              linear-gradient(to bottom, #e5e7eb 1px, transparent 1px)\\n            \",\n                            backgroundSize: '20px 20px'\n                        }\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/WorkflowCanvas.tsx\",\n                        lineNumber: 180,\n                        columnNumber: 9\n                    }, undefined),\n                    nodes.map(renderNode),\n                    nodes.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 flex items-center justify-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-gray-400 text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-lg mb-2\",\n                                    children: \"工作流画布\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/WorkflowCanvas.tsx\",\n                                    lineNumber: 198,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm\",\n                                    children: \"双击画布创建LED节点，或点击工具栏按钮\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/WorkflowCanvas.tsx\",\n                                    lineNumber: 199,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/WorkflowCanvas.tsx\",\n                            lineNumber: 197,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/WorkflowCanvas.tsx\",\n                        lineNumber: 196,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/WorkflowCanvas.tsx\",\n                lineNumber: 173,\n                columnNumber: 7\n            }, undefined),\n            showPropertyPanel && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_PropertyPanel__WEBPACK_IMPORTED_MODULE_4__.PropertyPanel, {\n                node: selectedNode,\n                onUpdateNode: updateNode,\n                onClose: ()=>setShowPropertyPanel(false)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/WorkflowCanvas.tsx\",\n                lineNumber: 207,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/WorkflowCanvas.tsx\",\n        lineNumber: 138,\n        columnNumber: 5\n    }, undefined);\n};\n_s(WorkflowCanvas, \"kOpDbL+UmWF6QzImBs/zcNJv4Kg=\");\n_c = WorkflowCanvas;\nvar _c;\n$RefreshReg$(_c, \"WorkflowCanvas\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/WorkflowCanvas.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/nodes/BaseNodeComponent.tsx":
/*!****************************************************!*\
  !*** ./src/components/nodes/BaseNodeComponent.tsx ***!
  \****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BaseNodeComponent: () => (/* binding */ BaseNodeComponent)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ BaseNodeComponent auto */ \nvar _s = $RefreshSig$();\n\nconst BaseNodeComponent = (param)=>{\n    let { node, onEvent, onUpdateNode, children } = param;\n    _s();\n    const [isDragging, setIsDragging] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [dragOffset, setDragOffset] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        x: 0,\n        y: 0\n    });\n    const nodeRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const animationFrameRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const pendingUpdateRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // 计算引脚位置\n    const calculatePinPositions = ()=>{\n        const inputPins = node.pins.filter((pin)=>pin.type === 'input');\n        const outputPins = node.pins.filter((pin)=>pin.type === 'output');\n        const maxPins = Math.max(inputPins.length, outputPins.length);\n        // 标题栏高度约为 32px (包含padding和border)\n        const titleHeight = 32;\n        // 底部留白\n        const bottomMargin = 8;\n        // 可用于引脚的高度\n        const availableHeight = node.size.height - titleHeight - bottomMargin;\n        // 引脚间距，最小16px\n        const pinSpacing = Math.max(16, availableHeight / Math.max(1, maxPins - 1));\n        return {\n            inputPins,\n            outputPins,\n            titleHeight,\n            pinSpacing,\n            availableHeight\n        };\n    };\n    const { inputPins, outputPins, titleHeight, pinSpacing } = calculatePinPositions();\n    // 使用requestAnimationFrame节流更新\n    const flushPendingUpdate = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"BaseNodeComponent.useCallback[flushPendingUpdate]\": ()=>{\n            if (pendingUpdateRef.current) {\n                onUpdateNode(node.id, {\n                    position: pendingUpdateRef.current\n                });\n                pendingUpdateRef.current = null;\n            }\n            animationFrameRef.current = null;\n        }\n    }[\"BaseNodeComponent.useCallback[flushPendingUpdate]\"], [\n        onUpdateNode,\n        node.id\n    ]);\n    const handleMouseMove = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"BaseNodeComponent.useCallback[handleMouseMove]\": (e)=>{\n            const newPosition = {\n                x: e.clientX - dragOffset.x,\n                y: e.clientY - dragOffset.y\n            };\n            // 立即更新DOM位置以获得流畅的视觉效果\n            if (nodeRef.current) {\n                nodeRef.current.style.left = \"\".concat(newPosition.x, \"px\");\n                nodeRef.current.style.top = \"\".concat(newPosition.y, \"px\");\n            }\n            // 节流状态更新\n            pendingUpdateRef.current = newPosition;\n            if (animationFrameRef.current === null) {\n                animationFrameRef.current = requestAnimationFrame(flushPendingUpdate);\n            }\n        }\n    }[\"BaseNodeComponent.useCallback[handleMouseMove]\"], [\n        dragOffset.x,\n        dragOffset.y,\n        flushPendingUpdate\n    ]);\n    const handleMouseUp = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"BaseNodeComponent.useCallback[handleMouseUp]\": ()=>{\n            setIsDragging(false);\n            // 清理动画帧\n            if (animationFrameRef.current) {\n                cancelAnimationFrame(animationFrameRef.current);\n                animationFrameRef.current = null;\n            }\n            // 确保最后的位置更新被应用\n            if (pendingUpdateRef.current) {\n                onUpdateNode(node.id, {\n                    position: pendingUpdateRef.current\n                });\n                pendingUpdateRef.current = null;\n            }\n            onEvent({\n                type: 'dragEnd',\n                nodeId: node.id\n            });\n            // 移除全局鼠标事件监听\n            document.removeEventListener('mousemove', handleMouseMove);\n            document.removeEventListener('mouseup', handleMouseUp);\n        }\n    }[\"BaseNodeComponent.useCallback[handleMouseUp]\"], [\n        handleMouseMove,\n        onEvent,\n        onUpdateNode,\n        node.id\n    ]);\n    const handleMouseDown = (e)=>{\n        var _nodeRef_current;\n        if (e.button !== 0) return; // 只处理左键\n        // 防止在引脚上开始拖拽\n        const target = e.target;\n        if (target.closest('.pin-connector')) {\n            return;\n        }\n        e.preventDefault();\n        e.stopPropagation();\n        const rect = (_nodeRef_current = nodeRef.current) === null || _nodeRef_current === void 0 ? void 0 : _nodeRef_current.getBoundingClientRect();\n        if (rect) {\n            setDragOffset({\n                x: e.clientX - rect.left,\n                y: e.clientY - rect.top\n            });\n        }\n        setIsDragging(true);\n        onEvent({\n            type: 'dragStart',\n            nodeId: node.id,\n            data: {\n                position: node.position\n            }\n        });\n        // 添加全局鼠标事件监听\n        document.addEventListener('mousemove', handleMouseMove);\n        document.addEventListener('mouseup', handleMouseUp);\n    };\n    const handleClick = (e)=>{\n        e.stopPropagation();\n        onEvent({\n            type: 'click',\n            nodeId: node.id\n        });\n    };\n    const handleDoubleClick = (e)=>{\n        e.stopPropagation();\n        onEvent({\n            type: 'doubleClick',\n            nodeId: node.id\n        });\n    };\n    const getStatusColor = ()=>{\n        switch(node.status){\n            case 'running':\n                return 'border-yellow-400 bg-yellow-50';\n            case 'success':\n                return 'border-green-400 bg-green-50';\n            case 'error':\n                return 'border-red-400 bg-red-50';\n            case 'warning':\n                return 'border-orange-400 bg-orange-50';\n            default:\n                return 'border-gray-300 bg-white';\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: nodeRef,\n        className: \"absolute border-2 rounded-lg shadow-md cursor-move select-none transition-all duration-200 \".concat(getStatusColor(), \" \").concat(node.selected ? 'ring-2 ring-blue-400' : '', \" \").concat(isDragging ? 'shadow-lg scale-105' : ''),\n        style: {\n            left: node.position.x,\n            top: node.position.y,\n            width: node.size.width,\n            height: node.size.height\n        },\n        onMouseDown: handleMouseDown,\n        onClick: handleClick,\n        onDoubleClick: handleDoubleClick,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-2 py-1 bg-gray-100 border-b border-gray-200 rounded-t-md\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-sm font-medium text-gray-800 truncate\",\n                        children: node.name\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/nodes/BaseNodeComponent.tsx\",\n                        lineNumber: 185,\n                        columnNumber: 9\n                    }, undefined),\n                    node.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-gray-500 truncate\",\n                        children: node.description\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/nodes/BaseNodeComponent.tsx\",\n                        lineNumber: 189,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/nodes/BaseNodeComponent.tsx\",\n                lineNumber: 184,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-2 flex-1\",\n                children: children\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/nodes/BaseNodeComponent.tsx\",\n                lineNumber: 196,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute left-0\",\n                style: {\n                    top: titleHeight\n                },\n                children: inputPins.map((pin, index)=>{\n                    const pinY = index * pinSpacing;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute flex items-center\",\n                        style: {\n                            top: pinY,\n                            left: -6,\n                            transform: 'translateY(-50%)' // 垂直居中对齐\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"pin-connector w-3 h-3 rounded-full border-2 cursor-pointer transition-colors \".concat(pin.connected ? 'bg-blue-500 border-blue-600' : 'bg-white border-gray-400 hover:border-blue-400'),\n                                title: pin.name\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/nodes/BaseNodeComponent.tsx\",\n                                lineNumber: 214,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"absolute left-4 top-1/2 transform -translate-y-1/2 text-xs text-gray-600 whitespace-nowrap\",\n                                children: pin.name\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/nodes/BaseNodeComponent.tsx\",\n                                lineNumber: 222,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, pin.id, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/nodes/BaseNodeComponent.tsx\",\n                        lineNumber: 205,\n                        columnNumber: 13\n                    }, undefined);\n                })\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/nodes/BaseNodeComponent.tsx\",\n                lineNumber: 201,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute right-0\",\n                style: {\n                    top: titleHeight\n                },\n                children: outputPins.map((pin, index)=>{\n                    const pinY = index * pinSpacing;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute flex items-center justify-end\",\n                        style: {\n                            top: pinY,\n                            right: -6,\n                            transform: 'translateY(-50%)' // 垂直居中对齐\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"absolute right-4 top-1/2 transform -translate-y-1/2 text-xs text-gray-600 whitespace-nowrap\",\n                                children: pin.name\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/nodes/BaseNodeComponent.tsx\",\n                                lineNumber: 244,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"pin-connector w-3 h-3 rounded-full border-2 cursor-pointer transition-colors \".concat(pin.connected ? 'bg-green-500 border-green-600' : 'bg-white border-gray-400 hover:border-green-400'),\n                                title: pin.name\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/nodes/BaseNodeComponent.tsx\",\n                                lineNumber: 247,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, pin.id, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/nodes/BaseNodeComponent.tsx\",\n                        lineNumber: 235,\n                        columnNumber: 13\n                    }, undefined);\n                })\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/nodes/BaseNodeComponent.tsx\",\n                lineNumber: 231,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/nodes/BaseNodeComponent.tsx\",\n        lineNumber: 166,\n        columnNumber: 5\n    }, undefined);\n};\n_s(BaseNodeComponent, \"7O/WCX1jRM3DSG4/td1hT8DSAmU=\");\n_c = BaseNodeComponent;\nvar _c;\n$RefreshReg$(_c, \"BaseNodeComponent\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/nodes/BaseNodeComponent.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/nodes/LedNodeComponent.tsx":
/*!***************************************************!*\
  !*** ./src/components/nodes/LedNodeComponent.tsx ***!
  \***************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LedNodeComponent: () => (/* binding */ LedNodeComponent)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _BaseNodeComponent__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./BaseNodeComponent */ \"(app-pages-browser)/./src/components/nodes/BaseNodeComponent.tsx\");\n/* __next_internal_client_entry_do_not_use__ LedNodeComponent auto */ \n\n\nconst LedNodeComponent = (param)=>{\n    let { node, onEvent, onUpdateNode } = param;\n    const properties = node.properties;\n    const redValue = properties.redValue || 0;\n    const greenValue = properties.greenValue || 0;\n    const blueValue = properties.blueValue || 0;\n    // 生成LED显示颜色\n    const ledColor = \"rgb(\".concat(redValue, \", \").concat(greenValue, \", \").concat(blueValue, \")\");\n    const isOn = redValue > 0 || greenValue > 0 || blueValue > 0;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_BaseNodeComponent__WEBPACK_IMPORTED_MODULE_2__.BaseNodeComponent, {\n        node: node,\n        onEvent: onEvent,\n        onUpdateNode: onUpdateNode,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center h-full\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-8 h-8 rounded-full border-2 transition-all duration-300 \".concat(isOn ? 'border-gray-400 shadow-lg' : 'border-gray-300'),\n                            style: {\n                                backgroundColor: isOn ? ledColor : '#f3f4f6',\n                                boxShadow: isOn ? \"0 0 10px \".concat(ledColor, \", 0 0 20px \").concat(ledColor, \"40\") : 'none'\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-4 h-4 rounded-full absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2\",\n                                style: {\n                                    backgroundColor: isOn ? \"rgba(\".concat(redValue, \", \").concat(greenValue, \", \").concat(blueValue, \", 0.8)\") : '#e5e7eb'\n                                }\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/nodes/LedNodeComponent.tsx\",\n                                lineNumber: 46,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/nodes/LedNodeComponent.tsx\",\n                            lineNumber: 32,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute -bottom-1 -right-1\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-2 h-2 rounded-full \".concat(isOn ? 'bg-green-400' : 'bg-gray-300')\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/nodes/LedNodeComponent.tsx\",\n                                lineNumber: 58,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/nodes/LedNodeComponent.tsx\",\n                            lineNumber: 57,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/nodes/LedNodeComponent.tsx\",\n                    lineNumber: 30,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/nodes/LedNodeComponent.tsx\",\n                lineNumber: 28,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute bottom-1 left-1 right-1\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-xs text-center text-gray-600 bg-white bg-opacity-75 rounded px-1\",\n                    children: [\n                        properties.colorMode === '1color' && \"R:\".concat(redValue),\n                        properties.colorMode === '2color' && \"R:\".concat(redValue, \" G:\").concat(greenValue),\n                        properties.colorMode === '3color' && \"R:\".concat(redValue, \" G:\").concat(greenValue, \" B:\").concat(blueValue)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/nodes/LedNodeComponent.tsx\",\n                    lineNumber: 69,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/nodes/LedNodeComponent.tsx\",\n                lineNumber: 68,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/nodes/LedNodeComponent.tsx\",\n        lineNumber: 23,\n        columnNumber: 5\n    }, undefined);\n};\n_c = LedNodeComponent;\nvar _c;\n$RefreshReg$(_c, \"LedNodeComponent\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/nodes/LedNodeComponent.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/nodes/BaseNode.ts":
/*!*******************************!*\
  !*** ./src/nodes/BaseNode.ts ***!
  \*******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BaseNode: () => (/* binding */ BaseNode)\n/* harmony export */ });\nclass BaseNode {\n    // 生成唯一ID\n    generateId() {\n        return \"node_\".concat(Date.now(), \"_\").concat(Math.random().toString(36).substr(2, 9));\n    }\n    // 获取节点数据\n    getData() {\n        return {\n            ...this.data\n        };\n    }\n    // 更新节点数据\n    updateData(updates) {\n        this.data = {\n            ...this.data,\n            ...updates\n        };\n    }\n    // 设置节点状态\n    setStatus(status) {\n        this.data.status = status;\n    }\n    // 添加引脚\n    addPin(pin) {\n        this.data.pins.push(pin);\n    }\n    // 移除引脚\n    removePin(pinId) {\n        this.data.pins = this.data.pins.filter((pin)=>pin.id !== pinId);\n    }\n    // 获取引脚\n    getPin(pinId) {\n        return this.data.pins.find((pin)=>pin.id === pinId);\n    }\n    // 设置引脚值\n    setPinValue(pinId, value) {\n        const pin = this.getPin(pinId);\n        if (pin) {\n            pin.value = value;\n            this.onPinValueChanged(pinId, value);\n        }\n    }\n    // 获取引脚值\n    getPinValue(pinId) {\n        const pin = this.getPin(pinId);\n        return pin === null || pin === void 0 ? void 0 : pin.value;\n    }\n    // 设置属性\n    setProperty(key, value) {\n        this.data.properties[key] = value;\n        this.onPropertyChanged(key, value);\n    }\n    // 获取属性\n    getProperty(key) {\n        return this.data.properties[key];\n    }\n    // 处理事件\n    handleEvent(event) {\n        switch(event.type){\n            case 'click':\n                this.onClick(event);\n                break;\n            case 'doubleClick':\n                this.onDoubleClick(event);\n                break;\n            case 'dragStart':\n                this.onDragStart(event);\n                break;\n            case 'drag':\n                this.onDrag(event);\n                break;\n            case 'dragEnd':\n                this.onDragEnd(event);\n                break;\n            case 'pinConnect':\n                this.onPinConnect(event);\n                break;\n            case 'pinDisconnect':\n                this.onPinDisconnect(event);\n                break;\n            case 'propertyChange':\n                this.onPropertyChange(event);\n                break;\n        }\n    }\n    // 事件处理方法 - 子类可以重写\n    onClick(event) {}\n    onDoubleClick(event) {}\n    onDragStart(event) {\n        this.data.dragging = true;\n    }\n    onDrag(event) {\n        if (event.data && this.data.dragging) {\n            this.data.position = event.data.position;\n        }\n    }\n    onDragEnd(event) {\n        this.data.dragging = false;\n    }\n    onPinConnect(event) {}\n    onPinDisconnect(event) {}\n    onPropertyChange(event) {}\n    onPinValueChanged(pinId, value) {\n        // 当引脚值改变时触发处理\n        this.process();\n    }\n    onPropertyChanged(key, value) {\n        // 当属性改变时可能需要重新配置引脚\n        this.updatePinsConfiguration();\n    }\n    // 更新引脚配置 - 子类可以重写\n    updatePinsConfiguration() {\n        // 更新引脚配置后，重新计算节点高度\n        this.updateNodeSize();\n    }\n    // 根据引脚数量计算并更新节点尺寸\n    updateNodeSize() {\n        const inputPins = this.data.pins.filter((pin)=>pin.type === 'input');\n        const outputPins = this.data.pins.filter((pin)=>pin.type === 'output');\n        const maxPins = Math.max(inputPins.length, outputPins.length);\n        // 基础高度：标题栏(32px) + 内容区域最小高度(60px) + 底部边距(8px)\n        const baseHeight = 100;\n        // 每个引脚需要的最小间距\n        const pinSpacing = 20;\n        // 根据引脚数量计算需要的额外高度\n        const pinsHeight = Math.max(0, (maxPins - 1) * pinSpacing);\n        const newHeight = baseHeight + pinsHeight;\n        // 只有当高度发生变化时才更新\n        if (this.data.size.height !== newHeight) {\n            this.data.size.height = newHeight;\n        }\n    }\n    constructor(data){\n        this.data = {\n            id: data.id || this.generateId(),\n            type: data.type || 'base',\n            name: data.name || 'Base Node',\n            description: data.description || '',\n            position: data.position || {\n                x: 0,\n                y: 0\n            },\n            size: data.size || {\n                width: 200,\n                height: 100\n            },\n            status: data.status || 'idle',\n            pins: data.pins || [],\n            properties: data.properties || {},\n            selected: data.selected || false,\n            dragging: data.dragging || false\n        };\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/nodes/BaseNode.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/nodes/LedNode.ts":
/*!******************************!*\
  !*** ./src/nodes/LedNode.ts ***!
  \******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LedNode: () => (/* binding */ LedNode)\n/* harmony export */ });\n/* harmony import */ var _BaseNode__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./BaseNode */ \"(app-pages-browser)/./src/nodes/BaseNode.ts\");\n\nclass LedNode extends _BaseNode__WEBPACK_IMPORTED_MODULE_0__.BaseNode {\n    initializePins() {\n        this.updatePinsConfiguration();\n    }\n    updatePinsConfiguration() {\n        // 清除现有引脚\n        this.data.pins = [];\n        const colorMode = this.getProperty('colorMode');\n        const inputRange = this.getProperty('inputRange');\n        // 根据颜色模式添加引脚\n        if (colorMode === '1color' || colorMode === '2color' || colorMode === '3color') {\n            this.addPin({\n                id: 'red_input',\n                name: 'R',\n                type: 'input',\n                dataType: inputRange === 'boolean' ? 'boolean' : 'number',\n                value: 0\n            });\n        }\n        if (colorMode === '2color' || colorMode === '3color') {\n            this.addPin({\n                id: 'green_input',\n                name: 'G',\n                type: 'input',\n                dataType: inputRange === 'boolean' ? 'boolean' : 'number',\n                value: 0\n            });\n        }\n        if (colorMode === '3color') {\n            this.addPin({\n                id: 'blue_input',\n                name: 'B',\n                type: 'input',\n                dataType: inputRange === 'boolean' ? 'boolean' : 'number',\n                value: 0\n            });\n        }\n        // 添加亮度控制引脚\n        this.addPin({\n            id: 'brightness_input',\n            name: 'Brightness',\n            type: 'input',\n            dataType: 'number',\n            value: 100\n        });\n        // 添加输出引脚\n        this.addPin({\n            id: 'color_output',\n            name: 'Color',\n            type: 'output',\n            dataType: 'color',\n            value: '#000000'\n        });\n    }\n    async process() {\n        try {\n            this.setStatus('running');\n            const colorMode = this.getProperty('colorMode');\n            const inputRange = this.getProperty('inputRange');\n            const brightness = this.getPinValue('brightness_input') || this.getProperty('brightness') || 100;\n            let red = 0, green = 0, blue = 0;\n            // 获取输入值\n            if (colorMode === '1color' || colorMode === '2color' || colorMode === '3color') {\n                red = this.normalizeInputValue(this.getPinValue('red_input') || 0, inputRange);\n            }\n            if (colorMode === '2color' || colorMode === '3color') {\n                green = this.normalizeInputValue(this.getPinValue('green_input') || 0, inputRange);\n            }\n            if (colorMode === '3color') {\n                blue = this.normalizeInputValue(this.getPinValue('blue_input') || 0, inputRange);\n            }\n            // 应用亮度\n            const brightnessMultiplier = Math.max(0, Math.min(100, brightness)) / 100;\n            red = Math.round(red * brightnessMultiplier);\n            green = Math.round(green * brightnessMultiplier);\n            blue = Math.round(blue * brightnessMultiplier);\n            // 更新属性值\n            this.setProperty('redValue', red);\n            this.setProperty('greenValue', green);\n            this.setProperty('blueValue', blue);\n            // 生成颜色输出\n            const colorHex = \"#\".concat(red.toString(16).padStart(2, '0')).concat(green.toString(16).padStart(2, '0')).concat(blue.toString(16).padStart(2, '0'));\n            this.setPinValue('color_output', colorHex);\n            this.setStatus('success');\n        } catch (error) {\n            console.error('LED Node processing error:', error);\n            this.setStatus('error');\n        }\n    }\n    normalizeInputValue(value, inputRange) {\n        if (typeof value === 'boolean') {\n            return value ? 255 : 0;\n        }\n        const numValue = Number(value) || 0;\n        switch(inputRange){\n            case 'percentage':\n                return Math.round(Math.max(0, Math.min(100, numValue)) * 2.55);\n            case '0-255':\n                return Math.round(Math.max(0, Math.min(255, numValue)));\n            case '0-1':\n                return Math.round(Math.max(0, Math.min(1, numValue)) * 255);\n            case 'boolean':\n                return numValue > 0 ? 255 : 0;\n            default:\n                return Math.round(Math.max(0, Math.min(255, numValue)));\n        }\n    }\n    onPropertyChanged(key, value) {\n        if (key === 'colorMode' || key === 'inputRange') {\n            this.updatePinsConfiguration();\n        }\n        super.onPropertyChanged(key, value);\n    }\n    constructor(data){\n        super({\n            type: 'led',\n            name: 'RGB LED',\n            description: '3色变色LED灯节点',\n            size: {\n                width: 140,\n                height: 100\n            },\n            properties: {\n                colorMode: '3color',\n                inputRange: 'percentage',\n                redValue: 0,\n                greenValue: 0,\n                blueValue: 0,\n                brightness: 100\n            },\n            ...data\n        });\n        this.initializePins();\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/nodes/LedNode.ts\n"));

/***/ })

},
/******/ __webpack_require__ => { // webpackRuntimeModules
/******/ var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
/******/ __webpack_require__.O(0, ["main-app"], () => (__webpack_exec__("(app-pages-browser)/./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fuser%2FDocuments%2Faugment-projects%2Fnextjs-superNode%2Fsrc%2Fcomponents%2FWorkflowCanvas.tsx%22%2C%22ids%22%3A%5B%22WorkflowCanvas%22%5D%7D&server=false!")));
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);