"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_src_nodes_controls_KnobNode_tsx"],{

/***/ "(app-pages-browser)/./src/base/BaseNode.ts":
/*!******************************!*\
  !*** ./src/base/BaseNode.ts ***!
  \******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BaseNode: () => (/* binding */ BaseNode)\n/* harmony export */ });\nclass BaseNode {\n    // --- Static method for property panel rendering ---\n    static renderProperties(props) {\n        return null; // Default implementation\n    }\n    // --- Core Methods ---\n    generateId() {\n        return \"node_\".concat(Date.now(), \"_\").concat(Math.random().toString(36).substr(2, 9));\n    }\n    getData() {\n        return {\n            ...this.data\n        };\n    }\n    updateData(updates) {\n        this.data = {\n            ...this.data,\n            ...updates\n        };\n    }\n    setStatus(status) {\n        this.data.status = status;\n    }\n    addPin(pin) {\n        this.data.pins.push(pin);\n    }\n    removePin(pinId) {\n        this.data.pins = this.data.pins.filter((pin)=>pin.id !== pinId);\n    }\n    getPin(pinId) {\n        return this.data.pins.find((pin)=>pin.id === pinId);\n    }\n    setPinValue(pinId, value) {\n        const pin = this.getPin(pinId);\n        if (pin) {\n            pin.value = value;\n            this.onPinValueChanged(pinId, value);\n        }\n    }\n    getPinValue(pinId) {\n        const pin = this.getPin(pinId);\n        return pin === null || pin === void 0 ? void 0 : pin.value;\n    }\n    getInputValue(pinId) {\n        const pin = this.getPin(pinId);\n        if (!pin || pin.type !== 'input') {\n            return undefined;\n        }\n        if (pin.connected) {\n            return pin.value;\n        }\n        var _pin_defaultValue;\n        return (_pin_defaultValue = pin.defaultValue) !== null && _pin_defaultValue !== void 0 ? _pin_defaultValue : pin.value;\n    }\n    setProperty(key, value) {\n        this.data.properties[key] = value;\n        this.onPropertyChanged(key, value);\n    }\n    getProperty(key) {\n        return this.data.properties[key];\n    }\n    handleEvent(event) {\n        switch(event.type){\n            case 'click':\n                this.onClick(event);\n                break;\n            case 'doubleClick':\n                this.onDoubleClick(event);\n                break;\n            case 'dragStart':\n                this.onDragStart(event);\n                break;\n            case 'drag':\n                this.onDrag(event);\n                break;\n            case 'dragEnd':\n                this.onDragEnd(event);\n                break;\n            case 'pinConnect':\n                this.onPinConnect(event);\n                break;\n            case 'pinDisconnect':\n                this.onPinDisconnect(event);\n                break;\n            case 'propertyChange':\n                if (event.data) {\n                    this.onPropertyChanged(event.data.key, event.data.value);\n                }\n                break;\n            case 'testRun':\n                this.process();\n                break;\n        }\n    }\n    // --- Event Handlers (for overriding) ---\n    onClick(event) {}\n    onDoubleClick(event) {}\n    onDragStart(event) {\n        this.data.dragging = true;\n    }\n    onDrag(event) {\n        if (event.data && this.data.dragging) {\n            this.data.position = event.data.position;\n        }\n    }\n    onDragEnd(event) {\n        this.data.dragging = false;\n    }\n    onPinConnect(event) {}\n    onPinDisconnect(event) {}\n    onPropertyChanged(key, value) {\n        this.updatePinsConfiguration();\n    }\n    onPinValueChanged(pinId, value) {\n        const pin = this.getPin(pinId);\n        if (pin && pin.type === 'input') {\n            this.process();\n        }\n    }\n    updatePinsConfiguration() {\n        return this.updateNodeSize();\n    }\n    updateNodeSize() {\n        const inputPins = this.data.pins.filter((pin)=>pin.type === 'input');\n        const outputPins = this.data.pins.filter((pin)=>pin.type === 'output');\n        const maxPins = Math.max(inputPins.length, outputPins.length);\n        const titleHeight = 37;\n        const minContentHeight = 40;\n        const pinPadding = 20;\n        const pinSlotHeight = 28;\n        let contentHeight = minContentHeight;\n        if (maxPins > 0) {\n            const requiredPinsHeight = maxPins * pinSlotHeight + pinPadding;\n            contentHeight = Math.max(minContentHeight, requiredPinsHeight);\n        }\n        // If the node has fixed size, don't update it automatically\n        if (this.data.size.width && this.data.size.height && !this.data.properties.dynamicSize) {\n            // Special case for fixed-size nodes that might still need to adjust if pins overflow\n            const requiredHeight = titleHeight + contentHeight;\n            if (this.data.size.height < requiredHeight) {\n                this.data.size.height = requiredHeight;\n                return true;\n            }\n            return false;\n        }\n        const newHeight = titleHeight + contentHeight;\n        if (this.data.size.height !== newHeight) {\n            this.data.size.height = newHeight;\n            return true;\n        }\n        return false;\n    }\n    constructor(data){\n        this.data = {\n            id: data.id || this.generateId(),\n            type: data.type,\n            name: data.name || 'Base Node',\n            icon: data.icon || '❓',\n            color: data.color || 'gray',\n            description: data.description || '',\n            position: data.position || {\n                x: 0,\n                y: 0\n            },\n            size: data.size || {\n                width: 200,\n                height: 100\n            },\n            status: data.status || 'idle',\n            pins: data.pins || [],\n            properties: data.properties || {},\n            selected: data.selected || false,\n            dragging: data.dragging || false\n        };\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/base/BaseNode.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/nodes/controls/KnobNode.tsx":
/*!*****************************************!*\
  !*** ./src/nodes/controls/KnobNode.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   KnobNode: () => (/* binding */ KnobNode)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _base_BaseNode__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../base/BaseNode */ \"(app-pages-browser)/./src/base/BaseNode.ts\");\n\n\n\nclass KnobNode extends _base_BaseNode__WEBPACK_IMPORTED_MODULE_2__.BaseNode {\n    initializePins() {\n        this.updatePinsConfiguration();\n    }\n    updatePinsConfiguration() {\n        const value = this.getProperty('value');\n        this.data.pins = [\n            {\n                id: 'value_output',\n                name: 'Value',\n                type: 'output',\n                dataType: 'number',\n                value\n            }\n        ];\n        return super.updatePinsConfiguration();\n    }\n    async process() {\n        this.setStatus('running');\n        const value = this.getProperty('value');\n        this.setPinValue('value_output', value);\n        this.setStatus('success');\n    }\n    onPropertyChanged(key, value) {\n        if (key === 'value' || key === 'outputRange') {\n            this.process();\n        }\n        super.onPropertyChanged(key, value);\n    }\n    static renderProperties(param) {\n        let { node, onUpdateNode } = param;\n        const properties = node.properties;\n        const handlePropertyChange = (key, value)=>{\n            onUpdateNode(node.id, {\n                properties: {\n                    ...node.properties,\n                    [key]: value\n                }\n            });\n        };\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-4\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                        children: \"输出范围\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/nodes/controls/KnobNode.tsx\",\n                        lineNumber: 64,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                        value: properties.outputRange || 'percentage',\n                        onChange: (e)=>handlePropertyChange('outputRange', e.target.value),\n                        className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                value: \"percentage\",\n                                children: \"百分比 (0-100)\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/nodes/controls/KnobNode.tsx\",\n                                lineNumber: 72,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                value: \"0-255\",\n                                children: \"0-255\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/nodes/controls/KnobNode.tsx\",\n                                lineNumber: 73,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                value: \"0-1\",\n                                children: \"0-1\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/nodes/controls/KnobNode.tsx\",\n                                lineNumber: 74,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                value: \"boolean\",\n                                children: \"布尔值\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/nodes/controls/KnobNode.tsx\",\n                                lineNumber: 75,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/nodes/controls/KnobNode.tsx\",\n                        lineNumber: 67,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/nodes/controls/KnobNode.tsx\",\n                lineNumber: 63,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/nodes/controls/KnobNode.tsx\",\n            lineNumber: 62,\n            columnNumber: 7\n        }, this);\n    }\n    constructor(data){\n        super({\n            type: 'knob',\n            name: '旋钮',\n            icon: '🎛️',\n            color: 'blue',\n            description: '一个可以调节数值的旋钮',\n            properties: {\n                value: 50,\n                outputRange: 'percentage'\n            },\n            size: {\n                width: 160,\n                height: 120\n            },\n            ...data\n        });\n        this.initializePins();\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/nodes/controls/KnobNode.tsx\n"));

/***/ })

}]);