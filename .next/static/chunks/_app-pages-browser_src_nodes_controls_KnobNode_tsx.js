"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_src_nodes_controls_KnobNode_tsx"],{

/***/ "(app-pages-browser)/./src/base/BaseNode.ts":
/*!******************************!*\
  !*** ./src/base/BaseNode.ts ***!
  \******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BaseNode: () => (/* binding */ BaseNode)\n/* harmony export */ });\nclass BaseNode {\n    // --- Static method for property panel rendering ---\n    static renderProperties(props) {\n        return null; // Default implementation\n    }\n    // --- Core Methods ---\n    generateId() {\n        return \"node_\".concat(Date.now(), \"_\").concat(Math.random().toString(36).substr(2, 9));\n    }\n    getData() {\n        return {\n            ...this.data\n        };\n    }\n    updateData(updates) {\n        this.data = {\n            ...this.data,\n            ...updates\n        };\n    }\n    setStatus(status) {\n        this.data.status = status;\n    }\n    addPin(pin) {\n        this.data.pins.push(pin);\n    }\n    removePin(pinId) {\n        this.data.pins = this.data.pins.filter((pin)=>pin.id !== pinId);\n    }\n    getPin(pinId) {\n        return this.data.pins.find((pin)=>pin.id === pinId);\n    }\n    setPinValue(pinId, value) {\n        const pin = this.getPin(pinId);\n        if (pin) {\n            pin.value = value;\n            this.onPinValueChanged(pinId, value);\n        }\n    }\n    getPinValue(pinId) {\n        const pin = this.getPin(pinId);\n        return pin === null || pin === void 0 ? void 0 : pin.value;\n    }\n    getInputValue(pinId) {\n        const pin = this.getPin(pinId);\n        if (!pin || pin.type !== 'input') {\n            return undefined;\n        }\n        if (pin.connected) {\n            return pin.value;\n        }\n        var _pin_defaultValue;\n        return (_pin_defaultValue = pin.defaultValue) !== null && _pin_defaultValue !== void 0 ? _pin_defaultValue : pin.value;\n    }\n    setProperty(key, value) {\n        this.data.properties[key] = value;\n        this.onPropertyChanged(key, value);\n    }\n    getProperty(key) {\n        return this.data.properties[key];\n    }\n    handleEvent(event) {\n        switch(event.type){\n            case 'click':\n                this.onClick(event);\n                break;\n            case 'doubleClick':\n                this.onDoubleClick(event);\n                break;\n            case 'dragStart':\n                this.onDragStart(event);\n                break;\n            case 'drag':\n                this.onDrag(event);\n                break;\n            case 'dragEnd':\n                this.onDragEnd(event);\n                break;\n            case 'pinConnect':\n                this.onPinConnect(event);\n                break;\n            case 'pinDisconnect':\n                this.onPinDisconnect(event);\n                break;\n            case 'propertyChange':\n                if (event.data) {\n                    this.onPropertyChanged(event.data.key, event.data.value);\n                }\n                break;\n            case 'testRun':\n                this.process();\n                break;\n        }\n    }\n    // --- Event Handlers (for overriding) ---\n    onClick(event) {}\n    onDoubleClick(event) {}\n    onDragStart(event) {\n        this.data.dragging = true;\n    }\n    onDrag(event) {\n        if (event.data && this.data.dragging) {\n            this.data.position = event.data.position;\n        }\n    }\n    onDragEnd(event) {\n        this.data.dragging = false;\n    }\n    onPinConnect(event) {}\n    onPinDisconnect(event) {}\n    onPropertyChanged(key, value) {\n        this.updatePinsConfiguration();\n    }\n    onPinValueChanged(pinId, value) {\n        const pin = this.getPin(pinId);\n        if (pin && pin.type === 'input') {\n            this.process();\n        }\n    }\n    updatePinsConfiguration() {\n        return this.updateNodeSize();\n    }\n    updateNodeSize() {\n        const inputPins = this.data.pins.filter((pin)=>pin.type === 'input');\n        const outputPins = this.data.pins.filter((pin)=>pin.type === 'output');\n        const maxPins = Math.max(inputPins.length, outputPins.length);\n        const titleHeight = 37;\n        const minContentHeight = 40;\n        const pinPadding = 20;\n        const pinSlotHeight = 28;\n        let contentHeight = minContentHeight;\n        if (maxPins > 0) {\n            const requiredPinsHeight = maxPins * pinSlotHeight + pinPadding;\n            contentHeight = Math.max(minContentHeight, requiredPinsHeight);\n        }\n        // For nodes with dynamic sizing enabled or when pins change, always update height\n        // Only skip auto-sizing if explicitly disabled AND no pin configuration changes\n        if (this.data.size.width && this.data.size.height && this.data.properties.dynamicSize === false) {\n            // Even for fixed-size nodes, allow adjustment if pins overflow or underflow\n            const requiredHeight = titleHeight + contentHeight;\n            if (this.data.size.height !== requiredHeight) {\n                this.data.size.height = requiredHeight;\n                return true;\n            }\n            return false;\n        }\n        const newHeight = titleHeight + contentHeight;\n        if (this.data.size.height !== newHeight) {\n            console.log(\"节点 \".concat(this.data.name, \" 高度变化: \").concat(this.data.size.height, \" -> \").concat(newHeight, \" (引脚数: \").concat(maxPins, \")\"));\n            this.data.size.height = newHeight;\n            return true;\n        }\n        return false;\n    }\n    constructor(data){\n        this.data = {\n            id: data.id || this.generateId(),\n            type: data.type,\n            name: data.name || 'Base Node',\n            icon: data.icon || '❓',\n            color: data.color || 'gray',\n            description: data.description || '',\n            position: data.position || {\n                x: 0,\n                y: 0\n            },\n            size: data.size || {\n                width: 200,\n                height: 100\n            },\n            status: data.status || 'idle',\n            pins: data.pins || [],\n            properties: data.properties || {},\n            selected: data.selected || false,\n            dragging: data.dragging || false\n        };\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/base/BaseNode.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/nodes/controls/KnobNode.tsx":
/*!*****************************************!*\
  !*** ./src/nodes/controls/KnobNode.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   KnobNode: () => (/* binding */ KnobNode)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _base_BaseNode__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../base/BaseNode */ \"(app-pages-browser)/./src/base/BaseNode.ts\");\n\n\n\nclass KnobNode extends _base_BaseNode__WEBPACK_IMPORTED_MODULE_2__.BaseNode {\n    initializePins() {\n        this.updatePinsConfiguration();\n    }\n    updatePinsConfiguration() {\n        const value = this.getProperty('value');\n        this.data.pins = [\n            {\n                id: 'value_output',\n                name: 'Value',\n                type: 'output',\n                dataType: 'number',\n                value\n            }\n        ];\n        return super.updatePinsConfiguration();\n    }\n    async process() {\n        this.setStatus('running');\n        const value = this.getProperty('value');\n        this.setPinValue('value_output', value);\n        this.setStatus('success');\n    }\n    onPropertyChanged(key, value) {\n        if (key === 'value' || key === 'outputRange') {\n            this.process();\n        }\n        super.onPropertyChanged(key, value);\n    }\n    static renderProperties(param) {\n        let { node, onUpdateNode } = param;\n        const properties = node.properties;\n        const handlePropertyChange = (key, value)=>{\n            onUpdateNode(node.id, {\n                properties: {\n                    ...node.properties,\n                    [key]: value\n                }\n            });\n        };\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-4\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                        children: \"输出范围\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/nodes/controls/KnobNode.tsx\",\n                        lineNumber: 65,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                        value: properties.outputRange || 'percentage',\n                        onChange: (e)=>handlePropertyChange('outputRange', e.target.value),\n                        className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                value: \"percentage\",\n                                children: \"百分比 (0-100)\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/nodes/controls/KnobNode.tsx\",\n                                lineNumber: 73,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                value: \"0-255\",\n                                children: \"0-255\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/nodes/controls/KnobNode.tsx\",\n                                lineNumber: 74,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                value: \"0-1\",\n                                children: \"0-1\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/nodes/controls/KnobNode.tsx\",\n                                lineNumber: 75,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                value: \"boolean\",\n                                children: \"布尔值\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/nodes/controls/KnobNode.tsx\",\n                                lineNumber: 76,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/nodes/controls/KnobNode.tsx\",\n                        lineNumber: 68,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/nodes/controls/KnobNode.tsx\",\n                lineNumber: 64,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/nodes/controls/KnobNode.tsx\",\n            lineNumber: 63,\n            columnNumber: 7\n        }, this);\n    }\n    constructor(data){\n        super({\n            type: 'knob',\n            name: '旋钮',\n            icon: '🎛️',\n            color: 'blue',\n            description: '一个可以调节数值的旋钮',\n            properties: {\n                value: 50,\n                outputRange: 'percentage',\n                dynamicSize: false\n            },\n            size: {\n                width: 160,\n                height: 200\n            },\n            ...data\n        });\n        this.initializePins();\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/nodes/controls/KnobNode.tsx\n"));

/***/ })

}]);