"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/WorkflowCanvas.tsx":
/*!*******************************************!*\
  !*** ./src/components/WorkflowCanvas.tsx ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   WorkflowCanvas: () => (/* binding */ WorkflowCanvas)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _nodes_LedNode__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/nodes/LedNode */ \"(app-pages-browser)/./src/nodes/LedNode.ts\");\n/* harmony import */ var _nodes_LedNodeComponent__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./nodes/LedNodeComponent */ \"(app-pages-browser)/./src/components/nodes/LedNodeComponent.tsx\");\n/* harmony import */ var _PropertyPanel__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./PropertyPanel */ \"(app-pages-browser)/./src/components/PropertyPanel.tsx\");\n/* __next_internal_client_entry_do_not_use__ WorkflowCanvas auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst WorkflowCanvas = ()=>{\n    _s();\n    const [nodes, setNodes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedNode, setSelectedNode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showPropertyPanel, setShowPropertyPanel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const canvasRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const nodeInstancesRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(new Map());\n    // 创建新的LED节点\n    const createLedNode = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"WorkflowCanvas.useCallback[createLedNode]\": (position)=>{\n            const ledNode = new _nodes_LedNode__WEBPACK_IMPORTED_MODULE_2__.LedNode({\n                position\n            });\n            const nodeData = ledNode.getData();\n            console.log('创建LED节点:', nodeData);\n            setNodes({\n                \"WorkflowCanvas.useCallback[createLedNode]\": (prev)=>[\n                        ...prev,\n                        nodeData\n                    ]\n            }[\"WorkflowCanvas.useCallback[createLedNode]\"]);\n            nodeInstancesRef.current.set(nodeData.id, ledNode);\n            return nodeData;\n        }\n    }[\"WorkflowCanvas.useCallback[createLedNode]\"], []);\n    // 更新节点数据\n    const updateNode = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"WorkflowCanvas.useCallback[updateNode]\": (nodeId, updates)=>{\n            // 使用函数式更新减少重新渲染\n            setNodes({\n                \"WorkflowCanvas.useCallback[updateNode]\": (prev)=>{\n                    const nodeIndex = prev.findIndex({\n                        \"WorkflowCanvas.useCallback[updateNode].nodeIndex\": (node)=>node.id === nodeId\n                    }[\"WorkflowCanvas.useCallback[updateNode].nodeIndex\"]);\n                    if (nodeIndex === -1) return prev;\n                    const currentNode = prev[nodeIndex];\n                    const updatedNode = {\n                        ...currentNode,\n                        ...updates\n                    };\n                    // 检查是否真的有变化，避免不必要的更新\n                    if (JSON.stringify(currentNode) === JSON.stringify(updatedNode)) {\n                        return prev;\n                    }\n                    // 更新节点实例\n                    const nodeInstance = nodeInstancesRef.current.get(nodeId);\n                    if (nodeInstance) {\n                        nodeInstance.updateData(updatedNode);\n                        // 如果属性发生变化，触发处理\n                        if (updates.properties) {\n                            Object.entries(updates.properties).forEach({\n                                \"WorkflowCanvas.useCallback[updateNode]\": (param)=>{\n                                    let [key, value] = param;\n                                    nodeInstance.setProperty(key, value);\n                                }\n                            }[\"WorkflowCanvas.useCallback[updateNode]\"]);\n                        }\n                    }\n                    // 创建新数组，只更新变化的节点\n                    const newNodes = [\n                        ...prev\n                    ];\n                    newNodes[nodeIndex] = updatedNode;\n                    return newNodes;\n                }\n            }[\"WorkflowCanvas.useCallback[updateNode]\"]);\n            // 如果更新的是当前选中的节点，也更新选中状态\n            if ((selectedNode === null || selectedNode === void 0 ? void 0 : selectedNode.id) === nodeId) {\n                setSelectedNode({\n                    \"WorkflowCanvas.useCallback[updateNode]\": (prev)=>prev ? {\n                            ...prev,\n                            ...updates\n                        } : null\n                }[\"WorkflowCanvas.useCallback[updateNode]\"]);\n            }\n        }\n    }[\"WorkflowCanvas.useCallback[updateNode]\"], [\n        selectedNode\n    ]);\n    // 处理节点事件\n    const handleNodeEvent = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"WorkflowCanvas.useCallback[handleNodeEvent]\": (event)=>{\n            console.log('节点事件:', event.type, event.nodeId);\n            const nodeInstance = nodeInstancesRef.current.get(event.nodeId);\n            if (nodeInstance) {\n                nodeInstance.handleEvent(event);\n            }\n            switch(event.type){\n                case 'click':\n                    const clickedNode = nodes.find({\n                        \"WorkflowCanvas.useCallback[handleNodeEvent].clickedNode\": (n)=>n.id === event.nodeId\n                    }[\"WorkflowCanvas.useCallback[handleNodeEvent].clickedNode\"]);\n                    if (clickedNode) {\n                        // 取消其他节点的选中状态\n                        setNodes({\n                            \"WorkflowCanvas.useCallback[handleNodeEvent]\": (prev)=>prev.map({\n                                    \"WorkflowCanvas.useCallback[handleNodeEvent]\": (node)=>({\n                                            ...node,\n                                            selected: node.id === event.nodeId\n                                        })\n                                }[\"WorkflowCanvas.useCallback[handleNodeEvent]\"])\n                        }[\"WorkflowCanvas.useCallback[handleNodeEvent]\"]);\n                        setSelectedNode(clickedNode);\n                    }\n                    break;\n                case 'doubleClick':\n                    const doubleClickedNode = nodes.find({\n                        \"WorkflowCanvas.useCallback[handleNodeEvent].doubleClickedNode\": (n)=>n.id === event.nodeId\n                    }[\"WorkflowCanvas.useCallback[handleNodeEvent].doubleClickedNode\"]);\n                    if (doubleClickedNode) {\n                        setSelectedNode(doubleClickedNode);\n                        setShowPropertyPanel(true);\n                    }\n                    break;\n                case 'dragStart':\n                    console.log('开始拖拽节点:', event.nodeId);\n                    break;\n                case 'drag':\n                    var _event_data;\n                    if ((_event_data = event.data) === null || _event_data === void 0 ? void 0 : _event_data.position) {\n                        console.log('拖拽节点到:', event.data.position);\n                    }\n                    break;\n                case 'dragEnd':\n                    console.log('结束拖拽节点:', event.nodeId);\n                    break;\n            }\n        }\n    }[\"WorkflowCanvas.useCallback[handleNodeEvent]\"], [\n        nodes\n    ]);\n    // 处理画布点击\n    const handleCanvasClick = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"WorkflowCanvas.useCallback[handleCanvasClick]\": (e)=>{\n            if (e.target === canvasRef.current) {\n                // 取消所有节点的选中状态\n                setNodes({\n                    \"WorkflowCanvas.useCallback[handleCanvasClick]\": (prev)=>prev.map({\n                            \"WorkflowCanvas.useCallback[handleCanvasClick]\": (node)=>({\n                                    ...node,\n                                    selected: false\n                                })\n                        }[\"WorkflowCanvas.useCallback[handleCanvasClick]\"])\n                }[\"WorkflowCanvas.useCallback[handleCanvasClick]\"]);\n                setSelectedNode(null);\n            }\n        }\n    }[\"WorkflowCanvas.useCallback[handleCanvasClick]\"], []);\n    // 处理画布双击 - 创建新节点\n    const handleCanvasDoubleClick = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"WorkflowCanvas.useCallback[handleCanvasDoubleClick]\": (e)=>{\n            if (e.target === canvasRef.current) {\n                const rect = canvasRef.current.getBoundingClientRect();\n                const position = {\n                    x: e.clientX - rect.left - 60,\n                    y: e.clientY - rect.top - 40\n                };\n                createLedNode(position);\n            }\n        }\n    }[\"WorkflowCanvas.useCallback[handleCanvasDoubleClick]\"], [\n        createLedNode\n    ]);\n    // 渲染节点\n    const renderNode = (node)=>{\n        switch(node.type){\n            case 'led':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_nodes_LedNodeComponent__WEBPACK_IMPORTED_MODULE_3__.LedNodeComponent, {\n                    node: node,\n                    onEvent: handleNodeEvent,\n                    onUpdateNode: updateNode\n                }, node.id, false, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/WorkflowCanvas.tsx\",\n                    lineNumber: 142,\n                    columnNumber: 11\n                }, undefined);\n            default:\n                return null;\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative w-full h-screen bg-gray-50 overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-4 left-4 z-10 bg-white rounded-lg shadow-md p-2 flex space-x-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>createLedNode({\n                                x: 100,\n                                y: 100\n                            }),\n                        className: \"px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors\",\n                        children: \"添加LED节点\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/WorkflowCanvas.tsx\",\n                        lineNumber: 158,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>{\n                            setNodes([]);\n                            nodeInstancesRef.current.clear();\n                            setSelectedNode(null);\n                            setShowPropertyPanel(false);\n                        },\n                        className: \"px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600 transition-colors\",\n                        children: \"清空画布\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/WorkflowCanvas.tsx\",\n                        lineNumber: 164,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/WorkflowCanvas.tsx\",\n                lineNumber: 157,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-4 right-4 z-10 bg-white rounded-lg shadow-md p-2\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-sm text-gray-600\",\n                    children: [\n                        \"节点数量: \",\n                        nodes.length,\n                        selectedNode && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-1\",\n                            children: [\n                                \"选中: \",\n                                selectedNode.name\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/WorkflowCanvas.tsx\",\n                            lineNumber: 182,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/WorkflowCanvas.tsx\",\n                    lineNumber: 179,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/WorkflowCanvas.tsx\",\n                lineNumber: 178,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: canvasRef,\n                className: \"w-full h-full relative cursor-crosshair\",\n                onClick: handleCanvasClick,\n                onDoubleClick: handleCanvasDoubleClick,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 opacity-20\",\n                        style: {\n                            backgroundImage: \"\\n              linear-gradient(to right, #e5e7eb 1px, transparent 1px),\\n              linear-gradient(to bottom, #e5e7eb 1px, transparent 1px)\\n            \",\n                            backgroundSize: '20px 20px'\n                        }\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/WorkflowCanvas.tsx\",\n                        lineNumber: 197,\n                        columnNumber: 9\n                    }, undefined),\n                    nodes.map(renderNode),\n                    nodes.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 flex items-center justify-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-gray-400 text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-lg mb-2\",\n                                    children: \"工作流画布\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/WorkflowCanvas.tsx\",\n                                    lineNumber: 215,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm\",\n                                    children: \"双击画布创建LED节点，或点击工具栏按钮\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/WorkflowCanvas.tsx\",\n                                    lineNumber: 216,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/WorkflowCanvas.tsx\",\n                            lineNumber: 214,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/WorkflowCanvas.tsx\",\n                        lineNumber: 213,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/WorkflowCanvas.tsx\",\n                lineNumber: 190,\n                columnNumber: 7\n            }, undefined),\n            showPropertyPanel && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_PropertyPanel__WEBPACK_IMPORTED_MODULE_4__.PropertyPanel, {\n                node: selectedNode,\n                onUpdateNode: updateNode,\n                onClose: ()=>setShowPropertyPanel(false)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/WorkflowCanvas.tsx\",\n                lineNumber: 224,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/WorkflowCanvas.tsx\",\n        lineNumber: 155,\n        columnNumber: 5\n    }, undefined);\n};\n_s(WorkflowCanvas, \"kOpDbL+UmWF6QzImBs/zcNJv4Kg=\");\n_c = WorkflowCanvas;\nvar _c;\n$RefreshReg$(_c, \"WorkflowCanvas\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/WorkflowCanvas.tsx\n"));

/***/ })

});