"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/nodes/BaseNode.ts":
/*!*******************************!*\
  !*** ./src/nodes/BaseNode.ts ***!
  \*******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BaseNode: () => (/* binding */ BaseNode)\n/* harmony export */ });\nclass BaseNode {\n    // 生成唯一ID\n    generateId() {\n        return \"node_\".concat(Date.now(), \"_\").concat(Math.random().toString(36).substr(2, 9));\n    }\n    // 获取节点数据\n    getData() {\n        return {\n            ...this.data\n        };\n    }\n    // 更新节点数据\n    updateData(updates) {\n        this.data = {\n            ...this.data,\n            ...updates\n        };\n    }\n    // 设置节点状态\n    setStatus(status) {\n        this.data.status = status;\n    }\n    // 添加引脚\n    addPin(pin) {\n        this.data.pins.push(pin);\n    }\n    // 移除引脚\n    removePin(pinId) {\n        this.data.pins = this.data.pins.filter((pin)=>pin.id !== pinId);\n    }\n    // 获取引脚\n    getPin(pinId) {\n        return this.data.pins.find((pin)=>pin.id === pinId);\n    }\n    // 设置引脚值\n    setPinValue(pinId, value) {\n        const pin = this.getPin(pinId);\n        if (pin) {\n            pin.value = value;\n            this.onPinValueChanged(pinId, value);\n        }\n    }\n    // 获取引脚值\n    getPinValue(pinId) {\n        const pin = this.getPin(pinId);\n        return pin === null || pin === void 0 ? void 0 : pin.value;\n    }\n    // 设置属性\n    setProperty(key, value) {\n        this.data.properties[key] = value;\n        this.onPropertyChanged(key, value);\n    }\n    // 获取属性\n    getProperty(key) {\n        return this.data.properties[key];\n    }\n    // 处理事件\n    handleEvent(event) {\n        switch(event.type){\n            case 'click':\n                this.onClick(event);\n                break;\n            case 'doubleClick':\n                this.onDoubleClick(event);\n                break;\n            case 'dragStart':\n                this.onDragStart(event);\n                break;\n            case 'drag':\n                this.onDrag(event);\n                break;\n            case 'dragEnd':\n                this.onDragEnd(event);\n                break;\n            case 'pinConnect':\n                this.onPinConnect(event);\n                break;\n            case 'pinDisconnect':\n                this.onPinDisconnect(event);\n                break;\n            case 'propertyChange':\n                this.onPropertyChange(event);\n                break;\n        }\n    }\n    // 事件处理方法 - 子类可以重写\n    onClick(event) {}\n    onDoubleClick(event) {}\n    onDragStart(event) {\n        this.data.dragging = true;\n    }\n    onDrag(event) {\n        if (event.data && this.data.dragging) {\n            this.data.position = event.data.position;\n        }\n    }\n    onDragEnd(event) {\n        this.data.dragging = false;\n    }\n    onPinConnect(event) {}\n    onPinDisconnect(event) {}\n    onPropertyChange(event) {}\n    onPinValueChanged(pinId, value) {\n        // 当引脚值改变时触发处理\n        this.process();\n    }\n    onPropertyChanged(key, value) {\n        // 当属性改变时可能需要重新配置引脚\n        this.updatePinsConfiguration();\n    }\n    // 更新引脚配置 - 子类可以重写\n    updatePinsConfiguration() {\n        // 更新引脚配置后，重新计算节点高度\n        this.updateNodeSize();\n    }\n    // 根据引脚数量计算并更新节点尺寸\n    updateNodeSize() {\n        const inputPins = this.data.pins.filter((pin)=>pin.type === 'input');\n        const outputPins = this.data.pins.filter((pin)=>pin.type === 'output');\n        const maxPins = Math.max(inputPins.length, outputPins.length);\n        // 标题栏高度：37px\n        const titleHeight = 37;\n        // 内容区域最小高度\n        const minContentHeight = 80;\n        // 引脚区域上下边距\n        const pinMargins = 24; // 上边距12px + 下边距12px\n        // 计算引脚所需的高度\n        let pinsHeight = minContentHeight;\n        if (maxPins > 0) {\n            // 每个引脚需要的最小间距 - 与前端保持一致\n            const minPinSpacing = 48;\n            // 确保有足够的空间容纳所有引脚\n            const requiredPinsHeight = (maxPins - 1) * minPinSpacing + 40; // 额外40px用于首尾边距\n            pinsHeight = Math.max(minContentHeight, requiredPinsHeight);\n        }\n        const newHeight = titleHeight + pinMargins + pinsHeight;\n        // 只有当高度发生变化时才更新\n        if (this.data.size.height !== newHeight) {\n            this.data.size.height = newHeight;\n        }\n    }\n    constructor(data){\n        this.data = {\n            id: data.id || this.generateId(),\n            type: data.type || 'base',\n            name: data.name || 'Base Node',\n            description: data.description || '',\n            position: data.position || {\n                x: 0,\n                y: 0\n            },\n            size: data.size || {\n                width: 200,\n                height: 100\n            },\n            status: data.status || 'idle',\n            pins: data.pins || [],\n            properties: data.properties || {},\n            selected: data.selected || false,\n            dragging: data.dragging || false\n        };\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/nodes/BaseNode.ts\n"));

/***/ })

});