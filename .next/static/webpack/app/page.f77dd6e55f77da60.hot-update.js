"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/nodes/BaseNode.ts":
/*!*******************************!*\
  !*** ./src/nodes/BaseNode.ts ***!
  \*******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BaseNode: () => (/* binding */ BaseNode)\n/* harmony export */ });\nclass BaseNode {\n    // 生成唯一ID\n    generateId() {\n        return \"node_\".concat(Date.now(), \"_\").concat(Math.random().toString(36).substr(2, 9));\n    }\n    // 获取节点数据\n    getData() {\n        return {\n            ...this.data\n        };\n    }\n    // 更新节点数据\n    updateData(updates) {\n        this.data = {\n            ...this.data,\n            ...updates\n        };\n    }\n    // 设置节点状态\n    setStatus(status) {\n        this.data.status = status;\n    }\n    // 添加引脚\n    addPin(pin) {\n        this.data.pins.push(pin);\n    }\n    // 移除引脚\n    removePin(pinId) {\n        this.data.pins = this.data.pins.filter((pin)=>pin.id !== pinId);\n    }\n    // 获取引脚\n    getPin(pinId) {\n        return this.data.pins.find((pin)=>pin.id === pinId);\n    }\n    // 设置引脚值\n    setPinValue(pinId, value) {\n        const pin = this.getPin(pinId);\n        if (pin) {\n            pin.value = value;\n            this.onPinValueChanged(pinId, value);\n        }\n    }\n    // 获取引脚值\n    getPinValue(pinId) {\n        const pin = this.getPin(pinId);\n        return pin === null || pin === void 0 ? void 0 : pin.value;\n    }\n    // 设置属性\n    setProperty(key, value) {\n        this.data.properties[key] = value;\n        this.onPropertyChanged(key, value);\n    }\n    // 获取属性\n    getProperty(key) {\n        return this.data.properties[key];\n    }\n    // 处理事件\n    handleEvent(event) {\n        switch(event.type){\n            case 'click':\n                this.onClick(event);\n                break;\n            case 'doubleClick':\n                this.onDoubleClick(event);\n                break;\n            case 'dragStart':\n                this.onDragStart(event);\n                break;\n            case 'drag':\n                this.onDrag(event);\n                break;\n            case 'dragEnd':\n                this.onDragEnd(event);\n                break;\n            case 'pinConnect':\n                this.onPinConnect(event);\n                break;\n            case 'pinDisconnect':\n                this.onPinDisconnect(event);\n                break;\n            case 'propertyChange':\n                this.onPropertyChange(event);\n                break;\n        }\n    }\n    // 事件处理方法 - 子类可以重写\n    onClick(event) {}\n    onDoubleClick(event) {}\n    onDragStart(event) {\n        this.data.dragging = true;\n    }\n    onDrag(event) {\n        if (event.data && this.data.dragging) {\n            this.data.position = event.data.position;\n        }\n    }\n    onDragEnd(event) {\n        this.data.dragging = false;\n    }\n    onPinConnect(event) {}\n    onPinDisconnect(event) {}\n    onPropertyChange(event) {}\n    onPinValueChanged(pinId, value) {\n        // 当引脚值改变时触发处理\n        this.process();\n    }\n    onPropertyChanged(key, value) {\n        // 当属性改变时可能需要重新配置引脚\n        this.updatePinsConfiguration();\n    }\n    // 更新引脚配置 - 子类可以重写\n    updatePinsConfiguration() {\n        // 更新引脚配置后，重新计算节点高度\n        this.updateNodeSize();\n    }\n    // 根据引脚数量计算并更新节点尺寸\n    updateNodeSize() {\n        const inputPins = this.data.pins.filter((pin)=>pin.type === 'input');\n        const outputPins = this.data.pins.filter((pin)=>pin.type === 'output');\n        const maxPins = Math.max(inputPins.length, outputPins.length);\n        // 标题栏高度：37px\n        const titleHeight = 37;\n        // 内容区域最小高度\n        const minContentHeight = 80;\n        // 引脚区域上下边距\n        const pinMargins = 24; // 上边距12px + 下边距12px\n        // 计算引脚所需的高度\n        let pinsHeight = 0;\n        if (maxPins > 0) {\n            // 每个引脚需要的最小间距\n            const minPinSpacing = 20;\n            pinsHeight = Math.max(minContentHeight - pinMargins, (maxPins - 1) * minPinSpacing + 20);\n        }\n        const newHeight = titleHeight + pinMargins + pinsHeight;\n        // 只有当高度发生变化时才更新\n        if (this.data.size.height !== newHeight) {\n            this.data.size.height = newHeight;\n        }\n    }\n    constructor(data){\n        this.data = {\n            id: data.id || this.generateId(),\n            type: data.type || 'base',\n            name: data.name || 'Base Node',\n            description: data.description || '',\n            position: data.position || {\n                x: 0,\n                y: 0\n            },\n            size: data.size || {\n                width: 200,\n                height: 100\n            },\n            status: data.status || 'idle',\n            pins: data.pins || [],\n            properties: data.properties || {},\n            selected: data.selected || false,\n            dragging: data.dragging || false\n        };\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/nodes/BaseNode.ts\n"));

/***/ })

});