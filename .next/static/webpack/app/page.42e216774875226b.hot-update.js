"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/nodes/BaseNodeComponent.tsx":
/*!****************************************************!*\
  !*** ./src/components/nodes/BaseNodeComponent.tsx ***!
  \****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BaseNodeComponent: () => (/* binding */ BaseNodeComponent)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ BaseNodeComponent auto */ \nvar _s = $RefreshSig$();\n\nconst BaseNodeComponent = (param)=>{\n    let { node, onEvent, onUpdateNode, children } = param;\n    _s();\n    const [isDragging, setIsDragging] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [dragOffset, setDragOffset] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        x: 0,\n        y: 0\n    });\n    const nodeRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const animationFrameRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const pendingUpdateRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // 计算引脚位置\n    const calculatePinPositions = ()=>{\n        const inputPins = node.pins.filter((pin)=>pin.type === 'input');\n        const outputPins = node.pins.filter((pin)=>pin.type === 'output');\n        const maxPins = Math.max(inputPins.length, outputPins.length);\n        // 标题栏高度：padding(16px) + 文字高度(20px) + border(1px) = 37px\n        const titleHeight = 37;\n        // 底部留白\n        const bottomMargin = 12;\n        // 引脚区域顶部留白\n        const topMargin = 12;\n        // 可用于引脚的高度\n        const availableHeight = node.size.height - titleHeight - bottomMargin - topMargin;\n        // 引脚间距，如果只有一个引脚则居中，多个引脚则均匀分布\n        // 期望间距48px，但要根据实际可用高度调整\n        const desiredPinSpacing = 48;\n        let pinSpacing = 0;\n        if (maxPins <= 1) {\n            pinSpacing = 0; // 单个引脚居中\n        } else {\n            // 计算实际可用的间距\n            pinSpacing = availableHeight / (maxPins - 1);\n            // 如果可用间距小于期望间距，说明节点高度不够，需要调整\n            if (pinSpacing < desiredPinSpacing) {\n                console.warn(\"节点 \".concat(node.name, \" 高度不足，期望间距 \").concat(desiredPinSpacing, \"px，实际只有 \").concat(pinSpacing.toFixed(1), \"px\"));\n            }\n        }\n        return {\n            inputPins,\n            outputPins,\n            titleHeight,\n            topMargin,\n            pinSpacing,\n            availableHeight,\n            maxPins\n        };\n    };\n    const { inputPins, outputPins, titleHeight, topMargin, pinSpacing, maxPins } = calculatePinPositions();\n    // 使用requestAnimationFrame节流更新\n    const flushPendingUpdate = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"BaseNodeComponent.useCallback[flushPendingUpdate]\": ()=>{\n            if (pendingUpdateRef.current) {\n                onUpdateNode(node.id, {\n                    position: pendingUpdateRef.current\n                });\n                pendingUpdateRef.current = null;\n            }\n            animationFrameRef.current = null;\n        }\n    }[\"BaseNodeComponent.useCallback[flushPendingUpdate]\"], [\n        onUpdateNode,\n        node.id\n    ]);\n    const handleMouseMove = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"BaseNodeComponent.useCallback[handleMouseMove]\": (e)=>{\n            const newPosition = {\n                x: e.clientX - dragOffset.x,\n                y: e.clientY - dragOffset.y\n            };\n            // 立即更新DOM位置以获得流畅的视觉效果\n            if (nodeRef.current) {\n                nodeRef.current.style.left = \"\".concat(newPosition.x, \"px\");\n                nodeRef.current.style.top = \"\".concat(newPosition.y, \"px\");\n            }\n            // 节流状态更新\n            pendingUpdateRef.current = newPosition;\n            if (animationFrameRef.current === null) {\n                animationFrameRef.current = requestAnimationFrame(flushPendingUpdate);\n            }\n        }\n    }[\"BaseNodeComponent.useCallback[handleMouseMove]\"], [\n        dragOffset.x,\n        dragOffset.y,\n        flushPendingUpdate\n    ]);\n    const handleMouseUp = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"BaseNodeComponent.useCallback[handleMouseUp]\": ()=>{\n            setIsDragging(false);\n            // 清理动画帧\n            if (animationFrameRef.current) {\n                cancelAnimationFrame(animationFrameRef.current);\n                animationFrameRef.current = null;\n            }\n            // 确保最后的位置更新被应用\n            if (pendingUpdateRef.current) {\n                onUpdateNode(node.id, {\n                    position: pendingUpdateRef.current\n                });\n                pendingUpdateRef.current = null;\n            }\n            onEvent({\n                type: 'dragEnd',\n                nodeId: node.id\n            });\n            // 移除全局鼠标事件监听\n            document.removeEventListener('mousemove', handleMouseMove);\n            document.removeEventListener('mouseup', handleMouseUp);\n        }\n    }[\"BaseNodeComponent.useCallback[handleMouseUp]\"], [\n        handleMouseMove,\n        onEvent,\n        onUpdateNode,\n        node.id\n    ]);\n    const handleMouseDown = (e)=>{\n        var _nodeRef_current;\n        if (e.button !== 0) return; // 只处理左键\n        // 防止在引脚上开始拖拽\n        const target = e.target;\n        if (target.closest('.pin-connector')) {\n            return;\n        }\n        e.preventDefault();\n        e.stopPropagation();\n        // 获取鼠标相对于节点的精确偏移量\n        const rect = (_nodeRef_current = nodeRef.current) === null || _nodeRef_current === void 0 ? void 0 : _nodeRef_current.getBoundingClientRect();\n        if (!rect) return;\n        // 计算鼠标在节点内的相对位置\n        const offsetX = e.clientX - rect.left;\n        const offsetY = e.clientY - rect.top;\n        setDragOffset({\n            x: offsetX,\n            y: offsetY\n        });\n        setIsDragging(true);\n        onEvent({\n            type: 'dragStart',\n            nodeId: node.id,\n            data: {\n                position: node.position\n            }\n        });\n        // 添加全局鼠标事件监听\n        document.addEventListener('mousemove', handleMouseMove);\n        document.addEventListener('mouseup', handleMouseUp);\n    };\n    const handleClick = (e)=>{\n        e.stopPropagation();\n        onEvent({\n            type: 'click',\n            nodeId: node.id\n        });\n    };\n    const handleDoubleClick = (e)=>{\n        e.stopPropagation();\n        onEvent({\n            type: 'doubleClick',\n            nodeId: node.id\n        });\n    };\n    const getStatusColor = ()=>{\n        switch(node.status){\n            case 'running':\n                return 'border-yellow-400 bg-yellow-50';\n            case 'success':\n                return 'border-green-400 bg-green-50';\n            case 'error':\n                return 'border-red-400 bg-red-50';\n            case 'warning':\n                return 'border-orange-400 bg-orange-50';\n            default:\n                return 'border-gray-300 bg-white';\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: nodeRef,\n        className: \"absolute border-2 rounded-lg shadow-md cursor-move select-none \".concat(getStatusColor(), \" \").concat(node.selected ? 'ring-2 ring-blue-400' : '', \" \").concat(isDragging ? 'shadow-lg' : ''),\n        style: {\n            left: node.position.x,\n            top: node.position.y,\n            width: node.size.width,\n            height: node.size.height\n        },\n        onMouseDown: handleMouseDown,\n        onClick: handleClick,\n        onDoubleClick: handleDoubleClick,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-2 py-2 bg-gray-100 border-b border-gray-200 rounded-t-md\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-sm font-medium text-gray-800 truncate\",\n                    children: node.name\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/nodes/BaseNodeComponent.tsx\",\n                    lineNumber: 207,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/nodes/BaseNodeComponent.tsx\",\n                lineNumber: 206,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-2 flex-1\",\n                children: children\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/nodes/BaseNodeComponent.tsx\",\n                lineNumber: 213,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute left-0\",\n                style: {\n                    top: titleHeight + topMargin\n                },\n                children: inputPins.map((pin, index)=>{\n                    // 如果只有一个引脚，居中显示；多个引脚则均匀分布\n                    const pinY = maxPins === 1 ? 0 : index * pinSpacing;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute flex items-center\",\n                        style: {\n                            top: pinY,\n                            left: -6,\n                            transform: 'translateY(-50%)' // 垂直居中对齐\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"pin-connector w-3 h-3 rounded-full border-2 cursor-pointer transition-colors \".concat(pin.connected ? 'bg-blue-500 border-blue-600' : 'bg-white border-gray-400 hover:border-blue-400'),\n                                title: pin.name\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/nodes/BaseNodeComponent.tsx\",\n                                lineNumber: 232,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"absolute left-4 top-1/2 transform -translate-y-1/2 text-xs text-gray-600 whitespace-nowrap\",\n                                children: pin.name\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/nodes/BaseNodeComponent.tsx\",\n                                lineNumber: 240,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, pin.id, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/nodes/BaseNodeComponent.tsx\",\n                        lineNumber: 223,\n                        columnNumber: 13\n                    }, undefined);\n                })\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/nodes/BaseNodeComponent.tsx\",\n                lineNumber: 218,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute right-0\",\n                style: {\n                    top: titleHeight + topMargin\n                },\n                children: outputPins.map((pin, index)=>{\n                    // 如果只有一个引脚，居中显示；多个引脚则均匀分布\n                    const pinY = maxPins === 1 ? 0 : index * pinSpacing;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute flex items-center justify-end\",\n                        style: {\n                            top: pinY,\n                            right: -6,\n                            transform: 'translateY(-50%)' // 垂直居中对齐\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"absolute right-4 top-1/2 transform -translate-y-1/2 text-xs text-gray-600 whitespace-nowrap\",\n                                children: pin.name\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/nodes/BaseNodeComponent.tsx\",\n                                lineNumber: 263,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"pin-connector w-3 h-3 rounded-full border-2 cursor-pointer transition-colors \".concat(pin.connected ? 'bg-green-500 border-green-600' : 'bg-white border-gray-400 hover:border-green-400'),\n                                title: pin.name\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/nodes/BaseNodeComponent.tsx\",\n                                lineNumber: 266,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, pin.id, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/nodes/BaseNodeComponent.tsx\",\n                        lineNumber: 254,\n                        columnNumber: 13\n                    }, undefined);\n                })\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/nodes/BaseNodeComponent.tsx\",\n                lineNumber: 249,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/nodes/BaseNodeComponent.tsx\",\n        lineNumber: 188,\n        columnNumber: 5\n    }, undefined);\n};\n_s(BaseNodeComponent, \"7O/WCX1jRM3DSG4/td1hT8DSAmU=\");\n_c = BaseNodeComponent;\nvar _c;\n$RefreshReg$(_c, \"BaseNodeComponent\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/nodes/BaseNodeComponent.tsx\n"));

/***/ })

});