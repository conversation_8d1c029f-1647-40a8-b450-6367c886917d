"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/WorkflowCanvas.tsx":
/*!*******************************************!*\
  !*** ./src/components/WorkflowCanvas.tsx ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   WorkflowCanvas: () => (/* binding */ WorkflowCanvas)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _nodes_LedNode__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/nodes/LedNode */ \"(app-pages-browser)/./src/nodes/LedNode.ts\");\n/* harmony import */ var _nodes_LedNodeComponent__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./nodes/LedNodeComponent */ \"(app-pages-browser)/./src/components/nodes/LedNodeComponent.tsx\");\n/* harmony import */ var _PropertyPanel__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./PropertyPanel */ \"(app-pages-browser)/./src/components/PropertyPanel.tsx\");\n/* __next_internal_client_entry_do_not_use__ WorkflowCanvas auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst WorkflowCanvas = ()=>{\n    _s();\n    const [nodes, setNodes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedNode, setSelectedNode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showPropertyPanel, setShowPropertyPanel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const canvasRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const nodeInstancesRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(new Map());\n    // 创建新的LED节点\n    const createLedNode = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"WorkflowCanvas.useCallback[createLedNode]\": (position)=>{\n            const ledNode = new _nodes_LedNode__WEBPACK_IMPORTED_MODULE_2__.LedNode({\n                position\n            });\n            const nodeData = ledNode.getData();\n            setNodes({\n                \"WorkflowCanvas.useCallback[createLedNode]\": (prev)=>[\n                        ...prev,\n                        nodeData\n                    ]\n            }[\"WorkflowCanvas.useCallback[createLedNode]\"]);\n            nodeInstancesRef.current.set(nodeData.id, ledNode);\n            return nodeData;\n        }\n    }[\"WorkflowCanvas.useCallback[createLedNode]\"], []);\n    // 更新节点数据\n    const updateNode = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"WorkflowCanvas.useCallback[updateNode]\": (nodeId, updates)=>{\n            // 使用函数式更新减少重新渲染\n            setNodes({\n                \"WorkflowCanvas.useCallback[updateNode]\": (prev)=>{\n                    const nodeIndex = prev.findIndex({\n                        \"WorkflowCanvas.useCallback[updateNode].nodeIndex\": (node)=>node.id === nodeId\n                    }[\"WorkflowCanvas.useCallback[updateNode].nodeIndex\"]);\n                    if (nodeIndex === -1) return prev;\n                    const currentNode = prev[nodeIndex];\n                    const updatedNode = {\n                        ...currentNode,\n                        ...updates\n                    };\n                    // 检查是否真的有变化，避免不必要的更新\n                    if (JSON.stringify(currentNode) === JSON.stringify(updatedNode)) {\n                        return prev;\n                    }\n                    // 更新节点实例\n                    const nodeInstance = nodeInstancesRef.current.get(nodeId);\n                    if (nodeInstance) {\n                        nodeInstance.updateData(updatedNode);\n                        // 如果属性发生变化，触发处理\n                        if (updates.properties) {\n                            Object.entries(updates.properties).forEach({\n                                \"WorkflowCanvas.useCallback[updateNode]\": (param)=>{\n                                    let [key, value] = param;\n                                    nodeInstance.setProperty(key, value);\n                                }\n                            }[\"WorkflowCanvas.useCallback[updateNode]\"]);\n                            // 获取更新后的节点数据（可能包含新的尺寸）\n                            const updatedNodeData = nodeInstance.getData();\n                            if (updatedNodeData.size.height !== updatedNode.size.height || updatedNodeData.size.width !== updatedNode.size.width) {\n                                console.log('检测到节点尺寸变化，更新React状态');\n                                updatedNode.size = updatedNodeData.size;\n                            }\n                        }\n                    }\n                    // 创建新数组，只更新变化的节点\n                    const newNodes = [\n                        ...prev\n                    ];\n                    newNodes[nodeIndex] = updatedNode;\n                    return newNodes;\n                }\n            }[\"WorkflowCanvas.useCallback[updateNode]\"]);\n            // 如果更新的是当前选中的节点，也更新选中状态\n            if ((selectedNode === null || selectedNode === void 0 ? void 0 : selectedNode.id) === nodeId) {\n                setSelectedNode({\n                    \"WorkflowCanvas.useCallback[updateNode]\": (prev)=>prev ? {\n                            ...prev,\n                            ...updates\n                        } : null\n                }[\"WorkflowCanvas.useCallback[updateNode]\"]);\n            }\n        }\n    }[\"WorkflowCanvas.useCallback[updateNode]\"], [\n        selectedNode\n    ]);\n    // 处理节点事件\n    const handleNodeEvent = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"WorkflowCanvas.useCallback[handleNodeEvent]\": (event)=>{\n            const nodeInstance = nodeInstancesRef.current.get(event.nodeId);\n            if (nodeInstance) {\n                nodeInstance.handleEvent(event);\n            }\n            switch(event.type){\n                case 'click':\n                    const clickedNode = nodes.find({\n                        \"WorkflowCanvas.useCallback[handleNodeEvent].clickedNode\": (n)=>n.id === event.nodeId\n                    }[\"WorkflowCanvas.useCallback[handleNodeEvent].clickedNode\"]);\n                    if (clickedNode) {\n                        // 取消其他节点的选中状态\n                        setNodes({\n                            \"WorkflowCanvas.useCallback[handleNodeEvent]\": (prev)=>prev.map({\n                                    \"WorkflowCanvas.useCallback[handleNodeEvent]\": (node)=>({\n                                            ...node,\n                                            selected: node.id === event.nodeId\n                                        })\n                                }[\"WorkflowCanvas.useCallback[handleNodeEvent]\"])\n                        }[\"WorkflowCanvas.useCallback[handleNodeEvent]\"]);\n                        setSelectedNode(clickedNode);\n                    }\n                    break;\n                case 'doubleClick':\n                    const doubleClickedNode = nodes.find({\n                        \"WorkflowCanvas.useCallback[handleNodeEvent].doubleClickedNode\": (n)=>n.id === event.nodeId\n                    }[\"WorkflowCanvas.useCallback[handleNodeEvent].doubleClickedNode\"]);\n                    if (doubleClickedNode) {\n                        setSelectedNode(doubleClickedNode);\n                        setShowPropertyPanel(true);\n                    }\n                    break;\n            }\n        }\n    }[\"WorkflowCanvas.useCallback[handleNodeEvent]\"], [\n        nodes\n    ]);\n    // 处理画布点击\n    const handleCanvasClick = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"WorkflowCanvas.useCallback[handleCanvasClick]\": (e)=>{\n            if (e.target === canvasRef.current) {\n                // 取消所有节点的选中状态\n                setNodes({\n                    \"WorkflowCanvas.useCallback[handleCanvasClick]\": (prev)=>prev.map({\n                            \"WorkflowCanvas.useCallback[handleCanvasClick]\": (node)=>({\n                                    ...node,\n                                    selected: false\n                                })\n                        }[\"WorkflowCanvas.useCallback[handleCanvasClick]\"])\n                }[\"WorkflowCanvas.useCallback[handleCanvasClick]\"]);\n                setSelectedNode(null);\n            }\n        }\n    }[\"WorkflowCanvas.useCallback[handleCanvasClick]\"], []);\n    // 处理画布双击 - 创建新节点\n    const handleCanvasDoubleClick = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"WorkflowCanvas.useCallback[handleCanvasDoubleClick]\": (e)=>{\n            if (e.target === canvasRef.current) {\n                const rect = canvasRef.current.getBoundingClientRect();\n                const position = {\n                    x: e.clientX - rect.left - 60,\n                    y: e.clientY - rect.top - 40\n                };\n                createLedNode(position);\n            }\n        }\n    }[\"WorkflowCanvas.useCallback[handleCanvasDoubleClick]\"], [\n        createLedNode\n    ]);\n    // 渲染节点\n    const renderNode = (node)=>{\n        switch(node.type){\n            case 'led':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_nodes_LedNodeComponent__WEBPACK_IMPORTED_MODULE_3__.LedNodeComponent, {\n                    node: node,\n                    onEvent: handleNodeEvent,\n                    onUpdateNode: updateNode\n                }, node.id, false, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/WorkflowCanvas.tsx\",\n                    lineNumber: 133,\n                    columnNumber: 11\n                }, undefined);\n            default:\n                return null;\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative w-full h-screen bg-gray-50 overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-4 left-4 z-10 bg-white rounded-lg shadow-md p-2 flex space-x-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>createLedNode({\n                                x: 100,\n                                y: 100\n                            }),\n                        className: \"px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors\",\n                        children: \"添加LED节点\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/WorkflowCanvas.tsx\",\n                        lineNumber: 149,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>{\n                            setNodes([]);\n                            nodeInstancesRef.current.clear();\n                            setSelectedNode(null);\n                            setShowPropertyPanel(false);\n                        },\n                        className: \"px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600 transition-colors\",\n                        children: \"清空画布\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/WorkflowCanvas.tsx\",\n                        lineNumber: 155,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/WorkflowCanvas.tsx\",\n                lineNumber: 148,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-4 right-4 z-10 bg-white rounded-lg shadow-md p-2\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-sm text-gray-600\",\n                    children: [\n                        \"节点数量: \",\n                        nodes.length,\n                        selectedNode && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-1\",\n                            children: [\n                                \"选中: \",\n                                selectedNode.name\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/WorkflowCanvas.tsx\",\n                            lineNumber: 173,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/WorkflowCanvas.tsx\",\n                    lineNumber: 170,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/WorkflowCanvas.tsx\",\n                lineNumber: 169,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: canvasRef,\n                className: \"w-full h-full relative cursor-crosshair\",\n                onClick: handleCanvasClick,\n                onDoubleClick: handleCanvasDoubleClick,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 opacity-20\",\n                        style: {\n                            backgroundImage: \"\\n              linear-gradient(to right, #e5e7eb 1px, transparent 1px),\\n              linear-gradient(to bottom, #e5e7eb 1px, transparent 1px)\\n            \",\n                            backgroundSize: '20px 20px'\n                        }\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/WorkflowCanvas.tsx\",\n                        lineNumber: 188,\n                        columnNumber: 9\n                    }, undefined),\n                    nodes.map(renderNode),\n                    nodes.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 flex items-center justify-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-gray-400 text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-lg mb-2\",\n                                    children: \"工作流画布\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/WorkflowCanvas.tsx\",\n                                    lineNumber: 206,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm\",\n                                    children: \"双击画布创建LED节点，或点击工具栏按钮\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/WorkflowCanvas.tsx\",\n                                    lineNumber: 207,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/WorkflowCanvas.tsx\",\n                            lineNumber: 205,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/WorkflowCanvas.tsx\",\n                        lineNumber: 204,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/WorkflowCanvas.tsx\",\n                lineNumber: 181,\n                columnNumber: 7\n            }, undefined),\n            showPropertyPanel && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_PropertyPanel__WEBPACK_IMPORTED_MODULE_4__.PropertyPanel, {\n                node: selectedNode,\n                onUpdateNode: updateNode,\n                onClose: ()=>setShowPropertyPanel(false)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/WorkflowCanvas.tsx\",\n                lineNumber: 215,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/WorkflowCanvas.tsx\",\n        lineNumber: 146,\n        columnNumber: 5\n    }, undefined);\n};\n_s(WorkflowCanvas, \"kOpDbL+UmWF6QzImBs/zcNJv4Kg=\");\n_c = WorkflowCanvas;\nvar _c;\n$RefreshReg$(_c, \"WorkflowCanvas\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/WorkflowCanvas.tsx\n"));

/***/ })

});