"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/WorkflowCanvas.tsx":
/*!*******************************************!*\
  !*** ./src/components/WorkflowCanvas.tsx ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   WorkflowCanvas: () => (/* binding */ WorkflowCanvas)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _nodes_LedNode__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/nodes/LedNode */ \"(app-pages-browser)/./src/nodes/LedNode.ts\");\n/* harmony import */ var _nodes_LedNodeComponent__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./nodes/LedNodeComponent */ \"(app-pages-browser)/./src/components/nodes/LedNodeComponent.tsx\");\n/* harmony import */ var _PropertyPanel__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./PropertyPanel */ \"(app-pages-browser)/./src/components/PropertyPanel.tsx\");\n/* __next_internal_client_entry_do_not_use__ WorkflowCanvas auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst WorkflowCanvas = ()=>{\n    _s();\n    const [nodes, setNodes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedNode, setSelectedNode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showPropertyPanel, setShowPropertyPanel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const canvasRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const nodeInstancesRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(new Map());\n    // 创建新的LED节点\n    const createLedNode = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"WorkflowCanvas.useCallback[createLedNode]\": (position)=>{\n            const ledNode = new _nodes_LedNode__WEBPACK_IMPORTED_MODULE_2__.LedNode({\n                position\n            });\n            const nodeData = ledNode.getData();\n            console.log('创建LED节点:', nodeData);\n            setNodes({\n                \"WorkflowCanvas.useCallback[createLedNode]\": (prev)=>[\n                        ...prev,\n                        nodeData\n                    ]\n            }[\"WorkflowCanvas.useCallback[createLedNode]\"]);\n            nodeInstancesRef.current.set(nodeData.id, ledNode);\n            return nodeData;\n        }\n    }[\"WorkflowCanvas.useCallback[createLedNode]\"], []);\n    // 更新节点数据\n    const updateNode = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"WorkflowCanvas.useCallback[updateNode]\": (nodeId, updates)=>{\n            setNodes({\n                \"WorkflowCanvas.useCallback[updateNode]\": (prev)=>prev.map({\n                        \"WorkflowCanvas.useCallback[updateNode]\": (node)=>{\n                            if (node.id === nodeId) {\n                                const updatedNode = {\n                                    ...node,\n                                    ...updates\n                                };\n                                // 更新节点实例\n                                const nodeInstance = nodeInstancesRef.current.get(nodeId);\n                                if (nodeInstance) {\n                                    nodeInstance.updateData(updatedNode);\n                                    // 如果属性发生变化，触发处理\n                                    if (updates.properties) {\n                                        Object.entries(updates.properties).forEach({\n                                            \"WorkflowCanvas.useCallback[updateNode]\": (param)=>{\n                                                let [key, value] = param;\n                                                nodeInstance.setProperty(key, value);\n                                            }\n                                        }[\"WorkflowCanvas.useCallback[updateNode]\"]);\n                                    }\n                                }\n                                return updatedNode;\n                            }\n                            return node;\n                        }\n                    }[\"WorkflowCanvas.useCallback[updateNode]\"])\n            }[\"WorkflowCanvas.useCallback[updateNode]\"]);\n            // 如果更新的是当前选中的节点，也更新选中状态\n            if ((selectedNode === null || selectedNode === void 0 ? void 0 : selectedNode.id) === nodeId) {\n                setSelectedNode({\n                    \"WorkflowCanvas.useCallback[updateNode]\": (prev)=>prev ? {\n                            ...prev,\n                            ...updates\n                        } : null\n                }[\"WorkflowCanvas.useCallback[updateNode]\"]);\n            }\n        }\n    }[\"WorkflowCanvas.useCallback[updateNode]\"], [\n        selectedNode\n    ]);\n    // 处理节点事件\n    const handleNodeEvent = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"WorkflowCanvas.useCallback[handleNodeEvent]\": (event)=>{\n            console.log('节点事件:', event.type, event.nodeId);\n            const nodeInstance = nodeInstancesRef.current.get(event.nodeId);\n            if (nodeInstance) {\n                nodeInstance.handleEvent(event);\n            }\n            switch(event.type){\n                case 'click':\n                    const clickedNode = nodes.find({\n                        \"WorkflowCanvas.useCallback[handleNodeEvent].clickedNode\": (n)=>n.id === event.nodeId\n                    }[\"WorkflowCanvas.useCallback[handleNodeEvent].clickedNode\"]);\n                    if (clickedNode) {\n                        // 取消其他节点的选中状态\n                        setNodes({\n                            \"WorkflowCanvas.useCallback[handleNodeEvent]\": (prev)=>prev.map({\n                                    \"WorkflowCanvas.useCallback[handleNodeEvent]\": (node)=>({\n                                            ...node,\n                                            selected: node.id === event.nodeId\n                                        })\n                                }[\"WorkflowCanvas.useCallback[handleNodeEvent]\"])\n                        }[\"WorkflowCanvas.useCallback[handleNodeEvent]\"]);\n                        setSelectedNode(clickedNode);\n                    }\n                    break;\n                case 'doubleClick':\n                    const doubleClickedNode = nodes.find({\n                        \"WorkflowCanvas.useCallback[handleNodeEvent].doubleClickedNode\": (n)=>n.id === event.nodeId\n                    }[\"WorkflowCanvas.useCallback[handleNodeEvent].doubleClickedNode\"]);\n                    if (doubleClickedNode) {\n                        setSelectedNode(doubleClickedNode);\n                        setShowPropertyPanel(true);\n                    }\n                    break;\n                case 'dragStart':\n                    console.log('开始拖拽节点:', event.nodeId);\n                    break;\n                case 'drag':\n                    var _event_data;\n                    if ((_event_data = event.data) === null || _event_data === void 0 ? void 0 : _event_data.position) {\n                        console.log('拖拽节点到:', event.data.position);\n                    }\n                    break;\n                case 'dragEnd':\n                    console.log('结束拖拽节点:', event.nodeId);\n                    break;\n            }\n        }\n    }[\"WorkflowCanvas.useCallback[handleNodeEvent]\"], [\n        nodes\n    ]);\n    // 处理画布点击\n    const handleCanvasClick = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"WorkflowCanvas.useCallback[handleCanvasClick]\": (e)=>{\n            if (e.target === canvasRef.current) {\n                // 取消所有节点的选中状态\n                setNodes({\n                    \"WorkflowCanvas.useCallback[handleCanvasClick]\": (prev)=>prev.map({\n                            \"WorkflowCanvas.useCallback[handleCanvasClick]\": (node)=>({\n                                    ...node,\n                                    selected: false\n                                })\n                        }[\"WorkflowCanvas.useCallback[handleCanvasClick]\"])\n                }[\"WorkflowCanvas.useCallback[handleCanvasClick]\"]);\n                setSelectedNode(null);\n            }\n        }\n    }[\"WorkflowCanvas.useCallback[handleCanvasClick]\"], []);\n    // 处理画布双击 - 创建新节点\n    const handleCanvasDoubleClick = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"WorkflowCanvas.useCallback[handleCanvasDoubleClick]\": (e)=>{\n            if (e.target === canvasRef.current) {\n                const rect = canvasRef.current.getBoundingClientRect();\n                const position = {\n                    x: e.clientX - rect.left - 60,\n                    y: e.clientY - rect.top - 40\n                };\n                createLedNode(position);\n            }\n        }\n    }[\"WorkflowCanvas.useCallback[handleCanvasDoubleClick]\"], [\n        createLedNode\n    ]);\n    // 渲染节点\n    const renderNode = (node)=>{\n        switch(node.type){\n            case 'led':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_nodes_LedNodeComponent__WEBPACK_IMPORTED_MODULE_3__.LedNodeComponent, {\n                    node: node,\n                    onEvent: handleNodeEvent,\n                    onUpdateNode: updateNode\n                }, node.id, false, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/WorkflowCanvas.tsx\",\n                    lineNumber: 132,\n                    columnNumber: 11\n                }, undefined);\n            default:\n                return null;\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative w-full h-screen bg-gray-50 overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-4 left-4 z-10 bg-white rounded-lg shadow-md p-2 flex space-x-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>createLedNode({\n                                x: 100,\n                                y: 100\n                            }),\n                        className: \"px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors\",\n                        children: \"添加LED节点\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/WorkflowCanvas.tsx\",\n                        lineNumber: 148,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>{\n                            setNodes([]);\n                            nodeInstancesRef.current.clear();\n                            setSelectedNode(null);\n                            setShowPropertyPanel(false);\n                        },\n                        className: \"px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600 transition-colors\",\n                        children: \"清空画布\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/WorkflowCanvas.tsx\",\n                        lineNumber: 154,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/WorkflowCanvas.tsx\",\n                lineNumber: 147,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-4 right-4 z-10 bg-white rounded-lg shadow-md p-2\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-sm text-gray-600\",\n                    children: [\n                        \"节点数量: \",\n                        nodes.length,\n                        selectedNode && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-1\",\n                            children: [\n                                \"选中: \",\n                                selectedNode.name\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/WorkflowCanvas.tsx\",\n                            lineNumber: 172,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/WorkflowCanvas.tsx\",\n                    lineNumber: 169,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/WorkflowCanvas.tsx\",\n                lineNumber: 168,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: canvasRef,\n                className: \"w-full h-full relative cursor-crosshair\",\n                onClick: handleCanvasClick,\n                onDoubleClick: handleCanvasDoubleClick,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 opacity-20\",\n                        style: {\n                            backgroundImage: \"\\n              linear-gradient(to right, #e5e7eb 1px, transparent 1px),\\n              linear-gradient(to bottom, #e5e7eb 1px, transparent 1px)\\n            \",\n                            backgroundSize: '20px 20px'\n                        }\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/WorkflowCanvas.tsx\",\n                        lineNumber: 187,\n                        columnNumber: 9\n                    }, undefined),\n                    nodes.map(renderNode),\n                    nodes.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 flex items-center justify-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-gray-400 text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-lg mb-2\",\n                                    children: \"工作流画布\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/WorkflowCanvas.tsx\",\n                                    lineNumber: 205,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm\",\n                                    children: \"双击画布创建LED节点，或点击工具栏按钮\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/WorkflowCanvas.tsx\",\n                                    lineNumber: 206,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/WorkflowCanvas.tsx\",\n                            lineNumber: 204,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/WorkflowCanvas.tsx\",\n                        lineNumber: 203,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/WorkflowCanvas.tsx\",\n                lineNumber: 180,\n                columnNumber: 7\n            }, undefined),\n            showPropertyPanel && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_PropertyPanel__WEBPACK_IMPORTED_MODULE_4__.PropertyPanel, {\n                node: selectedNode,\n                onUpdateNode: updateNode,\n                onClose: ()=>setShowPropertyPanel(false)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/WorkflowCanvas.tsx\",\n                lineNumber: 214,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/WorkflowCanvas.tsx\",\n        lineNumber: 145,\n        columnNumber: 5\n    }, undefined);\n};\n_s(WorkflowCanvas, \"kOpDbL+UmWF6QzImBs/zcNJv4Kg=\");\n_c = WorkflowCanvas;\nvar _c;\n$RefreshReg$(_c, \"WorkflowCanvas\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/WorkflowCanvas.tsx\n"));

/***/ })

});