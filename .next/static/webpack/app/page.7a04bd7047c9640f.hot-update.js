"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/nodes/BaseNodeComponent.tsx":
/*!****************************************************!*\
  !*** ./src/components/nodes/BaseNodeComponent.tsx ***!
  \****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BaseNodeComponent: () => (/* binding */ BaseNodeComponent)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ BaseNodeComponent auto */ \nvar _s = $RefreshSig$();\n\nconst BaseNodeComponent = (param)=>{\n    let { node, onEvent, onUpdateNode, children } = param;\n    _s();\n    const [isDragging, setIsDragging] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [dragOffset, setDragOffset] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        x: 0,\n        y: 0\n    });\n    const nodeRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const animationFrameRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const pendingUpdateRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // 计算引脚位置\n    const calculatePinPositions = ()=>{\n        const inputPins = node.pins.filter((pin)=>pin.type === 'input');\n        const outputPins = node.pins.filter((pin)=>pin.type === 'output');\n        const maxPins = Math.max(inputPins.length, outputPins.length);\n        // 标题栏高度：padding(16px) + 文字高度(20px) + border(1px) = 37px\n        const titleHeight = 37;\n        // 底部留白\n        const bottomMargin = 12;\n        // 引脚区域顶部留白\n        const topMargin = 12;\n        // 可用于引脚的高度\n        const availableHeight = node.size.height - titleHeight - bottomMargin - topMargin;\n        // 引脚间距，如果只有一个引脚则居中，多个引脚则均匀分布\n        let pinSpacing = 0;\n        if (maxPins > 1) {\n            pinSpacing = availableHeight / (maxPins - 1);\n        }\n        return {\n            inputPins,\n            outputPins,\n            titleHeight,\n            topMargin,\n            pinSpacing,\n            availableHeight,\n            maxPins\n        };\n    };\n    const { inputPins, outputPins, titleHeight, topMargin, pinSpacing, maxPins } = calculatePinPositions();\n    // 使用requestAnimationFrame节流更新\n    const flushPendingUpdate = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"BaseNodeComponent.useCallback[flushPendingUpdate]\": ()=>{\n            if (pendingUpdateRef.current) {\n                onUpdateNode(node.id, {\n                    position: pendingUpdateRef.current\n                });\n                pendingUpdateRef.current = null;\n            }\n            animationFrameRef.current = null;\n        }\n    }[\"BaseNodeComponent.useCallback[flushPendingUpdate]\"], [\n        onUpdateNode,\n        node.id\n    ]);\n    const handleMouseMove = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"BaseNodeComponent.useCallback[handleMouseMove]\": (e)=>{\n            // 确保拖拽偏移量已经设置\n            if (dragOffset.x === 0 && dragOffset.y === 0) return;\n            const newPosition = {\n                x: e.clientX - dragOffset.x,\n                y: e.clientY - dragOffset.y\n            };\n            // 立即更新DOM位置以获得流畅的视觉效果\n            if (nodeRef.current) {\n                nodeRef.current.style.left = \"\".concat(newPosition.x, \"px\");\n                nodeRef.current.style.top = \"\".concat(newPosition.y, \"px\");\n            }\n            // 节流状态更新\n            pendingUpdateRef.current = newPosition;\n            if (animationFrameRef.current === null) {\n                animationFrameRef.current = requestAnimationFrame(flushPendingUpdate);\n            }\n        }\n    }[\"BaseNodeComponent.useCallback[handleMouseMove]\"], [\n        dragOffset.x,\n        dragOffset.y,\n        flushPendingUpdate\n    ]);\n    const handleMouseUp = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"BaseNodeComponent.useCallback[handleMouseUp]\": ()=>{\n            setIsDragging(false);\n            // 清理动画帧\n            if (animationFrameRef.current) {\n                cancelAnimationFrame(animationFrameRef.current);\n                animationFrameRef.current = null;\n            }\n            // 确保最后的位置更新被应用\n            if (pendingUpdateRef.current) {\n                onUpdateNode(node.id, {\n                    position: pendingUpdateRef.current\n                });\n                pendingUpdateRef.current = null;\n            }\n            onEvent({\n                type: 'dragEnd',\n                nodeId: node.id\n            });\n            // 移除全局鼠标事件监听\n            document.removeEventListener('mousemove', handleMouseMove);\n            document.removeEventListener('mouseup', handleMouseUp);\n        }\n    }[\"BaseNodeComponent.useCallback[handleMouseUp]\"], [\n        handleMouseMove,\n        onEvent,\n        onUpdateNode,\n        node.id\n    ]);\n    const handleMouseDown = (e)=>{\n        if (e.button !== 0) return; // 只处理左键\n        // 防止在引脚上开始拖拽\n        const target = e.target;\n        if (target.closest('.pin-connector')) {\n            return;\n        }\n        e.preventDefault();\n        e.stopPropagation();\n        // 使用节点的当前位置而不是getBoundingClientRect来计算偏移量\n        // 这样可以避免因为CSS变换或其他因素导致的位置偏差\n        setDragOffset({\n            x: e.clientX - node.position.x,\n            y: e.clientY - node.position.y\n        });\n        setIsDragging(true);\n        onEvent({\n            type: 'dragStart',\n            nodeId: node.id,\n            data: {\n                position: node.position\n            }\n        });\n        // 添加全局鼠标事件监听\n        document.addEventListener('mousemove', handleMouseMove);\n        document.addEventListener('mouseup', handleMouseUp);\n    };\n    const handleClick = (e)=>{\n        e.stopPropagation();\n        onEvent({\n            type: 'click',\n            nodeId: node.id\n        });\n    };\n    const handleDoubleClick = (e)=>{\n        e.stopPropagation();\n        onEvent({\n            type: 'doubleClick',\n            nodeId: node.id\n        });\n    };\n    const getStatusColor = ()=>{\n        switch(node.status){\n            case 'running':\n                return 'border-yellow-400 bg-yellow-50';\n            case 'success':\n                return 'border-green-400 bg-green-50';\n            case 'error':\n                return 'border-red-400 bg-red-50';\n            case 'warning':\n                return 'border-orange-400 bg-orange-50';\n            default:\n                return 'border-gray-300 bg-white';\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: nodeRef,\n        className: \"absolute border-2 rounded-lg shadow-md cursor-move select-none \".concat(getStatusColor(), \" \").concat(node.selected ? 'ring-2 ring-blue-400' : '', \" \").concat(isDragging ? 'shadow-lg' : ''),\n        style: {\n            left: node.position.x,\n            top: node.position.y,\n            width: node.size.width,\n            height: node.size.height\n        },\n        onMouseDown: handleMouseDown,\n        onClick: handleClick,\n        onDoubleClick: handleDoubleClick,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-2 py-2 bg-gray-100 border-b border-gray-200 rounded-t-md\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-sm font-medium text-gray-800 truncate\",\n                    children: node.name\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/nodes/BaseNodeComponent.tsx\",\n                    lineNumber: 196,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/nodes/BaseNodeComponent.tsx\",\n                lineNumber: 195,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-2 flex-1\",\n                children: children\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/nodes/BaseNodeComponent.tsx\",\n                lineNumber: 202,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute left-0\",\n                style: {\n                    top: titleHeight + topMargin\n                },\n                children: inputPins.map((pin, index)=>{\n                    // 如果只有一个引脚，居中显示；多个引脚则均匀分布\n                    const pinY = maxPins === 1 ? 0 : index * pinSpacing;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute flex items-center\",\n                        style: {\n                            top: pinY,\n                            left: -6,\n                            transform: 'translateY(-50%)' // 垂直居中对齐\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"pin-connector w-3 h-3 rounded-full border-2 cursor-pointer transition-colors \".concat(pin.connected ? 'bg-blue-500 border-blue-600' : 'bg-white border-gray-400 hover:border-blue-400'),\n                                title: pin.name\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/nodes/BaseNodeComponent.tsx\",\n                                lineNumber: 221,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"absolute left-4 top-1/2 transform -translate-y-1/2 text-xs text-gray-600 whitespace-nowrap\",\n                                children: pin.name\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/nodes/BaseNodeComponent.tsx\",\n                                lineNumber: 229,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, pin.id, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/nodes/BaseNodeComponent.tsx\",\n                        lineNumber: 212,\n                        columnNumber: 13\n                    }, undefined);\n                })\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/nodes/BaseNodeComponent.tsx\",\n                lineNumber: 207,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute right-0\",\n                style: {\n                    top: titleHeight + topMargin\n                },\n                children: outputPins.map((pin, index)=>{\n                    // 如果只有一个引脚，居中显示；多个引脚则均匀分布\n                    const pinY = maxPins === 1 ? 0 : index * pinSpacing;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute flex items-center justify-end\",\n                        style: {\n                            top: pinY,\n                            right: -6,\n                            transform: 'translateY(-50%)' // 垂直居中对齐\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"absolute right-4 top-1/2 transform -translate-y-1/2 text-xs text-gray-600 whitespace-nowrap\",\n                                children: pin.name\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/nodes/BaseNodeComponent.tsx\",\n                                lineNumber: 252,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"pin-connector w-3 h-3 rounded-full border-2 cursor-pointer transition-colors \".concat(pin.connected ? 'bg-green-500 border-green-600' : 'bg-white border-gray-400 hover:border-green-400'),\n                                title: pin.name\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/nodes/BaseNodeComponent.tsx\",\n                                lineNumber: 255,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, pin.id, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/nodes/BaseNodeComponent.tsx\",\n                        lineNumber: 243,\n                        columnNumber: 13\n                    }, undefined);\n                })\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/nodes/BaseNodeComponent.tsx\",\n                lineNumber: 238,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/nodes/BaseNodeComponent.tsx\",\n        lineNumber: 177,\n        columnNumber: 5\n    }, undefined);\n};\n_s(BaseNodeComponent, \"7O/WCX1jRM3DSG4/td1hT8DSAmU=\");\n_c = BaseNodeComponent;\nvar _c;\n$RefreshReg$(_c, \"BaseNodeComponent\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/nodes/BaseNodeComponent.tsx\n"));

/***/ })

});