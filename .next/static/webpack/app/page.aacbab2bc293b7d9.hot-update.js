"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/nodes/BaseNodeComponent.tsx":
/*!****************************************************!*\
  !*** ./src/components/nodes/BaseNodeComponent.tsx ***!
  \****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BaseNodeComponent: () => (/* binding */ BaseNodeComponent)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ BaseNodeComponent auto */ \nvar _s = $RefreshSig$();\n\nconst BaseNodeComponent = (param)=>{\n    let { node, onEvent, onUpdateNode, children } = param;\n    _s();\n    const [isDragging, setIsDragging] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [dragOffset, setDragOffset] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        x: 0,\n        y: 0\n    });\n    const nodeRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const handleMouseMove = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"BaseNodeComponent.useCallback[handleMouseMove]\": (e)=>{\n            const newPosition = {\n                x: e.clientX - dragOffset.x,\n                y: e.clientY - dragOffset.y\n            };\n            onEvent({\n                type: 'drag',\n                nodeId: node.id,\n                data: {\n                    position: newPosition\n                }\n            });\n            onUpdateNode(node.id, {\n                position: newPosition\n            });\n        }\n    }[\"BaseNodeComponent.useCallback[handleMouseMove]\"], [\n        dragOffset.x,\n        dragOffset.y,\n        onEvent,\n        onUpdateNode,\n        node.id\n    ]);\n    const handleMouseUp = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"BaseNodeComponent.useCallback[handleMouseUp]\": ()=>{\n            setIsDragging(false);\n            onEvent({\n                type: 'dragEnd',\n                nodeId: node.id\n            });\n            // 移除全局鼠标事件监听\n            document.removeEventListener('mousemove', handleMouseMove);\n            document.removeEventListener('mouseup', handleMouseUp);\n        }\n    }[\"BaseNodeComponent.useCallback[handleMouseUp]\"], [\n        handleMouseMove,\n        onEvent,\n        node.id\n    ]);\n    const handleMouseDown = (e)=>{\n        var _nodeRef_current;\n        if (e.button !== 0) return; // 只处理左键\n        const rect = (_nodeRef_current = nodeRef.current) === null || _nodeRef_current === void 0 ? void 0 : _nodeRef_current.getBoundingClientRect();\n        if (rect) {\n            setDragOffset({\n                x: e.clientX - rect.left,\n                y: e.clientY - rect.top\n            });\n        }\n        setIsDragging(true);\n        onEvent({\n            type: 'dragStart',\n            nodeId: node.id,\n            data: {\n                position: node.position\n            }\n        });\n        // 添加全局鼠标事件监听\n        document.addEventListener('mousemove', handleMouseMove);\n        document.addEventListener('mouseup', handleMouseUp);\n    };\n    const handleClick = (e)=>{\n        e.stopPropagation();\n        onEvent({\n            type: 'click',\n            nodeId: node.id\n        });\n    };\n    const handleDoubleClick = (e)=>{\n        e.stopPropagation();\n        onEvent({\n            type: 'doubleClick',\n            nodeId: node.id\n        });\n    };\n    const getStatusColor = ()=>{\n        switch(node.status){\n            case 'running':\n                return 'border-yellow-400 bg-yellow-50';\n            case 'success':\n                return 'border-green-400 bg-green-50';\n            case 'error':\n                return 'border-red-400 bg-red-50';\n            case 'warning':\n                return 'border-orange-400 bg-orange-50';\n            default:\n                return 'border-gray-300 bg-white';\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: nodeRef,\n        className: \"absolute border-2 rounded-lg shadow-md cursor-move select-none transition-all duration-200 \".concat(getStatusColor(), \" \").concat(node.selected ? 'ring-2 ring-blue-400' : '', \" \").concat(isDragging ? 'shadow-lg scale-105' : ''),\n        style: {\n            left: node.position.x,\n            top: node.position.y,\n            width: node.size.width,\n            height: node.size.height\n        },\n        onMouseDown: handleMouseDown,\n        onClick: handleClick,\n        onDoubleClick: handleDoubleClick,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-2 py-1 bg-gray-100 border-b border-gray-200 rounded-t-md\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-sm font-medium text-gray-800 truncate\",\n                        children: node.name\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/nodes/BaseNodeComponent.tsx\",\n                        lineNumber: 121,\n                        columnNumber: 9\n                    }, undefined),\n                    node.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-gray-500 truncate\",\n                        children: node.description\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/nodes/BaseNodeComponent.tsx\",\n                        lineNumber: 125,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/nodes/BaseNodeComponent.tsx\",\n                lineNumber: 120,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-2 flex-1\",\n                children: children\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/nodes/BaseNodeComponent.tsx\",\n                lineNumber: 132,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute left-0 top-8\",\n                children: node.pins.filter((pin)=>pin.type === 'input').map((pin, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative flex items-center\",\n                        style: {\n                            top: index * 24,\n                            left: -6\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-3 h-3 rounded-full border-2 cursor-pointer transition-colors \".concat(pin.connected ? 'bg-blue-500 border-blue-600' : 'bg-white border-gray-400 hover:border-blue-400'),\n                                title: pin.name\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/nodes/BaseNodeComponent.tsx\",\n                                lineNumber: 146,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"absolute left-4 top-0 text-xs text-gray-600 whitespace-nowrap leading-3\",\n                                children: pin.name\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/nodes/BaseNodeComponent.tsx\",\n                                lineNumber: 154,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, pin.id, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/nodes/BaseNodeComponent.tsx\",\n                        lineNumber: 141,\n                        columnNumber: 13\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/nodes/BaseNodeComponent.tsx\",\n                lineNumber: 137,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute right-0 top-8\",\n                children: node.pins.filter((pin)=>pin.type === 'output').map((pin, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative flex items-center justify-end\",\n                        style: {\n                            top: index * 24,\n                            right: -6\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"absolute right-4 top-0 text-xs text-gray-600 whitespace-nowrap leading-3\",\n                                children: pin.name\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/nodes/BaseNodeComponent.tsx\",\n                                lineNumber: 171,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-3 h-3 rounded-full border-2 cursor-pointer transition-colors \".concat(pin.connected ? 'bg-green-500 border-green-600' : 'bg-white border-gray-400 hover:border-green-400'),\n                                title: pin.name\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/nodes/BaseNodeComponent.tsx\",\n                                lineNumber: 174,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, pin.id, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/nodes/BaseNodeComponent.tsx\",\n                        lineNumber: 166,\n                        columnNumber: 13\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/nodes/BaseNodeComponent.tsx\",\n                lineNumber: 162,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/nodes/BaseNodeComponent.tsx\",\n        lineNumber: 102,\n        columnNumber: 5\n    }, undefined);\n};\n_s(BaseNodeComponent, \"f/4FiARzt2iWp9ZgfFPTJJP1yCA=\");\n_c = BaseNodeComponent;\nvar _c;\n$RefreshReg$(_c, \"BaseNodeComponent\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/nodes/BaseNodeComponent.tsx\n"));

/***/ })

});