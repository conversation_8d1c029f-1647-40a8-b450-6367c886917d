"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/nodes/LedNode.ts":
/*!******************************!*\
  !*** ./src/nodes/LedNode.ts ***!
  \******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LedNode: () => (/* binding */ LedNode)\n/* harmony export */ });\n/* harmony import */ var _BaseNode__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./BaseNode */ \"(app-pages-browser)/./src/nodes/BaseNode.ts\");\n\nclass LedNode extends _BaseNode__WEBPACK_IMPORTED_MODULE_0__.BaseNode {\n    initializePins() {\n        this.updatePinsConfiguration();\n    }\n    updatePinsConfiguration() {\n        // 清除现有引脚\n        this.data.pins = [];\n        const colorMode = this.getProperty('colorMode');\n        const inputRange = this.getProperty('inputRange');\n        // 根据颜色模式添加引脚\n        if (colorMode === '1color' || colorMode === '2color' || colorMode === '3color') {\n            this.addPin({\n                id: 'red_input',\n                name: 'R',\n                type: 'input',\n                dataType: inputRange === 'boolean' ? 'boolean' : 'number',\n                value: 0\n            });\n        }\n        if (colorMode === '2color' || colorMode === '3color') {\n            this.addPin({\n                id: 'green_input',\n                name: 'G',\n                type: 'input',\n                dataType: inputRange === 'boolean' ? 'boolean' : 'number',\n                value: 0\n            });\n        }\n        if (colorMode === '3color') {\n            this.addPin({\n                id: 'blue_input',\n                name: 'B',\n                type: 'input',\n                dataType: inputRange === 'boolean' ? 'boolean' : 'number',\n                value: 0\n            });\n        }\n        // 添加亮度控制引脚\n        this.addPin({\n            id: 'brightness_input',\n            name: 'Brightness',\n            type: 'input',\n            dataType: 'number',\n            value: 100\n        });\n        // 添加输出引脚\n        this.addPin({\n            id: 'color_output',\n            name: 'Color',\n            type: 'output',\n            dataType: 'color',\n            value: '#000000'\n        });\n    }\n    async process() {\n        try {\n            this.setStatus('running');\n            const colorMode = this.getProperty('colorMode');\n            const inputRange = this.getProperty('inputRange');\n            const brightness = this.getPinValue('brightness_input') || this.getProperty('brightness') || 100;\n            let red = 0, green = 0, blue = 0;\n            // 获取输入值\n            if (colorMode === '1color' || colorMode === '2color' || colorMode === '3color') {\n                red = this.normalizeInputValue(this.getPinValue('red_input') || 0, inputRange);\n            }\n            if (colorMode === '2color' || colorMode === '3color') {\n                green = this.normalizeInputValue(this.getPinValue('green_input') || 0, inputRange);\n            }\n            if (colorMode === '3color') {\n                blue = this.normalizeInputValue(this.getPinValue('blue_input') || 0, inputRange);\n            }\n            // 应用亮度\n            const brightnessMultiplier = Math.max(0, Math.min(100, brightness)) / 100;\n            red = Math.round(red * brightnessMultiplier);\n            green = Math.round(green * brightnessMultiplier);\n            blue = Math.round(blue * brightnessMultiplier);\n            // 更新属性值\n            this.setProperty('redValue', red);\n            this.setProperty('greenValue', green);\n            this.setProperty('blueValue', blue);\n            // 生成颜色输出\n            const colorHex = \"#\".concat(red.toString(16).padStart(2, '0')).concat(green.toString(16).padStart(2, '0')).concat(blue.toString(16).padStart(2, '0'));\n            this.setPinValue('color_output', colorHex);\n            this.setStatus('success');\n        } catch (error) {\n            console.error('LED Node processing error:', error);\n            this.setStatus('error');\n        }\n    }\n    normalizeInputValue(value, inputRange) {\n        if (typeof value === 'boolean') {\n            return value ? 255 : 0;\n        }\n        const numValue = Number(value) || 0;\n        switch(inputRange){\n            case 'percentage':\n                return Math.round(Math.max(0, Math.min(100, numValue)) * 2.55);\n            case '0-255':\n                return Math.round(Math.max(0, Math.min(255, numValue)));\n            case '0-1':\n                return Math.round(Math.max(0, Math.min(1, numValue)) * 255);\n            case 'boolean':\n                return numValue > 0 ? 255 : 0;\n            default:\n                return Math.round(Math.max(0, Math.min(255, numValue)));\n        }\n    }\n    onPropertyChanged(key, value) {\n        if (key === 'colorMode' || key === 'inputRange') {\n            this.updatePinsConfiguration();\n        }\n        super.onPropertyChanged(key, value);\n    }\n    constructor(data){\n        super({\n            type: 'led',\n            name: 'RGB LED',\n            description: '3色变色LED灯节点',\n            size: {\n                width: 140,\n                height: 100\n            },\n            properties: {\n                colorMode: '3color',\n                inputRange: 'percentage',\n                redValue: 0,\n                greenValue: 0,\n                blueValue: 0,\n                brightness: 100\n            },\n            ...data\n        });\n        this.initializePins();\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/nodes/LedNode.ts\n"));

/***/ })

});