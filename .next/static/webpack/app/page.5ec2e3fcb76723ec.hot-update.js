"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/WorkflowCanvas.tsx":
/*!*******************************************!*\
  !*** ./src/components/WorkflowCanvas.tsx ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   WorkflowCanvas: () => (/* binding */ WorkflowCanvas)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _nodes_LedNode__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/nodes/LedNode */ \"(app-pages-browser)/./src/nodes/LedNode.ts\");\n/* harmony import */ var _nodes_LedNodeComponent__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./nodes/LedNodeComponent */ \"(app-pages-browser)/./src/components/nodes/LedNodeComponent.tsx\");\n/* harmony import */ var _PropertyPanel__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./PropertyPanel */ \"(app-pages-browser)/./src/components/PropertyPanel.tsx\");\n/* __next_internal_client_entry_do_not_use__ WorkflowCanvas auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst WorkflowCanvas = ()=>{\n    _s();\n    const [nodes, setNodes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedNode, setSelectedNode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showPropertyPanel, setShowPropertyPanel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const canvasRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const nodeInstancesRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(new Map());\n    // 创建新的LED节点\n    const createLedNode = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"WorkflowCanvas.useCallback[createLedNode]\": (position)=>{\n            const ledNode = new _nodes_LedNode__WEBPACK_IMPORTED_MODULE_2__.LedNode({\n                position\n            });\n            const nodeData = ledNode.getData();\n            console.log('创建LED节点:', nodeData);\n            setNodes({\n                \"WorkflowCanvas.useCallback[createLedNode]\": (prev)=>[\n                        ...prev,\n                        nodeData\n                    ]\n            }[\"WorkflowCanvas.useCallback[createLedNode]\"]);\n            nodeInstancesRef.current.set(nodeData.id, ledNode);\n            return nodeData;\n        }\n    }[\"WorkflowCanvas.useCallback[createLedNode]\"], []);\n    // 更新节点数据\n    const updateNode = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"WorkflowCanvas.useCallback[updateNode]\": (nodeId, updates)=>{\n            // 使用函数式更新减少重新渲染\n            setNodes({\n                \"WorkflowCanvas.useCallback[updateNode]\": (prev)=>{\n                    const nodeIndex = prev.findIndex({\n                        \"WorkflowCanvas.useCallback[updateNode].nodeIndex\": (node)=>node.id === nodeId\n                    }[\"WorkflowCanvas.useCallback[updateNode].nodeIndex\"]);\n                    if (nodeIndex === -1) return prev;\n                    const currentNode = prev[nodeIndex];\n                    const updatedNode = {\n                        ...currentNode,\n                        ...updates\n                    };\n                    // 检查是否真的有变化，避免不必要的更新\n                    if (JSON.stringify(currentNode) === JSON.stringify(updatedNode)) {\n                        return prev;\n                    }\n                    // 更新节点实例\n                    const nodeInstance = nodeInstancesRef.current.get(nodeId);\n                    if (nodeInstance) {\n                        nodeInstance.updateData(updatedNode);\n                        // 如果属性发生变化，触发处理\n                        if (updates.properties) {\n                            Object.entries(updates.properties).forEach({\n                                \"WorkflowCanvas.useCallback[updateNode]\": (param)=>{\n                                    let [key, value] = param;\n                                    nodeInstance.setProperty(key, value);\n                                }\n                            }[\"WorkflowCanvas.useCallback[updateNode]\"]);\n                        }\n                    }\n                    // 创建新数组，只更新变化的节点\n                    const newNodes = [\n                        ...prev\n                    ];\n                    newNodes[nodeIndex] = updatedNode;\n                    return newNodes;\n                }\n            }[\"WorkflowCanvas.useCallback[updateNode]\"]);\n            // 如果更新的是当前选中的节点，也更新选中状态\n            if ((selectedNode === null || selectedNode === void 0 ? void 0 : selectedNode.id) === nodeId) {\n                setSelectedNode({\n                    \"WorkflowCanvas.useCallback[updateNode]\": (prev)=>prev ? {\n                            ...prev,\n                            ...updates\n                        } : null\n                }[\"WorkflowCanvas.useCallback[updateNode]\"]);\n            }\n        }\n    }[\"WorkflowCanvas.useCallback[updateNode]\"], [\n        selectedNode\n    ]);\n    // 处理节点事件\n    const handleNodeEvent = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"WorkflowCanvas.useCallback[handleNodeEvent]\": (event)=>{\n            const nodeInstance = nodeInstancesRef.current.get(event.nodeId);\n            if (nodeInstance) {\n                nodeInstance.handleEvent(event);\n            }\n            switch(event.type){\n                case 'click':\n                    const clickedNode = nodes.find({\n                        \"WorkflowCanvas.useCallback[handleNodeEvent].clickedNode\": (n)=>n.id === event.nodeId\n                    }[\"WorkflowCanvas.useCallback[handleNodeEvent].clickedNode\"]);\n                    if (clickedNode) {\n                        // 取消其他节点的选中状态\n                        setNodes({\n                            \"WorkflowCanvas.useCallback[handleNodeEvent]\": (prev)=>prev.map({\n                                    \"WorkflowCanvas.useCallback[handleNodeEvent]\": (node)=>({\n                                            ...node,\n                                            selected: node.id === event.nodeId\n                                        })\n                                }[\"WorkflowCanvas.useCallback[handleNodeEvent]\"])\n                        }[\"WorkflowCanvas.useCallback[handleNodeEvent]\"]);\n                        setSelectedNode(clickedNode);\n                    }\n                    break;\n                case 'doubleClick':\n                    const doubleClickedNode = nodes.find({\n                        \"WorkflowCanvas.useCallback[handleNodeEvent].doubleClickedNode\": (n)=>n.id === event.nodeId\n                    }[\"WorkflowCanvas.useCallback[handleNodeEvent].doubleClickedNode\"]);\n                    if (doubleClickedNode) {\n                        setSelectedNode(doubleClickedNode);\n                        setShowPropertyPanel(true);\n                    }\n                    break;\n            }\n        }\n    }[\"WorkflowCanvas.useCallback[handleNodeEvent]\"], [\n        nodes\n    ]);\n    // 处理画布点击\n    const handleCanvasClick = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"WorkflowCanvas.useCallback[handleCanvasClick]\": (e)=>{\n            if (e.target === canvasRef.current) {\n                // 取消所有节点的选中状态\n                setNodes({\n                    \"WorkflowCanvas.useCallback[handleCanvasClick]\": (prev)=>prev.map({\n                            \"WorkflowCanvas.useCallback[handleCanvasClick]\": (node)=>({\n                                    ...node,\n                                    selected: false\n                                })\n                        }[\"WorkflowCanvas.useCallback[handleCanvasClick]\"])\n                }[\"WorkflowCanvas.useCallback[handleCanvasClick]\"]);\n                setSelectedNode(null);\n            }\n        }\n    }[\"WorkflowCanvas.useCallback[handleCanvasClick]\"], []);\n    // 处理画布双击 - 创建新节点\n    const handleCanvasDoubleClick = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"WorkflowCanvas.useCallback[handleCanvasDoubleClick]\": (e)=>{\n            if (e.target === canvasRef.current) {\n                const rect = canvasRef.current.getBoundingClientRect();\n                const position = {\n                    x: e.clientX - rect.left - 60,\n                    y: e.clientY - rect.top - 40\n                };\n                createLedNode(position);\n            }\n        }\n    }[\"WorkflowCanvas.useCallback[handleCanvasDoubleClick]\"], [\n        createLedNode\n    ]);\n    // 渲染节点\n    const renderNode = (node)=>{\n        switch(node.type){\n            case 'led':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_nodes_LedNodeComponent__WEBPACK_IMPORTED_MODULE_3__.LedNodeComponent, {\n                    node: node,\n                    onEvent: handleNodeEvent,\n                    onUpdateNode: updateNode\n                }, node.id, false, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/WorkflowCanvas.tsx\",\n                    lineNumber: 126,\n                    columnNumber: 11\n                }, undefined);\n            default:\n                return null;\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative w-full h-screen bg-gray-50 overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-4 left-4 z-10 bg-white rounded-lg shadow-md p-2 flex space-x-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>createLedNode({\n                                x: 100,\n                                y: 100\n                            }),\n                        className: \"px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors\",\n                        children: \"添加LED节点\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/WorkflowCanvas.tsx\",\n                        lineNumber: 142,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>{\n                            setNodes([]);\n                            nodeInstancesRef.current.clear();\n                            setSelectedNode(null);\n                            setShowPropertyPanel(false);\n                        },\n                        className: \"px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600 transition-colors\",\n                        children: \"清空画布\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/WorkflowCanvas.tsx\",\n                        lineNumber: 148,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/WorkflowCanvas.tsx\",\n                lineNumber: 141,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-4 right-4 z-10 bg-white rounded-lg shadow-md p-2\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-sm text-gray-600\",\n                    children: [\n                        \"节点数量: \",\n                        nodes.length,\n                        selectedNode && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-1\",\n                            children: [\n                                \"选中: \",\n                                selectedNode.name\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/WorkflowCanvas.tsx\",\n                            lineNumber: 166,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/WorkflowCanvas.tsx\",\n                    lineNumber: 163,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/WorkflowCanvas.tsx\",\n                lineNumber: 162,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: canvasRef,\n                className: \"w-full h-full relative cursor-crosshair\",\n                onClick: handleCanvasClick,\n                onDoubleClick: handleCanvasDoubleClick,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 opacity-20\",\n                        style: {\n                            backgroundImage: \"\\n              linear-gradient(to right, #e5e7eb 1px, transparent 1px),\\n              linear-gradient(to bottom, #e5e7eb 1px, transparent 1px)\\n            \",\n                            backgroundSize: '20px 20px'\n                        }\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/WorkflowCanvas.tsx\",\n                        lineNumber: 181,\n                        columnNumber: 9\n                    }, undefined),\n                    nodes.map(renderNode),\n                    nodes.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 flex items-center justify-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-gray-400 text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-lg mb-2\",\n                                    children: \"工作流画布\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/WorkflowCanvas.tsx\",\n                                    lineNumber: 199,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm\",\n                                    children: \"双击画布创建LED节点，或点击工具栏按钮\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/WorkflowCanvas.tsx\",\n                                    lineNumber: 200,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/WorkflowCanvas.tsx\",\n                            lineNumber: 198,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/WorkflowCanvas.tsx\",\n                        lineNumber: 197,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/WorkflowCanvas.tsx\",\n                lineNumber: 174,\n                columnNumber: 7\n            }, undefined),\n            showPropertyPanel && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_PropertyPanel__WEBPACK_IMPORTED_MODULE_4__.PropertyPanel, {\n                node: selectedNode,\n                onUpdateNode: updateNode,\n                onClose: ()=>setShowPropertyPanel(false)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/WorkflowCanvas.tsx\",\n                lineNumber: 208,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/WorkflowCanvas.tsx\",\n        lineNumber: 139,\n        columnNumber: 5\n    }, undefined);\n};\n_s(WorkflowCanvas, \"kOpDbL+UmWF6QzImBs/zcNJv4Kg=\");\n_c = WorkflowCanvas;\nvar _c;\n$RefreshReg$(_c, \"WorkflowCanvas\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/WorkflowCanvas.tsx\n"));

/***/ })

});