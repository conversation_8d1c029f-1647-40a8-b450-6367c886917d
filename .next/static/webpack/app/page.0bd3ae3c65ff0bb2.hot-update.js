"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/nodes/BaseNodeComponent.tsx":
/*!****************************************************!*\
  !*** ./src/components/nodes/BaseNodeComponent.tsx ***!
  \****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BaseNodeComponent: () => (/* binding */ BaseNodeComponent)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ BaseNodeComponent auto */ \nvar _s = $RefreshSig$();\n\nconst BaseNodeComponent = (param)=>{\n    let { node, onEvent, onUpdateNode, children } = param;\n    _s();\n    const [isDragging, setIsDragging] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [dragOffset, setDragOffset] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        x: 0,\n        y: 0\n    });\n    const nodeRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // 计算引脚位置\n    const calculatePinPositions = ()=>{\n        const inputPins = node.pins.filter((pin)=>pin.type === 'input');\n        const outputPins = node.pins.filter((pin)=>pin.type === 'output');\n        const maxPins = Math.max(inputPins.length, outputPins.length);\n        // 标题栏高度约为 32px (包含padding和border)\n        const titleHeight = 32;\n        // 底部留白\n        const bottomMargin = 8;\n        // 可用于引脚的高度\n        const availableHeight = node.size.height - titleHeight - bottomMargin;\n        // 引脚间距，最小16px\n        const pinSpacing = Math.max(16, availableHeight / Math.max(1, maxPins - 1));\n        return {\n            inputPins,\n            outputPins,\n            titleHeight,\n            pinSpacing,\n            availableHeight\n        };\n    };\n    const { inputPins, outputPins, titleHeight, pinSpacing } = calculatePinPositions();\n    const handleMouseMove = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"BaseNodeComponent.useCallback[handleMouseMove]\": (e)=>{\n            const newPosition = {\n                x: e.clientX - dragOffset.x,\n                y: e.clientY - dragOffset.y\n            };\n            // 直接更新节点位置，减少事件调用\n            onUpdateNode(node.id, {\n                position: newPosition\n            });\n        }\n    }[\"BaseNodeComponent.useCallback[handleMouseMove]\"], [\n        dragOffset.x,\n        dragOffset.y,\n        onUpdateNode,\n        node.id\n    ]);\n    const handleMouseUp = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"BaseNodeComponent.useCallback[handleMouseUp]\": ()=>{\n            setIsDragging(false);\n            onEvent({\n                type: 'dragEnd',\n                nodeId: node.id\n            });\n            // 移除全局鼠标事件监听\n            document.removeEventListener('mousemove', handleMouseMove);\n            document.removeEventListener('mouseup', handleMouseUp);\n        }\n    }[\"BaseNodeComponent.useCallback[handleMouseUp]\"], [\n        handleMouseMove,\n        onEvent,\n        node.id\n    ]);\n    const handleMouseDown = (e)=>{\n        var _nodeRef_current;\n        if (e.button !== 0) return; // 只处理左键\n        // 防止在引脚上开始拖拽\n        const target = e.target;\n        if (target.closest('.pin-connector')) {\n            return;\n        }\n        e.preventDefault();\n        e.stopPropagation();\n        const rect = (_nodeRef_current = nodeRef.current) === null || _nodeRef_current === void 0 ? void 0 : _nodeRef_current.getBoundingClientRect();\n        if (rect) {\n            setDragOffset({\n                x: e.clientX - rect.left,\n                y: e.clientY - rect.top\n            });\n        }\n        setIsDragging(true);\n        onEvent({\n            type: 'dragStart',\n            nodeId: node.id,\n            data: {\n                position: node.position\n            }\n        });\n        // 添加全局鼠标事件监听\n        document.addEventListener('mousemove', handleMouseMove);\n        document.addEventListener('mouseup', handleMouseUp);\n    };\n    const handleClick = (e)=>{\n        e.stopPropagation();\n        onEvent({\n            type: 'click',\n            nodeId: node.id\n        });\n    };\n    const handleDoubleClick = (e)=>{\n        e.stopPropagation();\n        onEvent({\n            type: 'doubleClick',\n            nodeId: node.id\n        });\n    };\n    const getStatusColor = ()=>{\n        switch(node.status){\n            case 'running':\n                return 'border-yellow-400 bg-yellow-50';\n            case 'success':\n                return 'border-green-400 bg-green-50';\n            case 'error':\n                return 'border-red-400 bg-red-50';\n            case 'warning':\n                return 'border-orange-400 bg-orange-50';\n            default:\n                return 'border-gray-300 bg-white';\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: nodeRef,\n        className: \"absolute border-2 rounded-lg shadow-md cursor-move select-none transition-all duration-200 \".concat(getStatusColor(), \" \").concat(node.selected ? 'ring-2 ring-blue-400' : '', \" \").concat(isDragging ? 'shadow-lg scale-105' : ''),\n        style: {\n            left: node.position.x,\n            top: node.position.y,\n            width: node.size.width,\n            height: node.size.height\n        },\n        onMouseDown: handleMouseDown,\n        onClick: handleClick,\n        onDoubleClick: handleDoubleClick,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-2 py-1 bg-gray-100 border-b border-gray-200 rounded-t-md\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-sm font-medium text-gray-800 truncate\",\n                        children: node.name\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/nodes/BaseNodeComponent.tsx\",\n                        lineNumber: 152,\n                        columnNumber: 9\n                    }, undefined),\n                    node.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-gray-500 truncate\",\n                        children: node.description\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/nodes/BaseNodeComponent.tsx\",\n                        lineNumber: 156,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/nodes/BaseNodeComponent.tsx\",\n                lineNumber: 151,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-2 flex-1\",\n                children: children\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/nodes/BaseNodeComponent.tsx\",\n                lineNumber: 163,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute left-0 top-8\",\n                children: node.pins.filter((pin)=>pin.type === 'input').map((pin, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative flex items-center\",\n                        style: {\n                            top: index * 24,\n                            left: -6\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"pin-connector w-3 h-3 rounded-full border-2 cursor-pointer transition-colors \".concat(pin.connected ? 'bg-blue-500 border-blue-600' : 'bg-white border-gray-400 hover:border-blue-400'),\n                                title: pin.name\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/nodes/BaseNodeComponent.tsx\",\n                                lineNumber: 177,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"absolute left-4 top-0 text-xs text-gray-600 whitespace-nowrap leading-3\",\n                                children: pin.name\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/nodes/BaseNodeComponent.tsx\",\n                                lineNumber: 185,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, pin.id, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/nodes/BaseNodeComponent.tsx\",\n                        lineNumber: 172,\n                        columnNumber: 13\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/nodes/BaseNodeComponent.tsx\",\n                lineNumber: 168,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute right-0 top-8\",\n                children: node.pins.filter((pin)=>pin.type === 'output').map((pin, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative flex items-center justify-end\",\n                        style: {\n                            top: index * 24,\n                            right: -6\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"absolute right-4 top-0 text-xs text-gray-600 whitespace-nowrap leading-3\",\n                                children: pin.name\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/nodes/BaseNodeComponent.tsx\",\n                                lineNumber: 202,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"pin-connector w-3 h-3 rounded-full border-2 cursor-pointer transition-colors \".concat(pin.connected ? 'bg-green-500 border-green-600' : 'bg-white border-gray-400 hover:border-green-400'),\n                                title: pin.name\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/nodes/BaseNodeComponent.tsx\",\n                                lineNumber: 205,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, pin.id, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/nodes/BaseNodeComponent.tsx\",\n                        lineNumber: 197,\n                        columnNumber: 13\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/nodes/BaseNodeComponent.tsx\",\n                lineNumber: 193,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/nodes/BaseNodeComponent.tsx\",\n        lineNumber: 133,\n        columnNumber: 5\n    }, undefined);\n};\n_s(BaseNodeComponent, \"f/4FiARzt2iWp9ZgfFPTJJP1yCA=\");\n_c = BaseNodeComponent;\nvar _c;\n$RefreshReg$(_c, \"BaseNodeComponent\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/nodes/BaseNodeComponent.tsx\n"));

/***/ })

});