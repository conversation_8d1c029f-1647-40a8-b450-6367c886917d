"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/nodes/BaseNodeComponent.tsx":
/*!****************************************************!*\
  !*** ./src/components/nodes/BaseNodeComponent.tsx ***!
  \****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BaseNodeComponent: () => (/* binding */ BaseNodeComponent)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ BaseNodeComponent auto */ \nvar _s = $RefreshSig$();\n\nconst BaseNodeComponent = (param)=>{\n    let { node, onEvent, onUpdateNode, children } = param;\n    _s();\n    const [isDragging, setIsDragging] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [dragOffset, setDragOffset] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        x: 0,\n        y: 0\n    });\n    const nodeRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // 计算引脚位置\n    const calculatePinPositions = ()=>{\n        const inputPins = node.pins.filter((pin)=>pin.type === 'input');\n        const outputPins = node.pins.filter((pin)=>pin.type === 'output');\n        const maxPins = Math.max(inputPins.length, outputPins.length);\n        // 标题栏高度约为 32px (包含padding和border)\n        const titleHeight = 32;\n        // 底部留白\n        const bottomMargin = 8;\n        // 可用于引脚的高度\n        const availableHeight = node.size.height - titleHeight - bottomMargin;\n        // 引脚间距，最小16px\n        const pinSpacing = Math.max(16, availableHeight / Math.max(1, maxPins - 1));\n        return {\n            inputPins,\n            outputPins,\n            titleHeight,\n            pinSpacing,\n            availableHeight\n        };\n    };\n    const { inputPins, outputPins, titleHeight, pinSpacing } = calculatePinPositions();\n    const handleMouseMove = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"BaseNodeComponent.useCallback[handleMouseMove]\": (e)=>{\n            const newPosition = {\n                x: e.clientX - dragOffset.x,\n                y: e.clientY - dragOffset.y\n            };\n            // 直接更新节点位置，减少事件调用\n            onUpdateNode(node.id, {\n                position: newPosition\n            });\n        }\n    }[\"BaseNodeComponent.useCallback[handleMouseMove]\"], [\n        dragOffset.x,\n        dragOffset.y,\n        onUpdateNode,\n        node.id\n    ]);\n    const handleMouseUp = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"BaseNodeComponent.useCallback[handleMouseUp]\": ()=>{\n            setIsDragging(false);\n            onEvent({\n                type: 'dragEnd',\n                nodeId: node.id\n            });\n            // 移除全局鼠标事件监听\n            document.removeEventListener('mousemove', handleMouseMove);\n            document.removeEventListener('mouseup', handleMouseUp);\n        }\n    }[\"BaseNodeComponent.useCallback[handleMouseUp]\"], [\n        handleMouseMove,\n        onEvent,\n        node.id\n    ]);\n    const handleMouseDown = (e)=>{\n        var _nodeRef_current;\n        if (e.button !== 0) return; // 只处理左键\n        // 防止在引脚上开始拖拽\n        const target = e.target;\n        if (target.closest('.pin-connector')) {\n            return;\n        }\n        e.preventDefault();\n        e.stopPropagation();\n        const rect = (_nodeRef_current = nodeRef.current) === null || _nodeRef_current === void 0 ? void 0 : _nodeRef_current.getBoundingClientRect();\n        if (rect) {\n            setDragOffset({\n                x: e.clientX - rect.left,\n                y: e.clientY - rect.top\n            });\n        }\n        setIsDragging(true);\n        onEvent({\n            type: 'dragStart',\n            nodeId: node.id,\n            data: {\n                position: node.position\n            }\n        });\n        // 添加全局鼠标事件监听\n        document.addEventListener('mousemove', handleMouseMove);\n        document.addEventListener('mouseup', handleMouseUp);\n    };\n    const handleClick = (e)=>{\n        e.stopPropagation();\n        onEvent({\n            type: 'click',\n            nodeId: node.id\n        });\n    };\n    const handleDoubleClick = (e)=>{\n        e.stopPropagation();\n        onEvent({\n            type: 'doubleClick',\n            nodeId: node.id\n        });\n    };\n    const getStatusColor = ()=>{\n        switch(node.status){\n            case 'running':\n                return 'border-yellow-400 bg-yellow-50';\n            case 'success':\n                return 'border-green-400 bg-green-50';\n            case 'error':\n                return 'border-red-400 bg-red-50';\n            case 'warning':\n                return 'border-orange-400 bg-orange-50';\n            default:\n                return 'border-gray-300 bg-white';\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: nodeRef,\n        className: \"absolute border-2 rounded-lg shadow-md cursor-move select-none transition-all duration-200 \".concat(getStatusColor(), \" \").concat(node.selected ? 'ring-2 ring-blue-400' : '', \" \").concat(isDragging ? 'shadow-lg scale-105' : ''),\n        style: {\n            left: node.position.x,\n            top: node.position.y,\n            width: node.size.width,\n            height: node.size.height\n        },\n        onMouseDown: handleMouseDown,\n        onClick: handleClick,\n        onDoubleClick: handleDoubleClick,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-2 py-1 bg-gray-100 border-b border-gray-200 rounded-t-md\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-sm font-medium text-gray-800 truncate\",\n                        children: node.name\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/nodes/BaseNodeComponent.tsx\",\n                        lineNumber: 152,\n                        columnNumber: 9\n                    }, undefined),\n                    node.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-gray-500 truncate\",\n                        children: node.description\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/nodes/BaseNodeComponent.tsx\",\n                        lineNumber: 156,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/nodes/BaseNodeComponent.tsx\",\n                lineNumber: 151,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-2 flex-1\",\n                children: children\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/nodes/BaseNodeComponent.tsx\",\n                lineNumber: 163,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute left-0\",\n                style: {\n                    top: titleHeight\n                },\n                children: inputPins.map((pin, index)=>{\n                    const pinY = index * pinSpacing;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute flex items-center\",\n                        style: {\n                            top: pinY,\n                            left: -6,\n                            transform: 'translateY(-50%)' // 垂直居中对齐\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"pin-connector w-3 h-3 rounded-full border-2 cursor-pointer transition-colors \".concat(pin.connected ? 'bg-blue-500 border-blue-600' : 'bg-white border-gray-400 hover:border-blue-400'),\n                                title: pin.name\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/nodes/BaseNodeComponent.tsx\",\n                                lineNumber: 181,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"absolute left-4 top-1/2 transform -translate-y-1/2 text-xs text-gray-600 whitespace-nowrap\",\n                                children: pin.name\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/nodes/BaseNodeComponent.tsx\",\n                                lineNumber: 189,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, pin.id, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/nodes/BaseNodeComponent.tsx\",\n                        lineNumber: 172,\n                        columnNumber: 13\n                    }, undefined);\n                })\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/nodes/BaseNodeComponent.tsx\",\n                lineNumber: 168,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute right-0\",\n                style: {\n                    top: titleHeight\n                },\n                children: outputPins.map((pin, index)=>{\n                    const pinY = index * pinSpacing;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute flex items-center justify-end\",\n                        style: {\n                            top: pinY,\n                            right: -6,\n                            transform: 'translateY(-50%)' // 垂直居中对齐\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"absolute right-4 top-1/2 transform -translate-y-1/2 text-xs text-gray-600 whitespace-nowrap\",\n                                children: pin.name\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/nodes/BaseNodeComponent.tsx\",\n                                lineNumber: 211,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"pin-connector w-3 h-3 rounded-full border-2 cursor-pointer transition-colors \".concat(pin.connected ? 'bg-green-500 border-green-600' : 'bg-white border-gray-400 hover:border-green-400'),\n                                title: pin.name\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/nodes/BaseNodeComponent.tsx\",\n                                lineNumber: 214,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, pin.id, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/nodes/BaseNodeComponent.tsx\",\n                        lineNumber: 202,\n                        columnNumber: 13\n                    }, undefined);\n                })\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/nodes/BaseNodeComponent.tsx\",\n                lineNumber: 198,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/nodes/BaseNodeComponent.tsx\",\n        lineNumber: 133,\n        columnNumber: 5\n    }, undefined);\n};\n_s(BaseNodeComponent, \"f/4FiARzt2iWp9ZgfFPTJJP1yCA=\");\n_c = BaseNodeComponent;\nvar _c;\n$RefreshReg$(_c, \"BaseNodeComponent\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/nodes/BaseNodeComponent.tsx\n"));

/***/ })

});