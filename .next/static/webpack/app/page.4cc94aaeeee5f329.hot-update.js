"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/nodes/BaseNode.ts":
/*!*******************************!*\
  !*** ./src/nodes/BaseNode.ts ***!
  \*******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BaseNode: () => (/* binding */ BaseNode)\n/* harmony export */ });\nclass BaseNode {\n    // 生成唯一ID\n    generateId() {\n        return \"node_\".concat(Date.now(), \"_\").concat(Math.random().toString(36).substr(2, 9));\n    }\n    // 获取节点数据\n    getData() {\n        return {\n            ...this.data\n        };\n    }\n    // 更新节点数据\n    updateData(updates) {\n        this.data = {\n            ...this.data,\n            ...updates\n        };\n    }\n    // 设置节点状态\n    setStatus(status) {\n        this.data.status = status;\n    }\n    // 添加引脚\n    addPin(pin) {\n        this.data.pins.push(pin);\n    }\n    // 移除引脚\n    removePin(pinId) {\n        this.data.pins = this.data.pins.filter((pin)=>pin.id !== pinId);\n    }\n    // 获取引脚\n    getPin(pinId) {\n        return this.data.pins.find((pin)=>pin.id === pinId);\n    }\n    // 设置引脚值\n    setPinValue(pinId, value) {\n        const pin = this.getPin(pinId);\n        if (pin) {\n            pin.value = value;\n            this.onPinValueChanged(pinId, value);\n        }\n    }\n    // 获取引脚值\n    getPinValue(pinId) {\n        const pin = this.getPin(pinId);\n        return pin === null || pin === void 0 ? void 0 : pin.value;\n    }\n    // 设置属性\n    setProperty(key, value) {\n        this.data.properties[key] = value;\n        this.onPropertyChanged(key, value);\n    }\n    // 获取属性\n    getProperty(key) {\n        return this.data.properties[key];\n    }\n    // 处理事件\n    handleEvent(event) {\n        switch(event.type){\n            case 'click':\n                this.onClick(event);\n                break;\n            case 'doubleClick':\n                this.onDoubleClick(event);\n                break;\n            case 'dragStart':\n                this.onDragStart(event);\n                break;\n            case 'drag':\n                this.onDrag(event);\n                break;\n            case 'dragEnd':\n                this.onDragEnd(event);\n                break;\n            case 'pinConnect':\n                this.onPinConnect(event);\n                break;\n            case 'pinDisconnect':\n                this.onPinDisconnect(event);\n                break;\n            case 'propertyChange':\n                this.onPropertyChange(event);\n                break;\n        }\n    }\n    // 事件处理方法 - 子类可以重写\n    onClick(event) {}\n    onDoubleClick(event) {}\n    onDragStart(event) {\n        this.data.dragging = true;\n    }\n    onDrag(event) {\n        if (event.data && this.data.dragging) {\n            this.data.position = event.data.position;\n        }\n    }\n    onDragEnd(event) {\n        this.data.dragging = false;\n    }\n    onPinConnect(event) {}\n    onPinDisconnect(event) {}\n    onPropertyChange(event) {}\n    onPinValueChanged(pinId, value) {\n        // 当引脚值改变时触发处理\n        this.process();\n    }\n    onPropertyChanged(key, value) {\n        // 当属性改变时可能需要重新配置引脚\n        this.updatePinsConfiguration();\n    }\n    // 更新引脚配置 - 子类可以重写\n    updatePinsConfiguration() {\n        // 更新引脚配置后，重新计算节点高度\n        this.updateNodeSize();\n    }\n    // 根据引脚数量计算并更新节点尺寸\n    updateNodeSize() {\n        const inputPins = this.data.pins.filter((pin)=>pin.type === 'input');\n        const outputPins = this.data.pins.filter((pin)=>pin.type === 'output');\n        const maxPins = Math.max(inputPins.length, outputPins.length);\n        // 标题栏高度：37px\n        const titleHeight = 37;\n        // 内容区域最小高度\n        const minContentHeight = 80;\n        // 引脚区域上下边距\n        const pinMargins = 24; // 上边距12px + 下边距12px\n        // 计算引脚所需的高度\n        let pinsHeight = minContentHeight;\n        if (maxPins > 0) {\n            // 每个引脚需要的最小间距 - 与前端保持一致\n            const minPinSpacing = 48;\n            // 确保有足够的空间容纳所有引脚\n            const requiredPinsHeight = (maxPins - 1) * minPinSpacing + 40; // 额外40px用于首尾边距\n            pinsHeight = Math.max(minContentHeight, requiredPinsHeight);\n        }\n        const newHeight = titleHeight + pinMargins + pinsHeight;\n        // 检查高度是否发生变化\n        if (this.data.size.height !== newHeight) {\n            console.log(\"节点 \".concat(this.data.name, \" 高度变化: \").concat(this.data.size.height, \" -> \").concat(newHeight, \" (引脚数: \").concat(maxPins, \")\"));\n            this.data.size.height = newHeight;\n            return true; // 返回true表示高度发生了变化\n        }\n        return false; // 返回false表示高度没有变化\n    }\n    constructor(data){\n        this.data = {\n            id: data.id || this.generateId(),\n            type: data.type || 'base',\n            name: data.name || 'Base Node',\n            description: data.description || '',\n            position: data.position || {\n                x: 0,\n                y: 0\n            },\n            size: data.size || {\n                width: 200,\n                height: 100\n            },\n            status: data.status || 'idle',\n            pins: data.pins || [],\n            properties: data.properties || {},\n            selected: data.selected || false,\n            dragging: data.dragging || false\n        };\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/nodes/BaseNode.ts\n"));

/***/ })

});