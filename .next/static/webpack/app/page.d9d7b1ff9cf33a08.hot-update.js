"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/nodes/BaseNodeComponent.tsx":
/*!****************************************************!*\
  !*** ./src/components/nodes/BaseNodeComponent.tsx ***!
  \****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BaseNodeComponent: () => (/* binding */ BaseNodeComponent)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ BaseNodeComponent auto */ \nvar _s = $RefreshSig$();\n\nconst BaseNodeComponent = (param)=>{\n    let { node, onEvent, onUpdateNode, children } = param;\n    _s();\n    const [isDragging, setIsDragging] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [dragOffset, setDragOffset] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        x: 0,\n        y: 0\n    });\n    const nodeRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const animationFrameRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const pendingUpdateRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // 计算引脚位置\n    const calculatePinPositions = ()=>{\n        const inputPins = node.pins.filter((pin)=>pin.type === 'input');\n        const outputPins = node.pins.filter((pin)=>pin.type === 'output');\n        const maxPins = Math.max(inputPins.length, outputPins.length);\n        // 标题栏高度约为 32px (包含padding和border)\n        const titleHeight = 32;\n        // 底部留白\n        const bottomMargin = 8;\n        // 可用于引脚的高度\n        const availableHeight = node.size.height - titleHeight - bottomMargin;\n        // 引脚间距，最小16px\n        const pinSpacing = Math.max(16, availableHeight / Math.max(1, maxPins - 1));\n        return {\n            inputPins,\n            outputPins,\n            titleHeight,\n            pinSpacing,\n            availableHeight\n        };\n    };\n    const { inputPins, outputPins, titleHeight, pinSpacing } = calculatePinPositions();\n    // 使用requestAnimationFrame节流更新\n    const flushPendingUpdate = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"BaseNodeComponent.useCallback[flushPendingUpdate]\": ()=>{\n            if (pendingUpdateRef.current) {\n                onUpdateNode(node.id, {\n                    position: pendingUpdateRef.current\n                });\n                pendingUpdateRef.current = null;\n            }\n            animationFrameRef.current = null;\n        }\n    }[\"BaseNodeComponent.useCallback[flushPendingUpdate]\"], [\n        onUpdateNode,\n        node.id\n    ]);\n    const handleMouseMove = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"BaseNodeComponent.useCallback[handleMouseMove]\": (e)=>{\n            const newPosition = {\n                x: e.clientX - dragOffset.x,\n                y: e.clientY - dragOffset.y\n            };\n            // 立即更新DOM位置以获得流畅的视觉效果\n            if (nodeRef.current) {\n                nodeRef.current.style.left = \"\".concat(newPosition.x, \"px\");\n                nodeRef.current.style.top = \"\".concat(newPosition.y, \"px\");\n            }\n            // 节流状态更新\n            pendingUpdateRef.current = newPosition;\n            if (animationFrameRef.current === null) {\n                animationFrameRef.current = requestAnimationFrame(flushPendingUpdate);\n            }\n        }\n    }[\"BaseNodeComponent.useCallback[handleMouseMove]\"], [\n        dragOffset.x,\n        dragOffset.y,\n        flushPendingUpdate\n    ]);\n    const handleMouseUp = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"BaseNodeComponent.useCallback[handleMouseUp]\": ()=>{\n            setIsDragging(false);\n            // 清理动画帧\n            if (animationFrameRef.current) {\n                cancelAnimationFrame(animationFrameRef.current);\n                animationFrameRef.current = null;\n            }\n            // 确保最后的位置更新被应用\n            if (pendingUpdateRef.current) {\n                onUpdateNode(node.id, {\n                    position: pendingUpdateRef.current\n                });\n                pendingUpdateRef.current = null;\n            }\n            onEvent({\n                type: 'dragEnd',\n                nodeId: node.id\n            });\n            // 移除全局鼠标事件监听\n            document.removeEventListener('mousemove', handleMouseMove);\n            document.removeEventListener('mouseup', handleMouseUp);\n        }\n    }[\"BaseNodeComponent.useCallback[handleMouseUp]\"], [\n        handleMouseMove,\n        onEvent,\n        onUpdateNode,\n        node.id\n    ]);\n    const handleMouseDown = (e)=>{\n        var _nodeRef_current;\n        if (e.button !== 0) return; // 只处理左键\n        // 防止在引脚上开始拖拽\n        const target = e.target;\n        if (target.closest('.pin-connector')) {\n            return;\n        }\n        e.preventDefault();\n        e.stopPropagation();\n        const rect = (_nodeRef_current = nodeRef.current) === null || _nodeRef_current === void 0 ? void 0 : _nodeRef_current.getBoundingClientRect();\n        if (rect) {\n            setDragOffset({\n                x: e.clientX - rect.left,\n                y: e.clientY - rect.top\n            });\n        }\n        setIsDragging(true);\n        onEvent({\n            type: 'dragStart',\n            nodeId: node.id,\n            data: {\n                position: node.position\n            }\n        });\n        // 添加全局鼠标事件监听\n        document.addEventListener('mousemove', handleMouseMove);\n        document.addEventListener('mouseup', handleMouseUp);\n    };\n    const handleClick = (e)=>{\n        e.stopPropagation();\n        onEvent({\n            type: 'click',\n            nodeId: node.id\n        });\n    };\n    const handleDoubleClick = (e)=>{\n        e.stopPropagation();\n        onEvent({\n            type: 'doubleClick',\n            nodeId: node.id\n        });\n    };\n    const getStatusColor = ()=>{\n        switch(node.status){\n            case 'running':\n                return 'border-yellow-400 bg-yellow-50';\n            case 'success':\n                return 'border-green-400 bg-green-50';\n            case 'error':\n                return 'border-red-400 bg-red-50';\n            case 'warning':\n                return 'border-orange-400 bg-orange-50';\n            default:\n                return 'border-gray-300 bg-white';\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: nodeRef,\n        className: \"absolute border-2 rounded-lg shadow-md cursor-move select-none transition-all duration-200 \".concat(getStatusColor(), \" \").concat(node.selected ? 'ring-2 ring-blue-400' : '', \" \").concat(isDragging ? 'shadow-lg scale-105' : ''),\n        style: {\n            left: node.position.x,\n            top: node.position.y,\n            width: node.size.width,\n            height: node.size.height\n        },\n        onMouseDown: handleMouseDown,\n        onClick: handleClick,\n        onDoubleClick: handleDoubleClick,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-2 py-1 bg-gray-100 border-b border-gray-200 rounded-t-md\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-sm font-medium text-gray-800 truncate\",\n                        children: node.name\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/nodes/BaseNodeComponent.tsx\",\n                        lineNumber: 185,\n                        columnNumber: 9\n                    }, undefined),\n                    node.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-gray-500 truncate\",\n                        children: node.description\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/nodes/BaseNodeComponent.tsx\",\n                        lineNumber: 189,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/nodes/BaseNodeComponent.tsx\",\n                lineNumber: 184,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-2 flex-1\",\n                children: children\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/nodes/BaseNodeComponent.tsx\",\n                lineNumber: 196,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute left-0\",\n                style: {\n                    top: titleHeight\n                },\n                children: inputPins.map((pin, index)=>{\n                    const pinY = index * pinSpacing;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute flex items-center\",\n                        style: {\n                            top: pinY,\n                            left: -6,\n                            transform: 'translateY(-50%)' // 垂直居中对齐\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"pin-connector w-3 h-3 rounded-full border-2 cursor-pointer transition-colors \".concat(pin.connected ? 'bg-blue-500 border-blue-600' : 'bg-white border-gray-400 hover:border-blue-400'),\n                                title: pin.name\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/nodes/BaseNodeComponent.tsx\",\n                                lineNumber: 214,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"absolute left-4 top-1/2 transform -translate-y-1/2 text-xs text-gray-600 whitespace-nowrap\",\n                                children: pin.name\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/nodes/BaseNodeComponent.tsx\",\n                                lineNumber: 222,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, pin.id, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/nodes/BaseNodeComponent.tsx\",\n                        lineNumber: 205,\n                        columnNumber: 13\n                    }, undefined);\n                })\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/nodes/BaseNodeComponent.tsx\",\n                lineNumber: 201,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute right-0\",\n                style: {\n                    top: titleHeight\n                },\n                children: outputPins.map((pin, index)=>{\n                    const pinY = index * pinSpacing;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute flex items-center justify-end\",\n                        style: {\n                            top: pinY,\n                            right: -6,\n                            transform: 'translateY(-50%)' // 垂直居中对齐\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"absolute right-4 top-1/2 transform -translate-y-1/2 text-xs text-gray-600 whitespace-nowrap\",\n                                children: pin.name\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/nodes/BaseNodeComponent.tsx\",\n                                lineNumber: 244,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"pin-connector w-3 h-3 rounded-full border-2 cursor-pointer transition-colors \".concat(pin.connected ? 'bg-green-500 border-green-600' : 'bg-white border-gray-400 hover:border-green-400'),\n                                title: pin.name\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/nodes/BaseNodeComponent.tsx\",\n                                lineNumber: 247,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, pin.id, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/nodes/BaseNodeComponent.tsx\",\n                        lineNumber: 235,\n                        columnNumber: 13\n                    }, undefined);\n                })\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/nodes/BaseNodeComponent.tsx\",\n                lineNumber: 231,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/nodes/BaseNodeComponent.tsx\",\n        lineNumber: 166,\n        columnNumber: 5\n    }, undefined);\n};\n_s(BaseNodeComponent, \"7O/WCX1jRM3DSG4/td1hT8DSAmU=\");\n_c = BaseNodeComponent;\nvar _c;\n$RefreshReg$(_c, \"BaseNodeComponent\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/nodes/BaseNodeComponent.tsx\n"));

/***/ })

});