"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/nodes/LedNode.ts":
/*!******************************!*\
  !*** ./src/nodes/LedNode.ts ***!
  \******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LedNode: () => (/* binding */ LedNode)\n/* harmony export */ });\n/* harmony import */ var _BaseNode__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./BaseNode */ \"(app-pages-browser)/./src/nodes/BaseNode.ts\");\n\nclass LedNode extends _BaseNode__WEBPACK_IMPORTED_MODULE_0__.BaseNode {\n    initializePins() {\n        this.updatePinsConfiguration();\n    }\n    updatePinsConfiguration() {\n        // 清除现有引脚\n        this.data.pins = [];\n        const colorMode = this.getProperty('colorMode');\n        const inputRange = this.getProperty('inputRange');\n        // 根据颜色模式添加引脚\n        if (colorMode === '1color' || colorMode === '2color' || colorMode === '3color') {\n            this.addPin({\n                id: 'red_input',\n                name: 'R',\n                type: 'input',\n                dataType: inputRange === 'boolean' ? 'boolean' : 'number',\n                value: 0\n            });\n        }\n        if (colorMode === '2color' || colorMode === '3color') {\n            this.addPin({\n                id: 'green_input',\n                name: 'G',\n                type: 'input',\n                dataType: inputRange === 'boolean' ? 'boolean' : 'number',\n                value: 0\n            });\n        }\n        if (colorMode === '3color') {\n            this.addPin({\n                id: 'blue_input',\n                name: 'B',\n                type: 'input',\n                dataType: inputRange === 'boolean' ? 'boolean' : 'number',\n                value: 0\n            });\n        }\n        // 添加亮度控制引脚\n        this.addPin({\n            id: 'brightness_input',\n            name: 'Brightness',\n            type: 'input',\n            dataType: 'number',\n            value: 100\n        });\n        // 添加输出引脚\n        this.addPin({\n            id: 'color_output',\n            name: 'Color',\n            type: 'output',\n            dataType: 'color',\n            value: '#000000'\n        });\n        // 调用基类方法重新计算节点高度\n        return super.updatePinsConfiguration();\n    }\n    async process() {\n        try {\n            this.setStatus('running');\n            const colorMode = this.getProperty('colorMode');\n            const inputRange = this.getProperty('inputRange');\n            const brightness = this.getPinValue('brightness_input') || this.getProperty('brightness') || 100;\n            let red = 0, green = 0, blue = 0;\n            // 获取输入值\n            if (colorMode === '1color' || colorMode === '2color' || colorMode === '3color') {\n                red = this.normalizeInputValue(this.getPinValue('red_input') || 0, inputRange);\n            }\n            if (colorMode === '2color' || colorMode === '3color') {\n                green = this.normalizeInputValue(this.getPinValue('green_input') || 0, inputRange);\n            }\n            if (colorMode === '3color') {\n                blue = this.normalizeInputValue(this.getPinValue('blue_input') || 0, inputRange);\n            }\n            // 应用亮度\n            const brightnessMultiplier = Math.max(0, Math.min(100, brightness)) / 100;\n            red = Math.round(red * brightnessMultiplier);\n            green = Math.round(green * brightnessMultiplier);\n            blue = Math.round(blue * brightnessMultiplier);\n            // 更新属性值\n            this.setProperty('redValue', red);\n            this.setProperty('greenValue', green);\n            this.setProperty('blueValue', blue);\n            // 生成颜色输出\n            const colorHex = \"#\".concat(red.toString(16).padStart(2, '0')).concat(green.toString(16).padStart(2, '0')).concat(blue.toString(16).padStart(2, '0'));\n            this.setPinValue('color_output', colorHex);\n            this.setStatus('success');\n        } catch (error) {\n            console.error('LED Node processing error:', error);\n            this.setStatus('error');\n        }\n    }\n    normalizeInputValue(value, inputRange) {\n        if (typeof value === 'boolean') {\n            return value ? 255 : 0;\n        }\n        const numValue = Number(value) || 0;\n        switch(inputRange){\n            case 'percentage':\n                return Math.round(Math.max(0, Math.min(100, numValue)) * 2.55);\n            case '0-255':\n                return Math.round(Math.max(0, Math.min(255, numValue)));\n            case '0-1':\n                return Math.round(Math.max(0, Math.min(1, numValue)) * 255);\n            case 'boolean':\n                return numValue > 0 ? 255 : 0;\n            default:\n                return Math.round(Math.max(0, Math.min(255, numValue)));\n        }\n    }\n    onPropertyChanged(key, value) {\n        if (key === 'colorMode' || key === 'inputRange') {\n            const sizeChanged = this.updatePinsConfiguration();\n            // 如果节点尺寸发生变化，需要通知外部更新\n            if (sizeChanged) {\n                // 这里需要一个回调来通知React组件更新\n                console.log('节点尺寸已更新，需要重新渲染');\n            }\n        }\n        super.onPropertyChanged(key, value);\n    }\n    constructor(data){\n        super({\n            type: 'led',\n            name: 'RGB LED',\n            description: '3色变色LED灯节点',\n            size: {\n                width: 140,\n                height: 100\n            },\n            properties: {\n                colorMode: '3color',\n                inputRange: 'percentage',\n                redValue: 0,\n                greenValue: 0,\n                blueValue: 0,\n                brightness: 100\n            },\n            ...data\n        });\n        this.initializePins();\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/nodes/LedNode.ts\n"));

/***/ })

});