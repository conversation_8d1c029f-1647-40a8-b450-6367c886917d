"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/nodes/BaseNodeComponent.tsx":
/*!****************************************************!*\
  !*** ./src/components/nodes/BaseNodeComponent.tsx ***!
  \****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BaseNodeComponent: () => (/* binding */ BaseNodeComponent)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ BaseNodeComponent auto */ \nvar _s = $RefreshSig$();\n\nconst BaseNodeComponent = (param)=>{\n    let { node, onEvent, onUpdateNode, children } = param;\n    _s();\n    const [isDragging, setIsDragging] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [dragOffset, setDragOffset] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        x: 0,\n        y: 0\n    });\n    const nodeRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const animationFrameRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const pendingUpdateRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // 计算引脚位置\n    const calculatePinPositions = ()=>{\n        const inputPins = node.pins.filter((pin)=>pin.type === 'input');\n        const outputPins = node.pins.filter((pin)=>pin.type === 'output');\n        const maxPins = Math.max(inputPins.length, outputPins.length);\n        // 标题栏高度：padding(8px) + 文字高度(20px) + border(1px) = 29px\n        const titleHeight = 29;\n        // 底部留白\n        const bottomMargin = 8;\n        // 引脚区域顶部留白\n        const topMargin = 8;\n        // 可用于引脚的高度\n        const availableHeight = node.size.height - titleHeight - bottomMargin - topMargin;\n        // 引脚间距，如果只有一个引脚则居中，多个引脚则均匀分布\n        let pinSpacing = 0;\n        if (maxPins > 1) {\n            pinSpacing = availableHeight / (maxPins - 1);\n        }\n        // 调试信息\n        if ( true && maxPins > 0) {\n            console.log(\"节点 \".concat(node.name, \" 引脚计算:\"), {\n                nodeHeight: node.size.height,\n                titleHeight,\n                topMargin,\n                bottomMargin,\n                availableHeight,\n                maxPins,\n                pinSpacing,\n                inputPins: inputPins.length,\n                outputPins: outputPins.length\n            });\n        }\n        return {\n            inputPins,\n            outputPins,\n            titleHeight,\n            topMargin,\n            pinSpacing,\n            availableHeight,\n            maxPins\n        };\n    };\n    const { inputPins, outputPins, titleHeight, topMargin, pinSpacing, maxPins } = calculatePinPositions();\n    // 使用requestAnimationFrame节流更新\n    const flushPendingUpdate = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"BaseNodeComponent.useCallback[flushPendingUpdate]\": ()=>{\n            if (pendingUpdateRef.current) {\n                onUpdateNode(node.id, {\n                    position: pendingUpdateRef.current\n                });\n                pendingUpdateRef.current = null;\n            }\n            animationFrameRef.current = null;\n        }\n    }[\"BaseNodeComponent.useCallback[flushPendingUpdate]\"], [\n        onUpdateNode,\n        node.id\n    ]);\n    const handleMouseMove = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"BaseNodeComponent.useCallback[handleMouseMove]\": (e)=>{\n            const newPosition = {\n                x: e.clientX - dragOffset.x,\n                y: e.clientY - dragOffset.y\n            };\n            // 立即更新DOM位置以获得流畅的视觉效果\n            if (nodeRef.current) {\n                nodeRef.current.style.left = \"\".concat(newPosition.x, \"px\");\n                nodeRef.current.style.top = \"\".concat(newPosition.y, \"px\");\n            }\n            // 节流状态更新\n            pendingUpdateRef.current = newPosition;\n            if (animationFrameRef.current === null) {\n                animationFrameRef.current = requestAnimationFrame(flushPendingUpdate);\n            }\n        }\n    }[\"BaseNodeComponent.useCallback[handleMouseMove]\"], [\n        dragOffset.x,\n        dragOffset.y,\n        flushPendingUpdate\n    ]);\n    const handleMouseUp = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"BaseNodeComponent.useCallback[handleMouseUp]\": ()=>{\n            setIsDragging(false);\n            // 清理动画帧\n            if (animationFrameRef.current) {\n                cancelAnimationFrame(animationFrameRef.current);\n                animationFrameRef.current = null;\n            }\n            // 确保最后的位置更新被应用\n            if (pendingUpdateRef.current) {\n                onUpdateNode(node.id, {\n                    position: pendingUpdateRef.current\n                });\n                pendingUpdateRef.current = null;\n            }\n            onEvent({\n                type: 'dragEnd',\n                nodeId: node.id\n            });\n            // 移除全局鼠标事件监听\n            document.removeEventListener('mousemove', handleMouseMove);\n            document.removeEventListener('mouseup', handleMouseUp);\n        }\n    }[\"BaseNodeComponent.useCallback[handleMouseUp]\"], [\n        handleMouseMove,\n        onEvent,\n        onUpdateNode,\n        node.id\n    ]);\n    const handleMouseDown = (e)=>{\n        var _nodeRef_current;\n        if (e.button !== 0) return; // 只处理左键\n        // 防止在引脚上开始拖拽\n        const target = e.target;\n        if (target.closest('.pin-connector')) {\n            return;\n        }\n        e.preventDefault();\n        e.stopPropagation();\n        const rect = (_nodeRef_current = nodeRef.current) === null || _nodeRef_current === void 0 ? void 0 : _nodeRef_current.getBoundingClientRect();\n        if (rect) {\n            setDragOffset({\n                x: e.clientX - rect.left,\n                y: e.clientY - rect.top\n            });\n        }\n        setIsDragging(true);\n        onEvent({\n            type: 'dragStart',\n            nodeId: node.id,\n            data: {\n                position: node.position\n            }\n        });\n        // 添加全局鼠标事件监听\n        document.addEventListener('mousemove', handleMouseMove);\n        document.addEventListener('mouseup', handleMouseUp);\n    };\n    const handleClick = (e)=>{\n        e.stopPropagation();\n        onEvent({\n            type: 'click',\n            nodeId: node.id\n        });\n    };\n    const handleDoubleClick = (e)=>{\n        e.stopPropagation();\n        onEvent({\n            type: 'doubleClick',\n            nodeId: node.id\n        });\n    };\n    const getStatusColor = ()=>{\n        switch(node.status){\n            case 'running':\n                return 'border-yellow-400 bg-yellow-50';\n            case 'success':\n                return 'border-green-400 bg-green-50';\n            case 'error':\n                return 'border-red-400 bg-red-50';\n            case 'warning':\n                return 'border-orange-400 bg-orange-50';\n            default:\n                return 'border-gray-300 bg-white';\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: nodeRef,\n        className: \"absolute border-2 rounded-lg shadow-md cursor-move select-none \".concat(getStatusColor(), \" \").concat(node.selected ? 'ring-2 ring-blue-400' : '', \" \").concat(isDragging ? 'shadow-lg' : ''),\n        style: {\n            left: node.position.x,\n            top: node.position.y,\n            width: node.size.width,\n            height: node.size.height\n        },\n        onMouseDown: handleMouseDown,\n        onClick: handleClick,\n        onDoubleClick: handleDoubleClick,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-2 py-2 bg-gray-100 border-b border-gray-200 rounded-t-md\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-sm font-medium text-gray-800 truncate\",\n                    children: node.name\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/nodes/BaseNodeComponent.tsx\",\n                    lineNumber: 207,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/nodes/BaseNodeComponent.tsx\",\n                lineNumber: 206,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-2 flex-1\",\n                children: children\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/nodes/BaseNodeComponent.tsx\",\n                lineNumber: 213,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute left-0\",\n                style: {\n                    top: titleHeight + topMargin\n                },\n                children: inputPins.map((pin, index)=>{\n                    // 如果只有一个引脚，居中显示；多个引脚则均匀分布\n                    const pinY = maxPins === 1 ? 0 : index * pinSpacing;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute flex items-center\",\n                        style: {\n                            top: pinY,\n                            left: -6,\n                            transform: 'translateY(-50%)' // 垂直居中对齐\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"pin-connector w-3 h-3 rounded-full border-2 cursor-pointer transition-colors \".concat(pin.connected ? 'bg-blue-500 border-blue-600' : 'bg-white border-gray-400 hover:border-blue-400'),\n                                title: pin.name\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/nodes/BaseNodeComponent.tsx\",\n                                lineNumber: 232,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"absolute left-4 top-1/2 transform -translate-y-1/2 text-xs text-gray-600 whitespace-nowrap\",\n                                children: pin.name\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/nodes/BaseNodeComponent.tsx\",\n                                lineNumber: 240,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, pin.id, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/nodes/BaseNodeComponent.tsx\",\n                        lineNumber: 223,\n                        columnNumber: 13\n                    }, undefined);\n                })\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/nodes/BaseNodeComponent.tsx\",\n                lineNumber: 218,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute right-0\",\n                style: {\n                    top: titleHeight + topMargin\n                },\n                children: outputPins.map((pin, index)=>{\n                    // 如果只有一个引脚，居中显示；多个引脚则均匀分布\n                    const pinY = maxPins === 1 ? 0 : index * pinSpacing;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute flex items-center justify-end\",\n                        style: {\n                            top: pinY,\n                            right: -6,\n                            transform: 'translateY(-50%)' // 垂直居中对齐\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"absolute right-4 top-1/2 transform -translate-y-1/2 text-xs text-gray-600 whitespace-nowrap\",\n                                children: pin.name\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/nodes/BaseNodeComponent.tsx\",\n                                lineNumber: 263,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"pin-connector w-3 h-3 rounded-full border-2 cursor-pointer transition-colors \".concat(pin.connected ? 'bg-green-500 border-green-600' : 'bg-white border-gray-400 hover:border-green-400'),\n                                title: pin.name\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/nodes/BaseNodeComponent.tsx\",\n                                lineNumber: 266,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, pin.id, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/nodes/BaseNodeComponent.tsx\",\n                        lineNumber: 254,\n                        columnNumber: 13\n                    }, undefined);\n                })\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/nodes/BaseNodeComponent.tsx\",\n                lineNumber: 249,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/nodes/BaseNodeComponent.tsx\",\n        lineNumber: 188,\n        columnNumber: 5\n    }, undefined);\n};\n_s(BaseNodeComponent, \"7O/WCX1jRM3DSG4/td1hT8DSAmU=\");\n_c = BaseNodeComponent;\nvar _c;\n$RefreshReg$(_c, \"BaseNodeComponent\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/nodes/BaseNodeComponent.tsx\n"));

/***/ })

});