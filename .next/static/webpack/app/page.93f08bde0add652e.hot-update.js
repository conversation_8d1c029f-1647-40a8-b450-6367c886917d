"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/nodes/BaseNodeComponent.tsx":
/*!****************************************************!*\
  !*** ./src/components/nodes/BaseNodeComponent.tsx ***!
  \****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BaseNodeComponent: () => (/* binding */ BaseNodeComponent)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ BaseNodeComponent auto */ \nvar _s = $RefreshSig$();\n\nconst BaseNodeComponent = (param)=>{\n    let { node, onEvent, onUpdateNode, children } = param;\n    _s();\n    const [isDragging, setIsDragging] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [dragOffset, setDragOffset] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        x: 0,\n        y: 0\n    });\n    const nodeRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const animationFrameRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const pendingUpdateRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // 计算引脚位置\n    const calculatePinPositions = ()=>{\n        const inputPins = node.pins.filter((pin)=>pin.type === 'input');\n        const outputPins = node.pins.filter((pin)=>pin.type === 'output');\n        const maxPins = Math.max(inputPins.length, outputPins.length);\n        // 标题栏高度：padding(8px) + 文字高度(20px) + border(1px) = 29px\n        const titleHeight = 29;\n        // 底部留白\n        const bottomMargin = 8;\n        // 引脚区域顶部留白\n        const topMargin = 8;\n        // 可用于引脚的高度\n        const availableHeight = node.size.height - titleHeight - bottomMargin - topMargin;\n        // 引脚间距，如果只有一个引脚则居中，多个引脚则均匀分布\n        let pinSpacing = 0;\n        if (maxPins > 1) {\n            pinSpacing = availableHeight / (maxPins - 1);\n        }\n        return {\n            inputPins,\n            outputPins,\n            titleHeight,\n            topMargin,\n            pinSpacing,\n            availableHeight,\n            maxPins\n        };\n    };\n    const { inputPins, outputPins, titleHeight, pinSpacing } = calculatePinPositions();\n    // 使用requestAnimationFrame节流更新\n    const flushPendingUpdate = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"BaseNodeComponent.useCallback[flushPendingUpdate]\": ()=>{\n            if (pendingUpdateRef.current) {\n                onUpdateNode(node.id, {\n                    position: pendingUpdateRef.current\n                });\n                pendingUpdateRef.current = null;\n            }\n            animationFrameRef.current = null;\n        }\n    }[\"BaseNodeComponent.useCallback[flushPendingUpdate]\"], [\n        onUpdateNode,\n        node.id\n    ]);\n    const handleMouseMove = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"BaseNodeComponent.useCallback[handleMouseMove]\": (e)=>{\n            const newPosition = {\n                x: e.clientX - dragOffset.x,\n                y: e.clientY - dragOffset.y\n            };\n            // 立即更新DOM位置以获得流畅的视觉效果\n            if (nodeRef.current) {\n                nodeRef.current.style.left = \"\".concat(newPosition.x, \"px\");\n                nodeRef.current.style.top = \"\".concat(newPosition.y, \"px\");\n            }\n            // 节流状态更新\n            pendingUpdateRef.current = newPosition;\n            if (animationFrameRef.current === null) {\n                animationFrameRef.current = requestAnimationFrame(flushPendingUpdate);\n            }\n        }\n    }[\"BaseNodeComponent.useCallback[handleMouseMove]\"], [\n        dragOffset.x,\n        dragOffset.y,\n        flushPendingUpdate\n    ]);\n    const handleMouseUp = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"BaseNodeComponent.useCallback[handleMouseUp]\": ()=>{\n            setIsDragging(false);\n            // 清理动画帧\n            if (animationFrameRef.current) {\n                cancelAnimationFrame(animationFrameRef.current);\n                animationFrameRef.current = null;\n            }\n            // 确保最后的位置更新被应用\n            if (pendingUpdateRef.current) {\n                onUpdateNode(node.id, {\n                    position: pendingUpdateRef.current\n                });\n                pendingUpdateRef.current = null;\n            }\n            onEvent({\n                type: 'dragEnd',\n                nodeId: node.id\n            });\n            // 移除全局鼠标事件监听\n            document.removeEventListener('mousemove', handleMouseMove);\n            document.removeEventListener('mouseup', handleMouseUp);\n        }\n    }[\"BaseNodeComponent.useCallback[handleMouseUp]\"], [\n        handleMouseMove,\n        onEvent,\n        onUpdateNode,\n        node.id\n    ]);\n    const handleMouseDown = (e)=>{\n        var _nodeRef_current;\n        if (e.button !== 0) return; // 只处理左键\n        // 防止在引脚上开始拖拽\n        const target = e.target;\n        if (target.closest('.pin-connector')) {\n            return;\n        }\n        e.preventDefault();\n        e.stopPropagation();\n        const rect = (_nodeRef_current = nodeRef.current) === null || _nodeRef_current === void 0 ? void 0 : _nodeRef_current.getBoundingClientRect();\n        if (rect) {\n            setDragOffset({\n                x: e.clientX - rect.left,\n                y: e.clientY - rect.top\n            });\n        }\n        setIsDragging(true);\n        onEvent({\n            type: 'dragStart',\n            nodeId: node.id,\n            data: {\n                position: node.position\n            }\n        });\n        // 添加全局鼠标事件监听\n        document.addEventListener('mousemove', handleMouseMove);\n        document.addEventListener('mouseup', handleMouseUp);\n    };\n    const handleClick = (e)=>{\n        e.stopPropagation();\n        onEvent({\n            type: 'click',\n            nodeId: node.id\n        });\n    };\n    const handleDoubleClick = (e)=>{\n        e.stopPropagation();\n        onEvent({\n            type: 'doubleClick',\n            nodeId: node.id\n        });\n    };\n    const getStatusColor = ()=>{\n        switch(node.status){\n            case 'running':\n                return 'border-yellow-400 bg-yellow-50';\n            case 'success':\n                return 'border-green-400 bg-green-50';\n            case 'error':\n                return 'border-red-400 bg-red-50';\n            case 'warning':\n                return 'border-orange-400 bg-orange-50';\n            default:\n                return 'border-gray-300 bg-white';\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: nodeRef,\n        className: \"absolute border-2 rounded-lg shadow-md cursor-move select-none transition-all duration-200 \".concat(getStatusColor(), \" \").concat(node.selected ? 'ring-2 ring-blue-400' : '', \" \").concat(isDragging ? 'shadow-lg scale-105' : ''),\n        style: {\n            left: node.position.x,\n            top: node.position.y,\n            width: node.size.width,\n            height: node.size.height\n        },\n        onMouseDown: handleMouseDown,\n        onClick: handleClick,\n        onDoubleClick: handleDoubleClick,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-2 py-2 bg-gray-100 border-b border-gray-200 rounded-t-md\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-sm font-medium text-gray-800 truncate\",\n                    children: node.name\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/nodes/BaseNodeComponent.tsx\",\n                    lineNumber: 192,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/nodes/BaseNodeComponent.tsx\",\n                lineNumber: 191,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-2 flex-1\",\n                children: children\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/nodes/BaseNodeComponent.tsx\",\n                lineNumber: 198,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute left-0\",\n                style: {\n                    top: titleHeight\n                },\n                children: inputPins.map((pin, index)=>{\n                    const pinY = index * pinSpacing;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute flex items-center\",\n                        style: {\n                            top: pinY,\n                            left: -6,\n                            transform: 'translateY(-50%)' // 垂直居中对齐\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"pin-connector w-3 h-3 rounded-full border-2 cursor-pointer transition-colors \".concat(pin.connected ? 'bg-blue-500 border-blue-600' : 'bg-white border-gray-400 hover:border-blue-400'),\n                                title: pin.name\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/nodes/BaseNodeComponent.tsx\",\n                                lineNumber: 216,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"absolute left-4 top-1/2 transform -translate-y-1/2 text-xs text-gray-600 whitespace-nowrap\",\n                                children: pin.name\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/nodes/BaseNodeComponent.tsx\",\n                                lineNumber: 224,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, pin.id, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/nodes/BaseNodeComponent.tsx\",\n                        lineNumber: 207,\n                        columnNumber: 13\n                    }, undefined);\n                })\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/nodes/BaseNodeComponent.tsx\",\n                lineNumber: 203,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute right-0\",\n                style: {\n                    top: titleHeight\n                },\n                children: outputPins.map((pin, index)=>{\n                    const pinY = index * pinSpacing;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute flex items-center justify-end\",\n                        style: {\n                            top: pinY,\n                            right: -6,\n                            transform: 'translateY(-50%)' // 垂直居中对齐\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"absolute right-4 top-1/2 transform -translate-y-1/2 text-xs text-gray-600 whitespace-nowrap\",\n                                children: pin.name\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/nodes/BaseNodeComponent.tsx\",\n                                lineNumber: 246,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"pin-connector w-3 h-3 rounded-full border-2 cursor-pointer transition-colors \".concat(pin.connected ? 'bg-green-500 border-green-600' : 'bg-white border-gray-400 hover:border-green-400'),\n                                title: pin.name\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/nodes/BaseNodeComponent.tsx\",\n                                lineNumber: 249,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, pin.id, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/nodes/BaseNodeComponent.tsx\",\n                        lineNumber: 237,\n                        columnNumber: 13\n                    }, undefined);\n                })\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/nodes/BaseNodeComponent.tsx\",\n                lineNumber: 233,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/nodes/BaseNodeComponent.tsx\",\n        lineNumber: 173,\n        columnNumber: 5\n    }, undefined);\n};\n_s(BaseNodeComponent, \"7O/WCX1jRM3DSG4/td1hT8DSAmU=\");\n_c = BaseNodeComponent;\nvar _c;\n$RefreshReg$(_c, \"BaseNodeComponent\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/nodes/BaseNodeComponent.tsx\n"));

/***/ })

});