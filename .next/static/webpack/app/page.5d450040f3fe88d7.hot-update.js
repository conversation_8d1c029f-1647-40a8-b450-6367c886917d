"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/nodes/BaseNodeComponent.tsx":
/*!****************************************************!*\
  !*** ./src/components/nodes/BaseNodeComponent.tsx ***!
  \****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BaseNodeComponent: () => (/* binding */ BaseNodeComponent)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ BaseNodeComponent auto */ \nvar _s = $RefreshSig$();\n\nconst BaseNodeComponent = (param)=>{\n    let { node, onEvent, onUpdateNode, children } = param;\n    _s();\n    const [isDragging, setIsDragging] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [dragOffset, setDragOffset] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        x: 0,\n        y: 0\n    });\n    const nodeRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const animationFrameRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const pendingUpdateRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // 计算引脚位置\n    const calculatePinPositions = ()=>{\n        const inputPins = node.pins.filter((pin)=>pin.type === 'input');\n        const outputPins = node.pins.filter((pin)=>pin.type === 'output');\n        const maxPins = Math.max(inputPins.length, outputPins.length);\n        // 标题栏高度约为 32px (包含padding和border)\n        const titleHeight = 32;\n        // 底部留白\n        const bottomMargin = 8;\n        // 可用于引脚的高度\n        const availableHeight = node.size.height - titleHeight - bottomMargin;\n        // 引脚间距，最小16px\n        const pinSpacing = Math.max(16, availableHeight / Math.max(1, maxPins - 1));\n        return {\n            inputPins,\n            outputPins,\n            titleHeight,\n            pinSpacing,\n            availableHeight\n        };\n    };\n    const { inputPins, outputPins, titleHeight, pinSpacing } = calculatePinPositions();\n    // 使用requestAnimationFrame节流更新\n    const flushPendingUpdate = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"BaseNodeComponent.useCallback[flushPendingUpdate]\": ()=>{\n            if (pendingUpdateRef.current) {\n                onUpdateNode(node.id, {\n                    position: pendingUpdateRef.current\n                });\n                pendingUpdateRef.current = null;\n            }\n            animationFrameRef.current = null;\n        }\n    }[\"BaseNodeComponent.useCallback[flushPendingUpdate]\"], [\n        onUpdateNode,\n        node.id\n    ]);\n    const handleMouseMove = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"BaseNodeComponent.useCallback[handleMouseMove]\": (e)=>{\n            const newPosition = {\n                x: e.clientX - dragOffset.x,\n                y: e.clientY - dragOffset.y\n            };\n            // 立即更新DOM位置以获得流畅的视觉效果\n            if (nodeRef.current) {\n                nodeRef.current.style.left = \"\".concat(newPosition.x, \"px\");\n                nodeRef.current.style.top = \"\".concat(newPosition.y, \"px\");\n            }\n            // 节流状态更新\n            pendingUpdateRef.current = newPosition;\n            if (animationFrameRef.current === null) {\n                animationFrameRef.current = requestAnimationFrame(flushPendingUpdate);\n            }\n        }\n    }[\"BaseNodeComponent.useCallback[handleMouseMove]\"], [\n        dragOffset.x,\n        dragOffset.y,\n        flushPendingUpdate\n    ]);\n    const handleMouseUp = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"BaseNodeComponent.useCallback[handleMouseUp]\": ()=>{\n            setIsDragging(false);\n            // 清理动画帧\n            if (animationFrameRef.current) {\n                cancelAnimationFrame(animationFrameRef.current);\n                animationFrameRef.current = null;\n            }\n            // 确保最后的位置更新被应用\n            if (pendingUpdateRef.current) {\n                onUpdateNode(node.id, {\n                    position: pendingUpdateRef.current\n                });\n                pendingUpdateRef.current = null;\n            }\n            onEvent({\n                type: 'dragEnd',\n                nodeId: node.id\n            });\n            // 移除全局鼠标事件监听\n            document.removeEventListener('mousemove', handleMouseMove);\n            document.removeEventListener('mouseup', handleMouseUp);\n        }\n    }[\"BaseNodeComponent.useCallback[handleMouseUp]\"], [\n        handleMouseMove,\n        onEvent,\n        onUpdateNode,\n        node.id\n    ]);\n    const handleMouseDown = (e)=>{\n        var _nodeRef_current;\n        if (e.button !== 0) return; // 只处理左键\n        // 防止在引脚上开始拖拽\n        const target = e.target;\n        if (target.closest('.pin-connector')) {\n            return;\n        }\n        e.preventDefault();\n        e.stopPropagation();\n        const rect = (_nodeRef_current = nodeRef.current) === null || _nodeRef_current === void 0 ? void 0 : _nodeRef_current.getBoundingClientRect();\n        if (rect) {\n            setDragOffset({\n                x: e.clientX - rect.left,\n                y: e.clientY - rect.top\n            });\n        }\n        setIsDragging(true);\n        onEvent({\n            type: 'dragStart',\n            nodeId: node.id,\n            data: {\n                position: node.position\n            }\n        });\n        // 添加全局鼠标事件监听\n        document.addEventListener('mousemove', handleMouseMove);\n        document.addEventListener('mouseup', handleMouseUp);\n    };\n    const handleClick = (e)=>{\n        e.stopPropagation();\n        onEvent({\n            type: 'click',\n            nodeId: node.id\n        });\n    };\n    const handleDoubleClick = (e)=>{\n        e.stopPropagation();\n        onEvent({\n            type: 'doubleClick',\n            nodeId: node.id\n        });\n    };\n    const getStatusColor = ()=>{\n        switch(node.status){\n            case 'running':\n                return 'border-yellow-400 bg-yellow-50';\n            case 'success':\n                return 'border-green-400 bg-green-50';\n            case 'error':\n                return 'border-red-400 bg-red-50';\n            case 'warning':\n                return 'border-orange-400 bg-orange-50';\n            default:\n                return 'border-gray-300 bg-white';\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: nodeRef,\n        className: \"absolute border-2 rounded-lg shadow-md cursor-move select-none transition-all duration-200 \".concat(getStatusColor(), \" \").concat(node.selected ? 'ring-2 ring-blue-400' : '', \" \").concat(isDragging ? 'shadow-lg scale-105' : ''),\n        style: {\n            left: node.position.x,\n            top: node.position.y,\n            width: node.size.width,\n            height: node.size.height\n        },\n        onMouseDown: handleMouseDown,\n        onClick: handleClick,\n        onDoubleClick: handleDoubleClick,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-2 py-2 bg-gray-100 border-b border-gray-200 rounded-t-md\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-sm font-medium text-gray-800 truncate\",\n                    children: node.name\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/nodes/BaseNodeComponent.tsx\",\n                    lineNumber: 185,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/nodes/BaseNodeComponent.tsx\",\n                lineNumber: 184,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-2 flex-1\",\n                children: children\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/nodes/BaseNodeComponent.tsx\",\n                lineNumber: 191,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute left-0\",\n                style: {\n                    top: titleHeight\n                },\n                children: inputPins.map((pin, index)=>{\n                    const pinY = index * pinSpacing;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute flex items-center\",\n                        style: {\n                            top: pinY,\n                            left: -6,\n                            transform: 'translateY(-50%)' // 垂直居中对齐\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"pin-connector w-3 h-3 rounded-full border-2 cursor-pointer transition-colors \".concat(pin.connected ? 'bg-blue-500 border-blue-600' : 'bg-white border-gray-400 hover:border-blue-400'),\n                                title: pin.name\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/nodes/BaseNodeComponent.tsx\",\n                                lineNumber: 209,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"absolute left-4 top-1/2 transform -translate-y-1/2 text-xs text-gray-600 whitespace-nowrap\",\n                                children: pin.name\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/nodes/BaseNodeComponent.tsx\",\n                                lineNumber: 217,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, pin.id, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/nodes/BaseNodeComponent.tsx\",\n                        lineNumber: 200,\n                        columnNumber: 13\n                    }, undefined);\n                })\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/nodes/BaseNodeComponent.tsx\",\n                lineNumber: 196,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute right-0\",\n                style: {\n                    top: titleHeight\n                },\n                children: outputPins.map((pin, index)=>{\n                    const pinY = index * pinSpacing;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute flex items-center justify-end\",\n                        style: {\n                            top: pinY,\n                            right: -6,\n                            transform: 'translateY(-50%)' // 垂直居中对齐\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"absolute right-4 top-1/2 transform -translate-y-1/2 text-xs text-gray-600 whitespace-nowrap\",\n                                children: pin.name\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/nodes/BaseNodeComponent.tsx\",\n                                lineNumber: 239,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"pin-connector w-3 h-3 rounded-full border-2 cursor-pointer transition-colors \".concat(pin.connected ? 'bg-green-500 border-green-600' : 'bg-white border-gray-400 hover:border-green-400'),\n                                title: pin.name\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/nodes/BaseNodeComponent.tsx\",\n                                lineNumber: 242,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, pin.id, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/nodes/BaseNodeComponent.tsx\",\n                        lineNumber: 230,\n                        columnNumber: 13\n                    }, undefined);\n                })\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/nodes/BaseNodeComponent.tsx\",\n                lineNumber: 226,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/nodes/BaseNodeComponent.tsx\",\n        lineNumber: 166,\n        columnNumber: 5\n    }, undefined);\n};\n_s(BaseNodeComponent, \"7O/WCX1jRM3DSG4/td1hT8DSAmU=\");\n_c = BaseNodeComponent;\nvar _c;\n$RefreshReg$(_c, \"BaseNodeComponent\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/nodes/BaseNodeComponent.tsx\n"));

/***/ })

});