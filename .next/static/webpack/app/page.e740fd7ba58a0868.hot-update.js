"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/nodes/BaseNodeComponent.tsx":
/*!****************************************************!*\
  !*** ./src/components/nodes/BaseNodeComponent.tsx ***!
  \****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BaseNodeComponent: () => (/* binding */ BaseNodeComponent)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ BaseNodeComponent auto */ \nvar _s = $RefreshSig$();\n\nconst BaseNodeComponent = (param)=>{\n    let { node, onEvent, onUpdateNode, children } = param;\n    _s();\n    const [isDragging, setIsDragging] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [dragOffset, setDragOffset] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        x: 0,\n        y: 0\n    });\n    const nodeRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const animationFrameRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const pendingUpdateRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // 计算引脚位置\n    const calculatePinPositions = ()=>{\n        const inputPins = node.pins.filter((pin)=>pin.type === 'input');\n        const outputPins = node.pins.filter((pin)=>pin.type === 'output');\n        const maxPins = Math.max(inputPins.length, outputPins.length);\n        // 标题栏高度：padding(16px) + 文字高度(20px) + border(1px) = 37px\n        const titleHeight = 37;\n        // 底部留白\n        const bottomMargin = 12;\n        // 引脚区域顶部留白\n        const topMargin = 12;\n        // 可用于引脚的高度\n        const availableHeight = node.size.height - titleHeight - bottomMargin - topMargin;\n        // 引脚间距，如果只有一个引脚则居中，多个引脚则均匀分布\n        let pinSpacing = 0;\n        if (maxPins > 1) {\n            pinSpacing = availableHeight / (maxPins - 1);\n        }\n        // 调试信息\n        if ( true && maxPins > 0) {\n            console.log(\"节点 \".concat(node.name, \" 引脚计算:\"), {\n                nodeHeight: node.size.height,\n                titleHeight,\n                topMargin,\n                bottomMargin,\n                availableHeight,\n                maxPins,\n                pinSpacing,\n                inputPins: inputPins.length,\n                outputPins: outputPins.length\n            });\n        }\n        return {\n            inputPins,\n            outputPins,\n            titleHeight,\n            topMargin,\n            pinSpacing,\n            availableHeight,\n            maxPins\n        };\n    };\n    const { inputPins, outputPins, titleHeight, topMargin, pinSpacing, maxPins } = calculatePinPositions();\n    // 使用requestAnimationFrame节流更新\n    const flushPendingUpdate = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"BaseNodeComponent.useCallback[flushPendingUpdate]\": ()=>{\n            if (pendingUpdateRef.current) {\n                onUpdateNode(node.id, {\n                    position: pendingUpdateRef.current\n                });\n                pendingUpdateRef.current = null;\n            }\n            animationFrameRef.current = null;\n        }\n    }[\"BaseNodeComponent.useCallback[flushPendingUpdate]\"], [\n        onUpdateNode,\n        node.id\n    ]);\n    const handleMouseMove = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"BaseNodeComponent.useCallback[handleMouseMove]\": (e)=>{\n            // 确保拖拽偏移量已经设置\n            if (dragOffset.x === 0 && dragOffset.y === 0) return;\n            const newPosition = {\n                x: e.clientX - dragOffset.x,\n                y: e.clientY - dragOffset.y\n            };\n            // 立即更新DOM位置以获得流畅的视觉效果\n            if (nodeRef.current) {\n                nodeRef.current.style.left = \"\".concat(newPosition.x, \"px\");\n                nodeRef.current.style.top = \"\".concat(newPosition.y, \"px\");\n            }\n            // 节流状态更新\n            pendingUpdateRef.current = newPosition;\n            if (animationFrameRef.current === null) {\n                animationFrameRef.current = requestAnimationFrame(flushPendingUpdate);\n            }\n        }\n    }[\"BaseNodeComponent.useCallback[handleMouseMove]\"], [\n        dragOffset.x,\n        dragOffset.y,\n        flushPendingUpdate\n    ]);\n    const handleMouseUp = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"BaseNodeComponent.useCallback[handleMouseUp]\": ()=>{\n            setIsDragging(false);\n            // 清理动画帧\n            if (animationFrameRef.current) {\n                cancelAnimationFrame(animationFrameRef.current);\n                animationFrameRef.current = null;\n            }\n            // 确保最后的位置更新被应用\n            if (pendingUpdateRef.current) {\n                onUpdateNode(node.id, {\n                    position: pendingUpdateRef.current\n                });\n                pendingUpdateRef.current = null;\n            }\n            onEvent({\n                type: 'dragEnd',\n                nodeId: node.id\n            });\n            // 移除全局鼠标事件监听\n            document.removeEventListener('mousemove', handleMouseMove);\n            document.removeEventListener('mouseup', handleMouseUp);\n        }\n    }[\"BaseNodeComponent.useCallback[handleMouseUp]\"], [\n        handleMouseMove,\n        onEvent,\n        onUpdateNode,\n        node.id\n    ]);\n    const handleMouseDown = (e)=>{\n        if (e.button !== 0) return; // 只处理左键\n        // 防止在引脚上开始拖拽\n        const target = e.target;\n        if (target.closest('.pin-connector')) {\n            return;\n        }\n        e.preventDefault();\n        e.stopPropagation();\n        // 使用节点的当前位置而不是getBoundingClientRect来计算偏移量\n        // 这样可以避免因为CSS变换或其他因素导致的位置偏差\n        setDragOffset({\n            x: e.clientX - node.position.x,\n            y: e.clientY - node.position.y\n        });\n        setIsDragging(true);\n        onEvent({\n            type: 'dragStart',\n            nodeId: node.id,\n            data: {\n                position: node.position\n            }\n        });\n        // 添加全局鼠标事件监听\n        document.addEventListener('mousemove', handleMouseMove);\n        document.addEventListener('mouseup', handleMouseUp);\n    };\n    const handleClick = (e)=>{\n        e.stopPropagation();\n        onEvent({\n            type: 'click',\n            nodeId: node.id\n        });\n    };\n    const handleDoubleClick = (e)=>{\n        e.stopPropagation();\n        onEvent({\n            type: 'doubleClick',\n            nodeId: node.id\n        });\n    };\n    const getStatusColor = ()=>{\n        switch(node.status){\n            case 'running':\n                return 'border-yellow-400 bg-yellow-50';\n            case 'success':\n                return 'border-green-400 bg-green-50';\n            case 'error':\n                return 'border-red-400 bg-red-50';\n            case 'warning':\n                return 'border-orange-400 bg-orange-50';\n            default:\n                return 'border-gray-300 bg-white';\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: nodeRef,\n        className: \"absolute border-2 rounded-lg shadow-md cursor-move select-none \".concat(getStatusColor(), \" \").concat(node.selected ? 'ring-2 ring-blue-400' : '', \" \").concat(isDragging ? 'shadow-lg' : ''),\n        style: {\n            left: node.position.x,\n            top: node.position.y,\n            width: node.size.width,\n            height: node.size.height\n        },\n        onMouseDown: handleMouseDown,\n        onClick: handleClick,\n        onDoubleClick: handleDoubleClick,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-2 py-2 bg-gray-100 border-b border-gray-200 rounded-t-md\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-sm font-medium text-gray-800 truncate\",\n                    children: node.name\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/nodes/BaseNodeComponent.tsx\",\n                    lineNumber: 209,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/nodes/BaseNodeComponent.tsx\",\n                lineNumber: 208,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-2 flex-1\",\n                children: children\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/nodes/BaseNodeComponent.tsx\",\n                lineNumber: 215,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute left-0\",\n                style: {\n                    top: titleHeight + topMargin\n                },\n                children: inputPins.map((pin, index)=>{\n                    // 如果只有一个引脚，居中显示；多个引脚则均匀分布\n                    const pinY = maxPins === 1 ? 0 : index * pinSpacing;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute flex items-center\",\n                        style: {\n                            top: pinY,\n                            left: -6,\n                            transform: 'translateY(-50%)' // 垂直居中对齐\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"pin-connector w-3 h-3 rounded-full border-2 cursor-pointer transition-colors \".concat(pin.connected ? 'bg-blue-500 border-blue-600' : 'bg-white border-gray-400 hover:border-blue-400'),\n                                title: pin.name\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/nodes/BaseNodeComponent.tsx\",\n                                lineNumber: 234,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"absolute left-4 top-1/2 transform -translate-y-1/2 text-xs text-gray-600 whitespace-nowrap\",\n                                children: pin.name\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/nodes/BaseNodeComponent.tsx\",\n                                lineNumber: 242,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, pin.id, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/nodes/BaseNodeComponent.tsx\",\n                        lineNumber: 225,\n                        columnNumber: 13\n                    }, undefined);\n                })\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/nodes/BaseNodeComponent.tsx\",\n                lineNumber: 220,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute right-0\",\n                style: {\n                    top: titleHeight + topMargin\n                },\n                children: outputPins.map((pin, index)=>{\n                    // 如果只有一个引脚，居中显示；多个引脚则均匀分布\n                    const pinY = maxPins === 1 ? 0 : index * pinSpacing;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute flex items-center justify-end\",\n                        style: {\n                            top: pinY,\n                            right: -6,\n                            transform: 'translateY(-50%)' // 垂直居中对齐\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"absolute right-4 top-1/2 transform -translate-y-1/2 text-xs text-gray-600 whitespace-nowrap\",\n                                children: pin.name\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/nodes/BaseNodeComponent.tsx\",\n                                lineNumber: 265,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"pin-connector w-3 h-3 rounded-full border-2 cursor-pointer transition-colors \".concat(pin.connected ? 'bg-green-500 border-green-600' : 'bg-white border-gray-400 hover:border-green-400'),\n                                title: pin.name\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/nodes/BaseNodeComponent.tsx\",\n                                lineNumber: 268,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, pin.id, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/nodes/BaseNodeComponent.tsx\",\n                        lineNumber: 256,\n                        columnNumber: 13\n                    }, undefined);\n                })\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/nodes/BaseNodeComponent.tsx\",\n                lineNumber: 251,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/components/nodes/BaseNodeComponent.tsx\",\n        lineNumber: 190,\n        columnNumber: 5\n    }, undefined);\n};\n_s(BaseNodeComponent, \"7O/WCX1jRM3DSG4/td1hT8DSAmU=\");\n_c = BaseNodeComponent;\nvar _c;\n$RefreshReg$(_c, \"BaseNodeComponent\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL25vZGVzL0Jhc2VOb2RlQ29tcG9uZW50LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFFNkQ7QUFPdEQsTUFBTUksb0JBQXNEO1FBQUMsRUFDbEVDLElBQUksRUFDSkMsT0FBTyxFQUNQQyxZQUFZLEVBQ1pDLFFBQVEsRUFDVDs7SUFDQyxNQUFNLENBQUNDLFlBQVlDLGNBQWMsR0FBR1QsK0NBQVFBLENBQUM7SUFDN0MsTUFBTSxDQUFDVSxZQUFZQyxjQUFjLEdBQUdYLCtDQUFRQSxDQUFDO1FBQUVZLEdBQUc7UUFBR0MsR0FBRztJQUFFO0lBQzFELE1BQU1DLFVBQVViLDZDQUFNQSxDQUFpQjtJQUN2QyxNQUFNYyxvQkFBb0JkLDZDQUFNQSxDQUFnQjtJQUNoRCxNQUFNZSxtQkFBbUJmLDZDQUFNQSxDQUFrQztJQUVqRSxTQUFTO0lBQ1QsTUFBTWdCLHdCQUF3QjtRQUM1QixNQUFNQyxZQUFZZCxLQUFLZSxJQUFJLENBQUNDLE1BQU0sQ0FBQ0MsQ0FBQUEsTUFBT0EsSUFBSUMsSUFBSSxLQUFLO1FBQ3ZELE1BQU1DLGFBQWFuQixLQUFLZSxJQUFJLENBQUNDLE1BQU0sQ0FBQ0MsQ0FBQUEsTUFBT0EsSUFBSUMsSUFBSSxLQUFLO1FBQ3hELE1BQU1FLFVBQVVDLEtBQUtDLEdBQUcsQ0FBQ1IsVUFBVVMsTUFBTSxFQUFFSixXQUFXSSxNQUFNO1FBRTVELHdEQUF3RDtRQUN4RCxNQUFNQyxjQUFjO1FBQ3BCLE9BQU87UUFDUCxNQUFNQyxlQUFlO1FBQ3JCLFdBQVc7UUFDWCxNQUFNQyxZQUFZO1FBQ2xCLFdBQVc7UUFDWCxNQUFNQyxrQkFBa0IzQixLQUFLNEIsSUFBSSxDQUFDQyxNQUFNLEdBQUdMLGNBQWNDLGVBQWVDO1FBRXhFLDZCQUE2QjtRQUM3QixJQUFJSSxhQUFhO1FBQ2pCLElBQUlWLFVBQVUsR0FBRztZQUNmVSxhQUFhSCxrQkFBbUJQLENBQUFBLFVBQVU7UUFDNUM7UUFFQSxPQUFPO1FBQ1AsSUFBSVcsS0FBc0MsSUFBSVgsVUFBVSxHQUFHO1lBQ3pEWSxRQUFRQyxHQUFHLENBQUMsTUFBZ0IsT0FBVmpDLEtBQUtrQyxJQUFJLEVBQUMsV0FBUztnQkFDbkNDLFlBQVluQyxLQUFLNEIsSUFBSSxDQUFDQyxNQUFNO2dCQUM1Qkw7Z0JBQ0FFO2dCQUNBRDtnQkFDQUU7Z0JBQ0FQO2dCQUNBVTtnQkFDQWhCLFdBQVdBLFVBQVVTLE1BQU07Z0JBQzNCSixZQUFZQSxXQUFXSSxNQUFNO1lBQy9CO1FBQ0Y7UUFFQSxPQUFPO1lBQ0xUO1lBQ0FLO1lBQ0FLO1lBQ0FFO1lBQ0FJO1lBQ0FIO1lBQ0FQO1FBQ0Y7SUFDRjtJQUVBLE1BQU0sRUFBRU4sU0FBUyxFQUFFSyxVQUFVLEVBQUVLLFdBQVcsRUFBRUUsU0FBUyxFQUFFSSxVQUFVLEVBQUVWLE9BQU8sRUFBRSxHQUFHUDtJQUUvRSw4QkFBOEI7SUFDOUIsTUFBTXVCLHFCQUFxQnRDLGtEQUFXQTs2REFBQztZQUNyQyxJQUFJYyxpQkFBaUJ5QixPQUFPLEVBQUU7Z0JBQzVCbkMsYUFBYUYsS0FBS3NDLEVBQUUsRUFBRTtvQkFBRUMsVUFBVTNCLGlCQUFpQnlCLE9BQU87Z0JBQUM7Z0JBQzNEekIsaUJBQWlCeUIsT0FBTyxHQUFHO1lBQzdCO1lBQ0ExQixrQkFBa0IwQixPQUFPLEdBQUc7UUFDOUI7NERBQUc7UUFBQ25DO1FBQWNGLEtBQUtzQyxFQUFFO0tBQUM7SUFFMUIsTUFBTUUsa0JBQWtCMUMsa0RBQVdBOzBEQUFDLENBQUMyQztZQUNuQyxjQUFjO1lBQ2QsSUFBSW5DLFdBQVdFLENBQUMsS0FBSyxLQUFLRixXQUFXRyxDQUFDLEtBQUssR0FBRztZQUU5QyxNQUFNaUMsY0FBYztnQkFDbEJsQyxHQUFHaUMsRUFBRUUsT0FBTyxHQUFHckMsV0FBV0UsQ0FBQztnQkFDM0JDLEdBQUdnQyxFQUFFRyxPQUFPLEdBQUd0QyxXQUFXRyxDQUFDO1lBQzdCO1lBRUEsc0JBQXNCO1lBQ3RCLElBQUlDLFFBQVEyQixPQUFPLEVBQUU7Z0JBQ25CM0IsUUFBUTJCLE9BQU8sQ0FBQ1EsS0FBSyxDQUFDQyxJQUFJLEdBQUcsR0FBaUIsT0FBZEosWUFBWWxDLENBQUMsRUFBQztnQkFDOUNFLFFBQVEyQixPQUFPLENBQUNRLEtBQUssQ0FBQ0UsR0FBRyxHQUFHLEdBQWlCLE9BQWRMLFlBQVlqQyxDQUFDLEVBQUM7WUFDL0M7WUFFQSxTQUFTO1lBQ1RHLGlCQUFpQnlCLE9BQU8sR0FBR0s7WUFDM0IsSUFBSS9CLGtCQUFrQjBCLE9BQU8sS0FBSyxNQUFNO2dCQUN0QzFCLGtCQUFrQjBCLE9BQU8sR0FBR1csc0JBQXNCWjtZQUNwRDtRQUNGO3lEQUFHO1FBQUM5QixXQUFXRSxDQUFDO1FBQUVGLFdBQVdHLENBQUM7UUFBRTJCO0tBQW1CO0lBRW5ELE1BQU1hLGdCQUFnQm5ELGtEQUFXQTt3REFBQztZQUNoQ08sY0FBYztZQUVkLFFBQVE7WUFDUixJQUFJTSxrQkFBa0IwQixPQUFPLEVBQUU7Z0JBQzdCYSxxQkFBcUJ2QyxrQkFBa0IwQixPQUFPO2dCQUM5QzFCLGtCQUFrQjBCLE9BQU8sR0FBRztZQUM5QjtZQUVBLGVBQWU7WUFDZixJQUFJekIsaUJBQWlCeUIsT0FBTyxFQUFFO2dCQUM1Qm5DLGFBQWFGLEtBQUtzQyxFQUFFLEVBQUU7b0JBQUVDLFVBQVUzQixpQkFBaUJ5QixPQUFPO2dCQUFDO2dCQUMzRHpCLGlCQUFpQnlCLE9BQU8sR0FBRztZQUM3QjtZQUVBcEMsUUFBUTtnQkFDTmlCLE1BQU07Z0JBQ05pQyxRQUFRbkQsS0FBS3NDLEVBQUU7WUFDakI7WUFFQSxhQUFhO1lBQ2JjLFNBQVNDLG1CQUFtQixDQUFDLGFBQWFiO1lBQzFDWSxTQUFTQyxtQkFBbUIsQ0FBQyxXQUFXSjtRQUMxQzt1REFBRztRQUFDVDtRQUFpQnZDO1FBQVNDO1FBQWNGLEtBQUtzQyxFQUFFO0tBQUM7SUFFcEQsTUFBTWdCLGtCQUFrQixDQUFDYjtRQUN2QixJQUFJQSxFQUFFYyxNQUFNLEtBQUssR0FBRyxRQUFRLFFBQVE7UUFFcEMsYUFBYTtRQUNiLE1BQU1DLFNBQVNmLEVBQUVlLE1BQU07UUFDdkIsSUFBSUEsT0FBT0MsT0FBTyxDQUFDLG1CQUFtQjtZQUNwQztRQUNGO1FBRUFoQixFQUFFaUIsY0FBYztRQUNoQmpCLEVBQUVrQixlQUFlO1FBRWpCLDBDQUEwQztRQUMxQyw0QkFBNEI7UUFDNUJwRCxjQUFjO1lBQ1pDLEdBQUdpQyxFQUFFRSxPQUFPLEdBQUczQyxLQUFLdUMsUUFBUSxDQUFDL0IsQ0FBQztZQUM5QkMsR0FBR2dDLEVBQUVHLE9BQU8sR0FBRzVDLEtBQUt1QyxRQUFRLENBQUM5QixDQUFDO1FBQ2hDO1FBRUFKLGNBQWM7UUFDZEosUUFBUTtZQUNOaUIsTUFBTTtZQUNOaUMsUUFBUW5ELEtBQUtzQyxFQUFFO1lBQ2ZzQixNQUFNO2dCQUFFckIsVUFBVXZDLEtBQUt1QyxRQUFRO1lBQUM7UUFDbEM7UUFFQSxhQUFhO1FBQ2JhLFNBQVNTLGdCQUFnQixDQUFDLGFBQWFyQjtRQUN2Q1ksU0FBU1MsZ0JBQWdCLENBQUMsV0FBV1o7SUFDdkM7SUFFQSxNQUFNYSxjQUFjLENBQUNyQjtRQUNuQkEsRUFBRWtCLGVBQWU7UUFDakIxRCxRQUFRO1lBQ05pQixNQUFNO1lBQ05pQyxRQUFRbkQsS0FBS3NDLEVBQUU7UUFDakI7SUFDRjtJQUVBLE1BQU15QixvQkFBb0IsQ0FBQ3RCO1FBQ3pCQSxFQUFFa0IsZUFBZTtRQUNqQjFELFFBQVE7WUFDTmlCLE1BQU07WUFDTmlDLFFBQVFuRCxLQUFLc0MsRUFBRTtRQUNqQjtJQUNGO0lBRUEsTUFBTTBCLGlCQUFpQjtRQUNyQixPQUFRaEUsS0FBS2lFLE1BQU07WUFDakIsS0FBSztnQkFDSCxPQUFPO1lBQ1QsS0FBSztnQkFDSCxPQUFPO1lBQ1QsS0FBSztnQkFDSCxPQUFPO1lBQ1QsS0FBSztnQkFDSCxPQUFPO1lBQ1Q7Z0JBQ0UsT0FBTztRQUNYO0lBQ0Y7SUFFQSxxQkFDRSw4REFBQ0M7UUFDQ0MsS0FBS3pEO1FBQ0wwRCxXQUFXLGtFQUVQcEUsT0FERmdFLGtCQUNELEtBQ0M1RCxPQURFSixLQUFLcUUsUUFBUSxHQUFHLHlCQUF5QixJQUFHLEtBRS9DLE9BRENqRSxhQUFhLGNBQWM7UUFFN0J5QyxPQUFPO1lBQ0xDLE1BQU05QyxLQUFLdUMsUUFBUSxDQUFDL0IsQ0FBQztZQUNyQnVDLEtBQUsvQyxLQUFLdUMsUUFBUSxDQUFDOUIsQ0FBQztZQUNwQjZELE9BQU90RSxLQUFLNEIsSUFBSSxDQUFDMEMsS0FBSztZQUN0QnpDLFFBQVE3QixLQUFLNEIsSUFBSSxDQUFDQyxNQUFNO1FBQzFCO1FBQ0EwQyxhQUFhakI7UUFDYmtCLFNBQVNWO1FBQ1RXLGVBQWVWOzswQkFHZiw4REFBQ0c7Z0JBQUlFLFdBQVU7MEJBQ2IsNEVBQUNGO29CQUFJRSxXQUFVOzhCQUNacEUsS0FBS2tDLElBQUk7Ozs7Ozs7Ozs7OzBCQUtkLDhEQUFDZ0M7Z0JBQUlFLFdBQVU7MEJBQ1pqRTs7Ozs7OzBCQUlILDhEQUFDK0Q7Z0JBQUlFLFdBQVU7Z0JBQWtCdkIsT0FBTztvQkFBRUUsS0FBS3ZCLGNBQWNFO2dCQUFVOzBCQUNwRVosVUFBVTRELEdBQUcsQ0FBQyxDQUFDekQsS0FBSzBEO29CQUNuQiwwQkFBMEI7b0JBQzFCLE1BQU1DLE9BQU94RCxZQUFZLElBQUksSUFBSXVELFFBQVE3QztvQkFDekMscUJBQ0UsOERBQUNvQzt3QkFFQ0UsV0FBVTt3QkFDVnZCLE9BQU87NEJBQ0xFLEtBQUs2Qjs0QkFDTDlCLE1BQU0sQ0FBQzs0QkFDUCtCLFdBQVcsbUJBQW1CLFNBQVM7d0JBQ3pDOzswQ0FFQSw4REFBQ1g7Z0NBQ0NFLFdBQVcsZ0ZBSVYsT0FIQ25ELElBQUk2RCxTQUFTLEdBQ1QsZ0NBQ0E7Z0NBRU5DLE9BQU85RCxJQUFJaUIsSUFBSTs7Ozs7OzBDQUVqQiw4REFBQzhDO2dDQUFLWixXQUFVOzBDQUNibkQsSUFBSWlCLElBQUk7Ozs7Ozs7dUJBakJOakIsSUFBSXFCLEVBQUU7Ozs7O2dCQXFCakI7Ozs7OzswQkFJRiw4REFBQzRCO2dCQUFJRSxXQUFVO2dCQUFtQnZCLE9BQU87b0JBQUVFLEtBQUt2QixjQUFjRTtnQkFBVTswQkFDckVQLFdBQVd1RCxHQUFHLENBQUMsQ0FBQ3pELEtBQUswRDtvQkFDcEIsMEJBQTBCO29CQUMxQixNQUFNQyxPQUFPeEQsWUFBWSxJQUFJLElBQUl1RCxRQUFRN0M7b0JBQ3pDLHFCQUNFLDhEQUFDb0M7d0JBRUNFLFdBQVU7d0JBQ1Z2QixPQUFPOzRCQUNMRSxLQUFLNkI7NEJBQ0xLLE9BQU8sQ0FBQzs0QkFDUkosV0FBVyxtQkFBbUIsU0FBUzt3QkFDekM7OzBDQUVBLDhEQUFDRztnQ0FBS1osV0FBVTswQ0FDYm5ELElBQUlpQixJQUFJOzs7Ozs7MENBRVgsOERBQUNnQztnQ0FDQ0UsV0FBVyxnRkFJVixPQUhDbkQsSUFBSTZELFNBQVMsR0FDVCxrQ0FDQTtnQ0FFTkMsT0FBTzlELElBQUlpQixJQUFJOzs7Ozs7O3VCQWpCWmpCLElBQUlxQixFQUFFOzs7OztnQkFxQmpCOzs7Ozs7Ozs7Ozs7QUFJUixFQUFFO0dBaFJXdkM7S0FBQUEiLCJzb3VyY2VzIjpbIi9Vc2Vycy91c2VyL0RvY3VtZW50cy9hdWdtZW50LXByb2plY3RzL25leHRqcy1zdXBlck5vZGUvc3JjL2NvbXBvbmVudHMvbm9kZXMvQmFzZU5vZGVDb21wb25lbnQudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcblxuaW1wb3J0IFJlYWN0LCB7IHVzZVN0YXRlLCB1c2VSZWYsIHVzZUNhbGxiYWNrIH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgTm9kZUNvbXBvbmVudFByb3BzLCBOb2RlRXZlbnQgfSBmcm9tICdAL3R5cGVzL25vZGUnO1xuXG5leHBvcnQgaW50ZXJmYWNlIEJhc2VOb2RlQ29tcG9uZW50UHJvcHMgZXh0ZW5kcyBOb2RlQ29tcG9uZW50UHJvcHMge1xuICBjaGlsZHJlbj86IFJlYWN0LlJlYWN0Tm9kZTtcbn1cblxuZXhwb3J0IGNvbnN0IEJhc2VOb2RlQ29tcG9uZW50OiBSZWFjdC5GQzxCYXNlTm9kZUNvbXBvbmVudFByb3BzPiA9ICh7XG4gIG5vZGUsXG4gIG9uRXZlbnQsXG4gIG9uVXBkYXRlTm9kZSxcbiAgY2hpbGRyZW4sXG59KSA9PiB7XG4gIGNvbnN0IFtpc0RyYWdnaW5nLCBzZXRJc0RyYWdnaW5nXSA9IHVzZVN0YXRlKGZhbHNlKTtcbiAgY29uc3QgW2RyYWdPZmZzZXQsIHNldERyYWdPZmZzZXRdID0gdXNlU3RhdGUoeyB4OiAwLCB5OiAwIH0pO1xuICBjb25zdCBub2RlUmVmID0gdXNlUmVmPEhUTUxEaXZFbGVtZW50PihudWxsKTtcbiAgY29uc3QgYW5pbWF0aW9uRnJhbWVSZWYgPSB1c2VSZWY8bnVtYmVyIHwgbnVsbD4obnVsbCk7XG4gIGNvbnN0IHBlbmRpbmdVcGRhdGVSZWYgPSB1c2VSZWY8eyB4OiBudW1iZXI7IHk6IG51bWJlciB9IHwgbnVsbD4obnVsbCk7XG5cbiAgLy8g6K6h566X5byV6ISa5L2N572uXG4gIGNvbnN0IGNhbGN1bGF0ZVBpblBvc2l0aW9ucyA9ICgpID0+IHtcbiAgICBjb25zdCBpbnB1dFBpbnMgPSBub2RlLnBpbnMuZmlsdGVyKHBpbiA9PiBwaW4udHlwZSA9PT0gJ2lucHV0Jyk7XG4gICAgY29uc3Qgb3V0cHV0UGlucyA9IG5vZGUucGlucy5maWx0ZXIocGluID0+IHBpbi50eXBlID09PSAnb3V0cHV0Jyk7XG4gICAgY29uc3QgbWF4UGlucyA9IE1hdGgubWF4KGlucHV0UGlucy5sZW5ndGgsIG91dHB1dFBpbnMubGVuZ3RoKTtcblxuICAgIC8vIOagh+mimOagj+mrmOW6pu+8mnBhZGRpbmcoMTZweCkgKyDmloflrZfpq5jluqYoMjBweCkgKyBib3JkZXIoMXB4KSA9IDM3cHhcbiAgICBjb25zdCB0aXRsZUhlaWdodCA9IDM3O1xuICAgIC8vIOW6lemDqOeVmeeZvVxuICAgIGNvbnN0IGJvdHRvbU1hcmdpbiA9IDEyO1xuICAgIC8vIOW8leiEmuWMuuWfn+mhtumDqOeVmeeZvVxuICAgIGNvbnN0IHRvcE1hcmdpbiA9IDEyO1xuICAgIC8vIOWPr+eUqOS6juW8leiEmueahOmrmOW6plxuICAgIGNvbnN0IGF2YWlsYWJsZUhlaWdodCA9IG5vZGUuc2l6ZS5oZWlnaHQgLSB0aXRsZUhlaWdodCAtIGJvdHRvbU1hcmdpbiAtIHRvcE1hcmdpbjtcblxuICAgIC8vIOW8leiEmumXtOi3ne+8jOWmguaenOWPquacieS4gOS4quW8leiEmuWImeWxheS4re+8jOWkmuS4quW8leiEmuWImeWdh+WMgOWIhuW4g1xuICAgIGxldCBwaW5TcGFjaW5nID0gMDtcbiAgICBpZiAobWF4UGlucyA+IDEpIHtcbiAgICAgIHBpblNwYWNpbmcgPSBhdmFpbGFibGVIZWlnaHQgLyAobWF4UGlucyAtIDEpO1xuICAgIH1cblxuICAgIC8vIOiwg+ivleS/oeaBr1xuICAgIGlmIChwcm9jZXNzLmVudi5OT0RFX0VOViA9PT0gJ2RldmVsb3BtZW50JyAmJiBtYXhQaW5zID4gMCkge1xuICAgICAgY29uc29sZS5sb2coYOiKgueCuSAke25vZGUubmFtZX0g5byV6ISa6K6h566XOmAsIHtcbiAgICAgICAgbm9kZUhlaWdodDogbm9kZS5zaXplLmhlaWdodCxcbiAgICAgICAgdGl0bGVIZWlnaHQsXG4gICAgICAgIHRvcE1hcmdpbixcbiAgICAgICAgYm90dG9tTWFyZ2luLFxuICAgICAgICBhdmFpbGFibGVIZWlnaHQsXG4gICAgICAgIG1heFBpbnMsXG4gICAgICAgIHBpblNwYWNpbmcsXG4gICAgICAgIGlucHV0UGluczogaW5wdXRQaW5zLmxlbmd0aCxcbiAgICAgICAgb3V0cHV0UGluczogb3V0cHV0UGlucy5sZW5ndGhcbiAgICAgIH0pO1xuICAgIH1cblxuICAgIHJldHVybiB7XG4gICAgICBpbnB1dFBpbnMsXG4gICAgICBvdXRwdXRQaW5zLFxuICAgICAgdGl0bGVIZWlnaHQsXG4gICAgICB0b3BNYXJnaW4sXG4gICAgICBwaW5TcGFjaW5nLFxuICAgICAgYXZhaWxhYmxlSGVpZ2h0LFxuICAgICAgbWF4UGluc1xuICAgIH07XG4gIH07XG5cbiAgY29uc3QgeyBpbnB1dFBpbnMsIG91dHB1dFBpbnMsIHRpdGxlSGVpZ2h0LCB0b3BNYXJnaW4sIHBpblNwYWNpbmcsIG1heFBpbnMgfSA9IGNhbGN1bGF0ZVBpblBvc2l0aW9ucygpO1xuXG4gIC8vIOS9v+eUqHJlcXVlc3RBbmltYXRpb25GcmFtZeiKgua1geabtOaWsFxuICBjb25zdCBmbHVzaFBlbmRpbmdVcGRhdGUgPSB1c2VDYWxsYmFjaygoKSA9PiB7XG4gICAgaWYgKHBlbmRpbmdVcGRhdGVSZWYuY3VycmVudCkge1xuICAgICAgb25VcGRhdGVOb2RlKG5vZGUuaWQsIHsgcG9zaXRpb246IHBlbmRpbmdVcGRhdGVSZWYuY3VycmVudCB9KTtcbiAgICAgIHBlbmRpbmdVcGRhdGVSZWYuY3VycmVudCA9IG51bGw7XG4gICAgfVxuICAgIGFuaW1hdGlvbkZyYW1lUmVmLmN1cnJlbnQgPSBudWxsO1xuICB9LCBbb25VcGRhdGVOb2RlLCBub2RlLmlkXSk7XG5cbiAgY29uc3QgaGFuZGxlTW91c2VNb3ZlID0gdXNlQ2FsbGJhY2soKGU6IE1vdXNlRXZlbnQpID0+IHtcbiAgICAvLyDnoa7kv53mi5bmi73lgY/np7vph4/lt7Lnu4/orr7nva5cbiAgICBpZiAoZHJhZ09mZnNldC54ID09PSAwICYmIGRyYWdPZmZzZXQueSA9PT0gMCkgcmV0dXJuO1xuXG4gICAgY29uc3QgbmV3UG9zaXRpb24gPSB7XG4gICAgICB4OiBlLmNsaWVudFggLSBkcmFnT2Zmc2V0LngsXG4gICAgICB5OiBlLmNsaWVudFkgLSBkcmFnT2Zmc2V0LnksXG4gICAgfTtcblxuICAgIC8vIOeri+WNs+abtOaWsERPTeS9jee9ruS7peiOt+W+l+a1geeVheeahOinhuinieaViOaenFxuICAgIGlmIChub2RlUmVmLmN1cnJlbnQpIHtcbiAgICAgIG5vZGVSZWYuY3VycmVudC5zdHlsZS5sZWZ0ID0gYCR7bmV3UG9zaXRpb24ueH1weGA7XG4gICAgICBub2RlUmVmLmN1cnJlbnQuc3R5bGUudG9wID0gYCR7bmV3UG9zaXRpb24ueX1weGA7XG4gICAgfVxuXG4gICAgLy8g6IqC5rWB54q25oCB5pu05pawXG4gICAgcGVuZGluZ1VwZGF0ZVJlZi5jdXJyZW50ID0gbmV3UG9zaXRpb247XG4gICAgaWYgKGFuaW1hdGlvbkZyYW1lUmVmLmN1cnJlbnQgPT09IG51bGwpIHtcbiAgICAgIGFuaW1hdGlvbkZyYW1lUmVmLmN1cnJlbnQgPSByZXF1ZXN0QW5pbWF0aW9uRnJhbWUoZmx1c2hQZW5kaW5nVXBkYXRlKTtcbiAgICB9XG4gIH0sIFtkcmFnT2Zmc2V0LngsIGRyYWdPZmZzZXQueSwgZmx1c2hQZW5kaW5nVXBkYXRlXSk7XG5cbiAgY29uc3QgaGFuZGxlTW91c2VVcCA9IHVzZUNhbGxiYWNrKCgpID0+IHtcbiAgICBzZXRJc0RyYWdnaW5nKGZhbHNlKTtcblxuICAgIC8vIOa4heeQhuWKqOeUu+W4p1xuICAgIGlmIChhbmltYXRpb25GcmFtZVJlZi5jdXJyZW50KSB7XG4gICAgICBjYW5jZWxBbmltYXRpb25GcmFtZShhbmltYXRpb25GcmFtZVJlZi5jdXJyZW50KTtcbiAgICAgIGFuaW1hdGlvbkZyYW1lUmVmLmN1cnJlbnQgPSBudWxsO1xuICAgIH1cblxuICAgIC8vIOehruS/neacgOWQjueahOS9jee9ruabtOaWsOiiq+W6lOeUqFxuICAgIGlmIChwZW5kaW5nVXBkYXRlUmVmLmN1cnJlbnQpIHtcbiAgICAgIG9uVXBkYXRlTm9kZShub2RlLmlkLCB7IHBvc2l0aW9uOiBwZW5kaW5nVXBkYXRlUmVmLmN1cnJlbnQgfSk7XG4gICAgICBwZW5kaW5nVXBkYXRlUmVmLmN1cnJlbnQgPSBudWxsO1xuICAgIH1cblxuICAgIG9uRXZlbnQoe1xuICAgICAgdHlwZTogJ2RyYWdFbmQnLFxuICAgICAgbm9kZUlkOiBub2RlLmlkLFxuICAgIH0pO1xuXG4gICAgLy8g56e76Zmk5YWo5bGA6byg5qCH5LqL5Lu255uR5ZCsXG4gICAgZG9jdW1lbnQucmVtb3ZlRXZlbnRMaXN0ZW5lcignbW91c2Vtb3ZlJywgaGFuZGxlTW91c2VNb3ZlKTtcbiAgICBkb2N1bWVudC5yZW1vdmVFdmVudExpc3RlbmVyKCdtb3VzZXVwJywgaGFuZGxlTW91c2VVcCk7XG4gIH0sIFtoYW5kbGVNb3VzZU1vdmUsIG9uRXZlbnQsIG9uVXBkYXRlTm9kZSwgbm9kZS5pZF0pO1xuXG4gIGNvbnN0IGhhbmRsZU1vdXNlRG93biA9IChlOiBSZWFjdC5Nb3VzZUV2ZW50KSA9PiB7XG4gICAgaWYgKGUuYnV0dG9uICE9PSAwKSByZXR1cm47IC8vIOWPquWkhOeQhuW3pumUrlxuXG4gICAgLy8g6Ziy5q2i5Zyo5byV6ISa5LiK5byA5aeL5ouW5ou9XG4gICAgY29uc3QgdGFyZ2V0ID0gZS50YXJnZXQgYXMgSFRNTEVsZW1lbnQ7XG4gICAgaWYgKHRhcmdldC5jbG9zZXN0KCcucGluLWNvbm5lY3RvcicpKSB7XG4gICAgICByZXR1cm47XG4gICAgfVxuXG4gICAgZS5wcmV2ZW50RGVmYXVsdCgpO1xuICAgIGUuc3RvcFByb3BhZ2F0aW9uKCk7XG5cbiAgICAvLyDkvb/nlKjoioLngrnnmoTlvZPliY3kvY3nva7ogIzkuI3mmK9nZXRCb3VuZGluZ0NsaWVudFJlY3TmnaXorqHnrpflgY/np7vph49cbiAgICAvLyDov5nmoLflj6/ku6Xpgb/lhY3lm6DkuLpDU1Plj5jmjaLmiJblhbbku5blm6DntKDlr7zoh7TnmoTkvY3nva7lgY/lt65cbiAgICBzZXREcmFnT2Zmc2V0KHtcbiAgICAgIHg6IGUuY2xpZW50WCAtIG5vZGUucG9zaXRpb24ueCxcbiAgICAgIHk6IGUuY2xpZW50WSAtIG5vZGUucG9zaXRpb24ueSxcbiAgICB9KTtcblxuICAgIHNldElzRHJhZ2dpbmcodHJ1ZSk7XG4gICAgb25FdmVudCh7XG4gICAgICB0eXBlOiAnZHJhZ1N0YXJ0JyxcbiAgICAgIG5vZGVJZDogbm9kZS5pZCxcbiAgICAgIGRhdGE6IHsgcG9zaXRpb246IG5vZGUucG9zaXRpb24gfSxcbiAgICB9KTtcblxuICAgIC8vIOa3u+WKoOWFqOWxgOm8oOagh+S6i+S7tuebkeWQrFxuICAgIGRvY3VtZW50LmFkZEV2ZW50TGlzdGVuZXIoJ21vdXNlbW92ZScsIGhhbmRsZU1vdXNlTW92ZSk7XG4gICAgZG9jdW1lbnQuYWRkRXZlbnRMaXN0ZW5lcignbW91c2V1cCcsIGhhbmRsZU1vdXNlVXApO1xuICB9O1xuXG4gIGNvbnN0IGhhbmRsZUNsaWNrID0gKGU6IFJlYWN0Lk1vdXNlRXZlbnQpID0+IHtcbiAgICBlLnN0b3BQcm9wYWdhdGlvbigpO1xuICAgIG9uRXZlbnQoe1xuICAgICAgdHlwZTogJ2NsaWNrJyxcbiAgICAgIG5vZGVJZDogbm9kZS5pZCxcbiAgICB9KTtcbiAgfTtcblxuICBjb25zdCBoYW5kbGVEb3VibGVDbGljayA9IChlOiBSZWFjdC5Nb3VzZUV2ZW50KSA9PiB7XG4gICAgZS5zdG9wUHJvcGFnYXRpb24oKTtcbiAgICBvbkV2ZW50KHtcbiAgICAgIHR5cGU6ICdkb3VibGVDbGljaycsXG4gICAgICBub2RlSWQ6IG5vZGUuaWQsXG4gICAgfSk7XG4gIH07XG5cbiAgY29uc3QgZ2V0U3RhdHVzQ29sb3IgPSAoKSA9PiB7XG4gICAgc3dpdGNoIChub2RlLnN0YXR1cykge1xuICAgICAgY2FzZSAncnVubmluZyc6XG4gICAgICAgIHJldHVybiAnYm9yZGVyLXllbGxvdy00MDAgYmcteWVsbG93LTUwJztcbiAgICAgIGNhc2UgJ3N1Y2Nlc3MnOlxuICAgICAgICByZXR1cm4gJ2JvcmRlci1ncmVlbi00MDAgYmctZ3JlZW4tNTAnO1xuICAgICAgY2FzZSAnZXJyb3InOlxuICAgICAgICByZXR1cm4gJ2JvcmRlci1yZWQtNDAwIGJnLXJlZC01MCc7XG4gICAgICBjYXNlICd3YXJuaW5nJzpcbiAgICAgICAgcmV0dXJuICdib3JkZXItb3JhbmdlLTQwMCBiZy1vcmFuZ2UtNTAnO1xuICAgICAgZGVmYXVsdDpcbiAgICAgICAgcmV0dXJuICdib3JkZXItZ3JheS0zMDAgYmctd2hpdGUnO1xuICAgIH1cbiAgfTtcblxuICByZXR1cm4gKFxuICAgIDxkaXZcbiAgICAgIHJlZj17bm9kZVJlZn1cbiAgICAgIGNsYXNzTmFtZT17YGFic29sdXRlIGJvcmRlci0yIHJvdW5kZWQtbGcgc2hhZG93LW1kIGN1cnNvci1tb3ZlIHNlbGVjdC1ub25lICR7XG4gICAgICAgIGdldFN0YXR1c0NvbG9yKClcbiAgICAgIH0gJHtub2RlLnNlbGVjdGVkID8gJ3JpbmctMiByaW5nLWJsdWUtNDAwJyA6ICcnfSAke1xuICAgICAgICBpc0RyYWdnaW5nID8gJ3NoYWRvdy1sZycgOiAnJ1xuICAgICAgfWB9XG4gICAgICBzdHlsZT17e1xuICAgICAgICBsZWZ0OiBub2RlLnBvc2l0aW9uLngsXG4gICAgICAgIHRvcDogbm9kZS5wb3NpdGlvbi55LFxuICAgICAgICB3aWR0aDogbm9kZS5zaXplLndpZHRoLFxuICAgICAgICBoZWlnaHQ6IG5vZGUuc2l6ZS5oZWlnaHQsXG4gICAgICB9fVxuICAgICAgb25Nb3VzZURvd249e2hhbmRsZU1vdXNlRG93bn1cbiAgICAgIG9uQ2xpY2s9e2hhbmRsZUNsaWNrfVxuICAgICAgb25Eb3VibGVDbGljaz17aGFuZGxlRG91YmxlQ2xpY2t9XG4gICAgPlxuICAgICAgey8qIOiKgueCueagh+mimOagjyAqL31cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwicHgtMiBweS0yIGJnLWdyYXktMTAwIGJvcmRlci1iIGJvcmRlci1ncmF5LTIwMCByb3VuZGVkLXQtbWRcIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS04MDAgdHJ1bmNhdGVcIj5cbiAgICAgICAgICB7bm9kZS5uYW1lfVxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuXG4gICAgICB7Lyog6IqC54K55YaF5a655Yy65Z+fICovfVxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJwLTIgZmxleC0xXCI+XG4gICAgICAgIHtjaGlsZHJlbn1cbiAgICAgIDwvZGl2PlxuXG4gICAgICB7Lyog6L6T5YWl5byV6ISaICovfVxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSBsZWZ0LTBcIiBzdHlsZT17eyB0b3A6IHRpdGxlSGVpZ2h0ICsgdG9wTWFyZ2luIH19PlxuICAgICAgICB7aW5wdXRQaW5zLm1hcCgocGluLCBpbmRleCkgPT4ge1xuICAgICAgICAgIC8vIOWmguaenOWPquacieS4gOS4quW8leiEmu+8jOWxheS4reaYvuekuu+8m+WkmuS4quW8leiEmuWImeWdh+WMgOWIhuW4g1xuICAgICAgICAgIGNvbnN0IHBpblkgPSBtYXhQaW5zID09PSAxID8gMCA6IGluZGV4ICogcGluU3BhY2luZztcbiAgICAgICAgICByZXR1cm4gKFxuICAgICAgICAgICAgPGRpdlxuICAgICAgICAgICAgICBrZXk9e3Bpbi5pZH1cbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYWJzb2x1dGUgZmxleCBpdGVtcy1jZW50ZXJcIlxuICAgICAgICAgICAgICBzdHlsZT17e1xuICAgICAgICAgICAgICAgIHRvcDogcGluWSxcbiAgICAgICAgICAgICAgICBsZWZ0OiAtNiwgLy8g5byV6ISa5ZyG54K555qE5LiA5Y2K5Zyo6IqC54K55aSWXG4gICAgICAgICAgICAgICAgdHJhbnNmb3JtOiAndHJhbnNsYXRlWSgtNTAlKScgLy8g5Z6C55u05bGF5Lit5a+56b2QXG4gICAgICAgICAgICAgIH19XG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIDxkaXZcbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2BwaW4tY29ubmVjdG9yIHctMyBoLTMgcm91bmRlZC1mdWxsIGJvcmRlci0yIGN1cnNvci1wb2ludGVyIHRyYW5zaXRpb24tY29sb3JzICR7XG4gICAgICAgICAgICAgICAgICBwaW4uY29ubmVjdGVkXG4gICAgICAgICAgICAgICAgICAgID8gJ2JnLWJsdWUtNTAwIGJvcmRlci1ibHVlLTYwMCdcbiAgICAgICAgICAgICAgICAgICAgOiAnYmctd2hpdGUgYm9yZGVyLWdyYXktNDAwIGhvdmVyOmJvcmRlci1ibHVlLTQwMCdcbiAgICAgICAgICAgICAgICB9YH1cbiAgICAgICAgICAgICAgICB0aXRsZT17cGluLm5hbWV9XG4gICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImFic29sdXRlIGxlZnQtNCB0b3AtMS8yIHRyYW5zZm9ybSAtdHJhbnNsYXRlLXktMS8yIHRleHQteHMgdGV4dC1ncmF5LTYwMCB3aGl0ZXNwYWNlLW5vd3JhcFwiPlxuICAgICAgICAgICAgICAgIHtwaW4ubmFtZX1cbiAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgKTtcbiAgICAgICAgfSl9XG4gICAgICA8L2Rpdj5cblxuICAgICAgey8qIOi+k+WHuuW8leiEmiAqL31cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgcmlnaHQtMFwiIHN0eWxlPXt7IHRvcDogdGl0bGVIZWlnaHQgKyB0b3BNYXJnaW4gfX0+XG4gICAgICAgIHtvdXRwdXRQaW5zLm1hcCgocGluLCBpbmRleCkgPT4ge1xuICAgICAgICAgIC8vIOWmguaenOWPquacieS4gOS4quW8leiEmu+8jOWxheS4reaYvuekuu+8m+WkmuS4quW8leiEmuWImeWdh+WMgOWIhuW4g1xuICAgICAgICAgIGNvbnN0IHBpblkgPSBtYXhQaW5zID09PSAxID8gMCA6IGluZGV4ICogcGluU3BhY2luZztcbiAgICAgICAgICByZXR1cm4gKFxuICAgICAgICAgICAgPGRpdlxuICAgICAgICAgICAgICBrZXk9e3Bpbi5pZH1cbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYWJzb2x1dGUgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1lbmRcIlxuICAgICAgICAgICAgICBzdHlsZT17e1xuICAgICAgICAgICAgICAgIHRvcDogcGluWSxcbiAgICAgICAgICAgICAgICByaWdodDogLTYsIC8vIOW8leiEmuWchueCueeahOS4gOWNiuWcqOiKgueCueWkllxuICAgICAgICAgICAgICAgIHRyYW5zZm9ybTogJ3RyYW5zbGF0ZVkoLTUwJSknIC8vIOWeguebtOWxheS4reWvuem9kFxuICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJhYnNvbHV0ZSByaWdodC00IHRvcC0xLzIgdHJhbnNmb3JtIC10cmFuc2xhdGUteS0xLzIgdGV4dC14cyB0ZXh0LWdyYXktNjAwIHdoaXRlc3BhY2Utbm93cmFwXCI+XG4gICAgICAgICAgICAgICAge3Bpbi5uYW1lfVxuICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgIDxkaXZcbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2BwaW4tY29ubmVjdG9yIHctMyBoLTMgcm91bmRlZC1mdWxsIGJvcmRlci0yIGN1cnNvci1wb2ludGVyIHRyYW5zaXRpb24tY29sb3JzICR7XG4gICAgICAgICAgICAgICAgICBwaW4uY29ubmVjdGVkXG4gICAgICAgICAgICAgICAgICAgID8gJ2JnLWdyZWVuLTUwMCBib3JkZXItZ3JlZW4tNjAwJ1xuICAgICAgICAgICAgICAgICAgICA6ICdiZy13aGl0ZSBib3JkZXItZ3JheS00MDAgaG92ZXI6Ym9yZGVyLWdyZWVuLTQwMCdcbiAgICAgICAgICAgICAgICB9YH1cbiAgICAgICAgICAgICAgICB0aXRsZT17cGluLm5hbWV9XG4gICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICApO1xuICAgICAgICB9KX1cbiAgICAgIDwvZGl2PlxuICAgIDwvZGl2PlxuICApO1xufTtcbiJdLCJuYW1lcyI6WyJSZWFjdCIsInVzZVN0YXRlIiwidXNlUmVmIiwidXNlQ2FsbGJhY2siLCJCYXNlTm9kZUNvbXBvbmVudCIsIm5vZGUiLCJvbkV2ZW50Iiwib25VcGRhdGVOb2RlIiwiY2hpbGRyZW4iLCJpc0RyYWdnaW5nIiwic2V0SXNEcmFnZ2luZyIsImRyYWdPZmZzZXQiLCJzZXREcmFnT2Zmc2V0IiwieCIsInkiLCJub2RlUmVmIiwiYW5pbWF0aW9uRnJhbWVSZWYiLCJwZW5kaW5nVXBkYXRlUmVmIiwiY2FsY3VsYXRlUGluUG9zaXRpb25zIiwiaW5wdXRQaW5zIiwicGlucyIsImZpbHRlciIsInBpbiIsInR5cGUiLCJvdXRwdXRQaW5zIiwibWF4UGlucyIsIk1hdGgiLCJtYXgiLCJsZW5ndGgiLCJ0aXRsZUhlaWdodCIsImJvdHRvbU1hcmdpbiIsInRvcE1hcmdpbiIsImF2YWlsYWJsZUhlaWdodCIsInNpemUiLCJoZWlnaHQiLCJwaW5TcGFjaW5nIiwicHJvY2VzcyIsImNvbnNvbGUiLCJsb2ciLCJuYW1lIiwibm9kZUhlaWdodCIsImZsdXNoUGVuZGluZ1VwZGF0ZSIsImN1cnJlbnQiLCJpZCIsInBvc2l0aW9uIiwiaGFuZGxlTW91c2VNb3ZlIiwiZSIsIm5ld1Bvc2l0aW9uIiwiY2xpZW50WCIsImNsaWVudFkiLCJzdHlsZSIsImxlZnQiLCJ0b3AiLCJyZXF1ZXN0QW5pbWF0aW9uRnJhbWUiLCJoYW5kbGVNb3VzZVVwIiwiY2FuY2VsQW5pbWF0aW9uRnJhbWUiLCJub2RlSWQiLCJkb2N1bWVudCIsInJlbW92ZUV2ZW50TGlzdGVuZXIiLCJoYW5kbGVNb3VzZURvd24iLCJidXR0b24iLCJ0YXJnZXQiLCJjbG9zZXN0IiwicHJldmVudERlZmF1bHQiLCJzdG9wUHJvcGFnYXRpb24iLCJkYXRhIiwiYWRkRXZlbnRMaXN0ZW5lciIsImhhbmRsZUNsaWNrIiwiaGFuZGxlRG91YmxlQ2xpY2siLCJnZXRTdGF0dXNDb2xvciIsInN0YXR1cyIsImRpdiIsInJlZiIsImNsYXNzTmFtZSIsInNlbGVjdGVkIiwid2lkdGgiLCJvbk1vdXNlRG93biIsIm9uQ2xpY2siLCJvbkRvdWJsZUNsaWNrIiwibWFwIiwiaW5kZXgiLCJwaW5ZIiwidHJhbnNmb3JtIiwiY29ubmVjdGVkIiwidGl0bGUiLCJzcGFuIiwicmlnaHQiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/nodes/BaseNodeComponent.tsx\n"));

/***/ })

});