"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_src_nodes_controls_KnobNode_tsx",{

/***/ "(app-pages-browser)/./src/nodes/controls/KnobNode.tsx":
/*!*****************************************!*\
  !*** ./src/nodes/controls/KnobNode.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   KnobNode: () => (/* binding */ KnobNode)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.4.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _base_BaseNode__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../base/BaseNode */ \"(app-pages-browser)/./src/base/BaseNode.ts\");\n\n\n\nclass KnobNode extends _base_BaseNode__WEBPACK_IMPORTED_MODULE_2__.BaseNode {\n    initializePins() {\n        this.updatePinsConfiguration();\n    }\n    updatePinsConfiguration() {\n        const value = this.getProperty('value');\n        this.data.pins = [\n            {\n                id: 'value_output',\n                name: 'Value',\n                type: 'output',\n                dataType: 'number',\n                value\n            }\n        ];\n        return super.updatePinsConfiguration();\n    }\n    async process() {\n        this.setStatus('running');\n        const value = this.getProperty('value');\n        this.setPinValue('value_output', value);\n        this.setStatus('success');\n    }\n    onPropertyChanged(key, value) {\n        if (key === 'value' || key === 'outputRange') {\n            this.process();\n        }\n        super.onPropertyChanged(key, value);\n    }\n    static renderProperties(param) {\n        let { node, onUpdateNode } = param;\n        const properties = node.properties;\n        const handlePropertyChange = (key, value)=>{\n            onUpdateNode(node.id, {\n                properties: {\n                    ...node.properties,\n                    [key]: value\n                }\n            });\n        };\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-4\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                        children: \"输出范围\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/nodes/controls/KnobNode.tsx\",\n                        lineNumber: 65,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                        value: properties.outputRange || 'percentage',\n                        onChange: (e)=>handlePropertyChange('outputRange', e.target.value),\n                        className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                value: \"percentage\",\n                                children: \"百分比 (0-100)\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/nodes/controls/KnobNode.tsx\",\n                                lineNumber: 73,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                value: \"0-255\",\n                                children: \"0-255\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/nodes/controls/KnobNode.tsx\",\n                                lineNumber: 74,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                value: \"0-1\",\n                                children: \"0-1\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/nodes/controls/KnobNode.tsx\",\n                                lineNumber: 75,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                value: \"boolean\",\n                                children: \"布尔值\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/nodes/controls/KnobNode.tsx\",\n                                lineNumber: 76,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/nodes/controls/KnobNode.tsx\",\n                        lineNumber: 68,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/nodes/controls/KnobNode.tsx\",\n                lineNumber: 64,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/augment-projects/nextjs-superNode/src/nodes/controls/KnobNode.tsx\",\n            lineNumber: 63,\n            columnNumber: 7\n        }, this);\n    }\n    constructor(data){\n        super({\n            type: 'knob',\n            name: '旋钮',\n            icon: '🎛️',\n            color: 'blue',\n            description: '一个可以调节数值的旋钮',\n            properties: {\n                value: 50,\n                outputRange: 'percentage',\n                dynamicSize: false\n            },\n            size: {\n                width: 160,\n                height: 200\n            },\n            ...data\n        });\n        this.initializePins();\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/nodes/controls/KnobNode.tsx\n"));

/***/ })

});